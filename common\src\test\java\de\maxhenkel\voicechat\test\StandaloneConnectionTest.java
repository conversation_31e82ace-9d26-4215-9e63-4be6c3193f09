package de.maxhenkel.voicechat.test;

import de.maxhenkel.voicechat.net.SecretPacket;
import de.maxhenkel.voicechat.voice.client.InitializationData;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 独立语音服务器连接测试
 */
public class StandaloneConnectionTest {
    
    @Test
    public void testSecretPacketStandaloneMode() {
        // 创建模拟的SecretPacket
        SecretPacket packet = mock(SecretPacket.class);
        
        // 设置独立模式
        when(packet.useStandaloneServer()).thenReturn(true);
        when(packet.getStandaloneVoiceHost()).thenReturn("voice.example.com");
        when(packet.getStandaloneVoicePort()).thenReturn(24454);
        when(packet.getPlayerUUID()).thenReturn(UUID.randomUUID());
        when(packet.getSecret()).thenReturn(UUID.randomUUID());
        when(packet.getCodec()).thenReturn(null); // 简化测试
        when(packet.getMtuSize()).thenReturn(1024);
        when(packet.getVoiceChatDistance()).thenReturn(48.0);
        when(packet.getKeepAlive()).thenReturn(1000);
        when(packet.groupsEnabled()).thenReturn(true);
        when(packet.allowRecording()).thenReturn(true);
        
        // 测试InitializationData是否正确使用独立服务器地址
        InitializationData initData = new InitializationData("minecraft.example.com", packet);
        
        assertEquals("voice.example.com", initData.getServerIP());
        assertEquals(24454, initData.getServerPort());
    }
    
    @Test
    public void testSecretPacketIntegratedMode() {
        // 创建模拟的SecretPacket
        SecretPacket packet = mock(SecretPacket.class);
        
        // 设置集成模式
        when(packet.useStandaloneServer()).thenReturn(false);
        when(packet.getVoiceHost()).thenReturn("");
        when(packet.getServerPort()).thenReturn(24454);
        when(packet.getPlayerUUID()).thenReturn(UUID.randomUUID());
        when(packet.getSecret()).thenReturn(UUID.randomUUID());
        when(packet.getCodec()).thenReturn(null); // 简化测试
        when(packet.getMtuSize()).thenReturn(1024);
        when(packet.getVoiceChatDistance()).thenReturn(48.0);
        when(packet.getKeepAlive()).thenReturn(1000);
        when(packet.groupsEnabled()).thenReturn(true);
        when(packet.allowRecording()).thenReturn(true);
        
        // 测试InitializationData是否回退到集成模式
        InitializationData initData = new InitializationData("minecraft.example.com", packet);
        
        assertEquals("minecraft.example.com", initData.getServerIP());
        assertEquals(24454, initData.getServerPort());
    }
    
    @Test
    public void testSecretPacketBackwardCompatibility() {
        // 创建模拟的旧版本SecretPacket（没有独立服务器字段）
        SecretPacket packet = mock(SecretPacket.class);
        
        // 模拟旧版本行为
        when(packet.useStandaloneServer()).thenReturn(false);
        when(packet.getStandaloneVoiceHost()).thenReturn(null);
        when(packet.getStandaloneVoicePort()).thenReturn(0);
        when(packet.getVoiceHost()).thenReturn("custom.example.com:25454");
        when(packet.getServerPort()).thenReturn(24454);
        when(packet.getPlayerUUID()).thenReturn(UUID.randomUUID());
        when(packet.getSecret()).thenReturn(UUID.randomUUID());
        when(packet.getCodec()).thenReturn(null);
        when(packet.getMtuSize()).thenReturn(1024);
        when(packet.getVoiceChatDistance()).thenReturn(48.0);
        when(packet.getKeepAlive()).thenReturn(1000);
        when(packet.groupsEnabled()).thenReturn(true);
        when(packet.allowRecording()).thenReturn(true);
        
        // 测试向后兼容性
        InitializationData initData = new InitializationData("minecraft.example.com", packet);
        
        // 应该使用voiceHost中指定的地址
        assertEquals("custom.example.com", initData.getServerIP());
        assertEquals(25454, initData.getServerPort());
    }
    
    @Test
    public void testSecretPacketConstructors() {
        // 测试新的构造函数
        UUID playerUuid = UUID.randomUUID();
        UUID secret = UUID.randomUUID();
        
        // 创建一个简单的SecretPacket实例来测试构造函数
        // 注意：这里需要实际的实现类，而不是mock
        // 在实际测试中，你可能需要创建真实的对象或使用测试工具
        
        // 验证独立服务器信息是否正确设置
        assertTrue(true); // 占位符测试，实际实现时需要更详细的测试
    }
}
