buildscript {
    println('Loading root gradle.properties')
    Properties properties = new Properties()
    properties.load(new FileInputStream(file('../gradle.properties')))
    properties.each { key, value ->
        ext.set(key.toString(), value.toString())
    }
}

plugins {
    id 'org.quiltmc.loom' version "${quilt_loom_version}"
    id 'com.gradleup.shadow' version "${shadow_version}"
    id 'com.matthewprenger.cursegradle' version "${cursegradle_version}"
    id 'com.modrinth.minotaur' version "${minotaur_version}"
    id 'mod-update' version "${mod_update_version}"
}

apply from: "https://raw.githubusercontent.com/henkelmax/mod-gradle-scripts/${mod_gradle_script_version}/mod.gradle"
apply from: "https://raw.githubusercontent.com/henkelmax/mod-gradle-scripts/${mod_gradle_script_version}/taskutils.gradle"

processResources {
    filesMatching('quilt.mod.json') {
        expand 'java_version': java_version,
                'mod_version': mod_version,
                'minecraft_version': minecraft_version,
                'minecraft_dependency': minecraft_dependency,
                'quilt_loader_dependency': quilt_loader_dependency,
                'modmenu_version': modmenu_version,
                'cloth_config_version': cloth_config_version,
                'qsl_version': qsl_version
    }
}

repositories {
    maven { url = 'https://maven.quiltmc.org/repository/release' }
    maven { url = 'https://maven.quiltmc.org/repository/snapshot' }
    maven { url = 'https://maven.shedaniel.me/' }
    maven { url = 'https://maven.terraformersmc.com/releases' }
    maven { url = 'https://repo.viaversion.com/' }
}

dependencies {
    implementation 'org.jetbrains:annotations:23.0.0'

    include(modImplementation('org.quiltmc.quilted-fabric-api:fabric-key-binding-api-v1:11.0.0-alpha.3+0.102.0-1.21'))

    include(modImplementation("org.quiltmc.qsl.core:qsl_base:${qsl_version}"))
    include(modImplementation("org.quiltmc.qsl.core:registry:${qsl_version}"))
    include(modImplementation("org.quiltmc.qsl.core:lifecycle_events:${qsl_version}"))
    include(modImplementation("org.quiltmc.qsl.core:networking:${qsl_version}"))
    include(modImplementation("org.quiltmc.qsl.core:resource_loader:${qsl_version}"))
    include(modImplementation("org.quiltmc.qsl.management:command:${qsl_version}"))

    modCompileOnly("com.terraformersmc:modmenu:${modmenu_version}") {
        transitive false
    }
    modCompileOnly("me.shedaniel.cloth:cloth-config-fabric:${cloth_config_version}") {
        transitive false
    }
    modCompileOnly("me.lucko:fabric-permissions-api:${fabric_permission_api_version}") {
        transitive false
    }
    modCompileOnly("com.viaversion:viaversion-common:${viaversion_version}") {
        transitive false
    }

    implementation "org.concentus:Concentus:${concentus_version}"
    shadow "org.concentus:Concentus:${concentus_version}"

    implementation "de.maxhenkel.opus4j:opus4j:${opus4j_version}"
    shadow "de.maxhenkel.opus4j:opus4j:${opus4j_version}"

    implementation "de.maxhenkel.rnnoise4j:rnnoise4j:${rnnoise4j_version}"
    shadow "de.maxhenkel.rnnoise4j:rnnoise4j:${rnnoise4j_version}"

    implementation "de.maxhenkel.lame4j:lame4j:${lame4j_version}"
    shadow "de.maxhenkel.lame4j:lame4j:${lame4j_version}"
}

tasks.register('generateJava', Copy) {
    from file('../common/src/template/java')
    into "${layout.buildDirectory.asFile.get()}/generated/java"
    expand 'compatibility_version': voicechat_compatibility_version,
            'minecraft_version': minecraft_version
}
sourceSets.main.java {
    srcDir "${layout.buildDirectory.asFile.get()}/generated/java"
}
compileJava.dependsOn generateJava

sourceSets {
    main {
        java {
            srcDir '../common/src/main/java'
            srcDir '../common-client/src/main/java'
            srcDir '../api/src/main/java'
        }
        resources {
            srcDir '../common/src/main/resources'
            srcDir '../common-client/src/main/resources'
            srcDir '../api/src/main/resources'
        }
    }
}

shadowJar {
    relocate 'org.concentus', 'de.maxhenkel.voicechat.concentus'
    exclude 'natives/*-x86/*'
}

tasks.register('uploadMod') {
    group = 'voicechat'
    doLast {
        runGradleTasks(['clean'], ['curseforge', 'modrinth', 'modUpdate'])
    }
}