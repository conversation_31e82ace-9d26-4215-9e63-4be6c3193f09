# Voice Chat Adapter Configuration

# 语音服务器配置
voice-server:
  # 语音服务器地址
  host: "localhost"
  # 语音服务器UDP端口
  port: 24454
  # API端点地址
  api-endpoint: "http://localhost:8080"
  # 认证令牌（必须与语音服务器配置一致）
  auth-token: "change-this-secret-token"

# 同步配置
sync:
  # 位置同步间隔（毫秒），0表示禁用定时同步
  position-interval: 1000
  # 权限同步间隔（毫秒），0表示禁用定时同步
  permission-interval: 5000
  # 是否在玩家移动时立即同步位置
  sync-on-move: true
  # 最小移动距离（方块），小于此距离的移动不会触发同步
  min-move-distance: 1.0

# 服务器名称（用于区分不同的Minecraft服务器）
server-name: "default"
