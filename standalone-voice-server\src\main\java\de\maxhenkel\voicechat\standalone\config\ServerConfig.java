package de.maxhenkel.voicechat.standalone.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.maxhenkel.voicechat.standalone.audio.OpusCodec;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 独立语音服务器配置类
 */
public class ServerConfig {
    
    @JsonProperty("server")
    private ServerSettings server = new ServerSettings();
    
    @JsonProperty("api")
    private ApiSettings api = new ApiSettings();

    @JsonProperty("voice")
    private VoiceSettings voice = new VoiceSettings();
    
    @JsonProperty("security")
    private SecuritySettings security = new SecuritySettings();

    @JsonProperty("cross-server")
    private CrossServerSettings crossServer = new CrossServerSettings();

    @JsonProperty("cross-world")
    private CrossWorldSettings crossWorld = new CrossWorldSettings();

    @JsonProperty("permissions")
    private PermissionSettings permissions = new PermissionSettings();

    @JsonProperty("server-management")
    private ServerManagementSettings serverManagement = new ServerManagementSettings();

    @JsonProperty("monitoring")
    private MonitoringSettings monitoring = new MonitoringSettings();

    // Getters
    public ServerSettings getServer() { return server; }
    public ApiSettings getApi() { return api; }
    public VoiceSettings getVoice() { return voice; }
    public SecuritySettings getSecurity() { return security; }
    public CrossServerSettings getCrossServer() { return crossServer; }
    public CrossWorldSettings getCrossWorld() { return crossWorld; }
    public PermissionSettings getPermissions() { return permissions; }
    public ServerManagementSettings getServerManagement() { return serverManagement; }
    public MonitoringSettings getMonitoring() { return monitoring; }
    
    public static class ServerSettings {
        @JsonProperty("host")
        private String host = "0.0.0.0";
        
        @JsonProperty("port")
        private int port = 24454;
        
        @JsonProperty("bind_address")
        private String bindAddress = "";
        
        // Getters
        public String getHost() { return host; }
        public int getPort() { return port; }
        public String getBindAddress() { return bindAddress; }
    }
    
    public static class ApiSettings {
        @JsonProperty("host")
        private String host = "0.0.0.0";
        
        @JsonProperty("port")
        private int port = 8080;
        
        @JsonProperty("auth_token")
        private String authToken = "change-this-secret-token";
        
        // Getters
        public String getHost() { return host; }
        public int getPort() { return port; }
        public String getAuthToken() { return authToken; }
    }
    

    
    public static class VoiceSettings {
        @JsonProperty("codec")
        private String codec = "VOIP";

        @JsonProperty("mtu_size")
        private int mtuSize = 1024;

        @JsonProperty("keep_alive")
        private int keepAlive = 1000;

        @JsonProperty("max_distance")
        private double maxDistance = 48.0;

        @JsonProperty("whisper_distance_multiplier")
        private double whisperDistanceMultiplier = 0.5;

        @JsonProperty("groups_enabled")
        private boolean groupsEnabled = true;

        @JsonProperty("allow_recording")
        private boolean allowRecording = true;

        @JsonProperty("bitrate")
        private int bitrate = 24000; // 默认24kbps

        @JsonProperty("complexity")
        private int complexity = 5; // 默认复杂度5

        @JsonProperty("dtx_enabled")
        private boolean dtxEnabled = true; // 默认启用DTX

        @JsonProperty("fec_enabled")
        private boolean fecEnabled = true; // 默认启用FEC
        
        // Getters
        public String getCodec() { return codec; }
        public int getMtuSize() { return mtuSize; }
        public int getKeepAlive() { return keepAlive; }
        public double getMaxDistance() { return maxDistance; }
        public double getWhisperDistanceMultiplier() { return whisperDistanceMultiplier; }
        public double getWhisperDistance() { return maxDistance * whisperDistanceMultiplier; }
        public boolean isGroupsEnabled() { return groupsEnabled; }
        public boolean isAllowRecording() { return allowRecording; }
        public int getBitrate() { return bitrate; }
        public int getComplexity() { return complexity; }
        public boolean isDtxEnabled() { return dtxEnabled; }
        public boolean isFecEnabled() { return fecEnabled; }

        // Setters
        public void setCodec(String codec) { this.codec = codec; }
        public void setMtuSize(int mtuSize) { this.mtuSize = mtuSize; }
        public void setKeepAlive(int keepAlive) { this.keepAlive = keepAlive; }
        public void setMaxDistance(double maxDistance) { this.maxDistance = maxDistance; }
        public void setWhisperDistanceMultiplier(double whisperDistanceMultiplier) { this.whisperDistanceMultiplier = whisperDistanceMultiplier; }
        public void setGroupsEnabled(boolean groupsEnabled) { this.groupsEnabled = groupsEnabled; }
        public void setAllowRecording(boolean allowRecording) { this.allowRecording = allowRecording; }
        public void setBitrate(int bitrate) { this.bitrate = bitrate; }
        public void setComplexity(int complexity) { this.complexity = complexity; }
        public void setDtxEnabled(boolean dtxEnabled) { this.dtxEnabled = dtxEnabled; }
        public void setFecEnabled(boolean fecEnabled) { this.fecEnabled = fecEnabled; }

        /**
         * 获取Opus编解码器枚举
         */
        public OpusCodec getOpusCodec() {
            return OpusCodec.fromString(codec);
        }

        /**
         * 设置Opus编解码器
         */
        public void setOpusCodec(OpusCodec opusCodec) {
            this.codec = opusCodec != null ? opusCodec.getName() : "VOIP";
        }
    }
    
    public static class SecuritySettings {
        @JsonProperty("encryption_enabled")
        private boolean encryptionEnabled = true;
        
        @JsonProperty("auth_timeout")
        private int authTimeout = 30000;
        
        // Getters
        public boolean isEncryptionEnabled() { return encryptionEnabled; }
        public int getAuthTimeout() { return authTimeout; }
    }

    public static class CrossServerSettings {
        @JsonProperty("auto_enable")
        private boolean autoEnable = true;

        @JsonProperty("default_allowed")
        private boolean defaultAllowed = true;

        @JsonProperty("registration_timeout")
        private int registrationTimeout = 30000;

        @JsonProperty("keepalive_timeout")
        private long keepaliveTimeout = 1800000; // 30分钟

        @JsonProperty("cleanup_interval")
        private long cleanupInterval = 1800000; // 30分钟

        // Getters
        public boolean isAutoEnable() { return autoEnable; }
        public boolean isDefaultAllowed() { return defaultAllowed; }
        public int getRegistrationTimeout() { return registrationTimeout; }
        public long getKeepaliveTimeout() { return keepaliveTimeout; }
        public long getCleanupInterval() { return cleanupInterval; }
    }

    public static class CrossWorldSettings {
        @JsonProperty("default_allowed")
        private boolean defaultAllowed = true;

        @JsonProperty("config_timeout")
        private int configTimeout = 30000;

        // Getters
        public boolean isDefaultAllowed() { return defaultAllowed; }
        public int getConfigTimeout() { return configTimeout; }
    }

    public static class PermissionSettings {
        @JsonProperty("defaults")
        private DefaultPermissions defaults = new DefaultPermissions();

        @JsonProperty("groups")
        private java.util.Map<String, List<String>> groups = new java.util.HashMap<>();

        // Getters
        public DefaultPermissions getDefaults() { return defaults; }
        public java.util.Map<String, List<String>> getGroups() { return groups; }

        public static class DefaultPermissions {
            @JsonProperty("listen")
            private boolean listen = true;

            @JsonProperty("speak")
            private boolean speak = true;

            @JsonProperty("groups")
            private boolean groups = true;

            @JsonProperty("create_group")
            private boolean createGroup = true;

            @JsonProperty("join_group")
            private boolean joinGroup = true;

            @JsonProperty("cross_world")
            private boolean crossWorld = true;

            @JsonProperty("cross_server")
            private boolean crossServer = true;

            @JsonProperty("manage_group")
            private boolean manageGroup = false;

            @JsonProperty("admin")
            private boolean admin = false;

            @JsonProperty("record")
            private boolean record = false;

            @JsonProperty("spectator_interaction")
            private boolean spectatorInteraction = false;

            @JsonProperty("spectator_possession")
            private boolean spectatorPossession = false;

            // Getters
            public boolean isListen() { return listen; }
            public boolean isSpeak() { return speak; }
            public boolean isGroups() { return groups; }
            public boolean isCreateGroup() { return createGroup; }
            public boolean isJoinGroup() { return joinGroup; }
            public boolean isCrossWorld() { return crossWorld; }
            public boolean isCrossServer() { return crossServer; }
            public boolean isManageGroup() { return manageGroup; }
            public boolean isAdmin() { return admin; }
            public boolean isRecord() { return record; }
            public boolean isSpectatorInteraction() { return spectatorInteraction; }
            public boolean isSpectatorPossession() { return spectatorPossession; }
        }
    }

    public static class ServerManagementSettings {
        @JsonProperty("auto_registration")
        private boolean autoRegistration = true;

        @JsonProperty("auto_configure_cross_server")
        private boolean autoConfigureCrossServer = true;

        @JsonProperty("name_pattern")
        private String namePattern = "^[a-zA-Z0-9_-]{1,32}$";

        @JsonProperty("allow_duplicate_names")
        private boolean allowDuplicateNames = false;

        // Getters
        public boolean isAutoRegistration() { return autoRegistration; }
        public boolean isAutoConfigureCrossServer() { return autoConfigureCrossServer; }
        public String getNamePattern() { return namePattern; }
        public boolean isAllowDuplicateNames() { return allowDuplicateNames; }
    }

    public static class MonitoringSettings {
        @JsonProperty("verbose_logging")
        private boolean verboseLogging = false;

        @JsonProperty("log_voice_stats")
        private boolean logVoiceStats = true;

        @JsonProperty("stats_interval")
        private long statsInterval = 300000; // 5分钟

        @JsonProperty("performance_monitoring")
        private boolean performanceMonitoring = true;

        // Getters
        public boolean isVerboseLogging() { return verboseLogging; }
        public boolean isLogVoiceStats() { return logVoiceStats; }
        public long getStatsInterval() { return statsInterval; }
        public boolean isPerformanceMonitoring() { return performanceMonitoring; }
    }
    
    /**
     * 从文件加载配置
     */
    public static ServerConfig load(File file) throws IOException {
        ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
        return mapper.readValue(file, ServerConfig.class);
    }
    
    /**
     * 保存配置到文件
     */
    public void save(File file) throws IOException {
        ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
        mapper.writeValue(file, this);
    }
    
    /**
     * 创建默认配置文件
     */
    public static void createDefaultConfig(File file) throws IOException {
        ServerConfig defaultConfig = new ServerConfig();
        defaultConfig.save(file);
    }
}
