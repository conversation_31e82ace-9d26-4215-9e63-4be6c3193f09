package de.maxhenkel.voicechat.standalone.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.maxhenkel.voicechat.standalone.audio.OpusCodec;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 独立语音服务器配置类
 */
public class ServerConfig {
    
    @JsonProperty("server")
    private ServerSettings server = new ServerSettings();
    
    @JsonProperty("api")
    private ApiSettings api = new ApiSettings();

    @JsonProperty("voice")
    private VoiceSettings voice = new VoiceSettings();
    
    @JsonProperty("security")
    private SecuritySettings security = new SecuritySettings();
    
    // Getters
    public ServerSettings getServer() { return server; }
    public ApiSettings getApi() { return api; }
    public VoiceSettings getVoice() { return voice; }
    public SecuritySettings getSecurity() { return security; }
    
    public static class ServerSettings {
        @JsonProperty("host")
        private String host = "0.0.0.0";
        
        @JsonProperty("port")
        private int port = 24454;
        
        @JsonProperty("bind_address")
        private String bindAddress = "";
        
        // Getters
        public String getHost() { return host; }
        public int getPort() { return port; }
        public String getBindAddress() { return bindAddress; }
    }
    
    public static class ApiSettings {
        @JsonProperty("host")
        private String host = "0.0.0.0";
        
        @JsonProperty("port")
        private int port = 8080;
        
        @JsonProperty("auth_token")
        private String authToken = "change-this-secret-token";
        
        // Getters
        public String getHost() { return host; }
        public int getPort() { return port; }
        public String getAuthToken() { return authToken; }
    }
    

    
    public static class VoiceSettings {
        @JsonProperty("codec")
        private String codec = "VOIP";

        @JsonProperty("mtu_size")
        private int mtuSize = 1024;

        @JsonProperty("keep_alive")
        private int keepAlive = 1000;

        @JsonProperty("max_distance")
        private double maxDistance = 48.0;

        @JsonProperty("whisper_distance_multiplier")
        private double whisperDistanceMultiplier = 0.5;

        @JsonProperty("groups_enabled")
        private boolean groupsEnabled = true;

        @JsonProperty("allow_recording")
        private boolean allowRecording = true;

        @JsonProperty("bitrate")
        private int bitrate = 24000; // 默认24kbps

        @JsonProperty("complexity")
        private int complexity = 5; // 默认复杂度5

        @JsonProperty("dtx_enabled")
        private boolean dtxEnabled = true; // 默认启用DTX

        @JsonProperty("fec_enabled")
        private boolean fecEnabled = true; // 默认启用FEC
        
        // Getters
        public String getCodec() { return codec; }
        public int getMtuSize() { return mtuSize; }
        public int getKeepAlive() { return keepAlive; }
        public double getMaxDistance() { return maxDistance; }
        public double getWhisperDistanceMultiplier() { return whisperDistanceMultiplier; }
        public double getWhisperDistance() { return maxDistance * whisperDistanceMultiplier; }
        public boolean isGroupsEnabled() { return groupsEnabled; }
        public boolean isAllowRecording() { return allowRecording; }
        public int getBitrate() { return bitrate; }
        public int getComplexity() { return complexity; }
        public boolean isDtxEnabled() { return dtxEnabled; }
        public boolean isFecEnabled() { return fecEnabled; }

        // Setters
        public void setCodec(String codec) { this.codec = codec; }
        public void setMtuSize(int mtuSize) { this.mtuSize = mtuSize; }
        public void setKeepAlive(int keepAlive) { this.keepAlive = keepAlive; }
        public void setMaxDistance(double maxDistance) { this.maxDistance = maxDistance; }
        public void setWhisperDistanceMultiplier(double whisperDistanceMultiplier) { this.whisperDistanceMultiplier = whisperDistanceMultiplier; }
        public void setGroupsEnabled(boolean groupsEnabled) { this.groupsEnabled = groupsEnabled; }
        public void setAllowRecording(boolean allowRecording) { this.allowRecording = allowRecording; }
        public void setBitrate(int bitrate) { this.bitrate = bitrate; }
        public void setComplexity(int complexity) { this.complexity = complexity; }
        public void setDtxEnabled(boolean dtxEnabled) { this.dtxEnabled = dtxEnabled; }
        public void setFecEnabled(boolean fecEnabled) { this.fecEnabled = fecEnabled; }

        /**
         * 获取Opus编解码器枚举
         */
        public OpusCodec getOpusCodec() {
            return OpusCodec.fromString(codec);
        }

        /**
         * 设置Opus编解码器
         */
        public void setOpusCodec(OpusCodec opusCodec) {
            this.codec = opusCodec != null ? opusCodec.getName() : "VOIP";
        }
    }
    
    public static class SecuritySettings {
        @JsonProperty("encryption_enabled")
        private boolean encryptionEnabled = true;
        
        @JsonProperty("auth_timeout")
        private int authTimeout = 30000;
        
        // Getters
        public boolean isEncryptionEnabled() { return encryptionEnabled; }
        public int getAuthTimeout() { return authTimeout; }
    }
    
    /**
     * 从文件加载配置
     */
    public static ServerConfig load(File file) throws IOException {
        ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
        return mapper.readValue(file, ServerConfig.class);
    }
    
    /**
     * 保存配置到文件
     */
    public void save(File file) throws IOException {
        ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
        mapper.writeValue(file, this);
    }
    
    /**
     * 创建默认配置文件
     */
    public static void createDefaultConfig(File file) throws IOException {
        ServerConfig defaultConfig = new ServerConfig();
        defaultConfig.save(file);
    }
}
