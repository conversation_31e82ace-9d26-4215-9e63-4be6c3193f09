name: voicechat
version: ${version}
main: de.maxhenkel.voicechat.Voicechat
api-version: 1.13
prefix: voicechat
authors: [ "<PERSON>", "<PERSON>" ]
description: A working voice chat in Minecraft
website: https://modrepo.de/minecraft/voicechat/overview
folia-supported: true
commands:
  voicechat:
    description: Manage voice chat
    usage: Invalid command syntax
permissions:
  voicechat.listen:
    description: Allows to listen to voice chat
  voicechat.speak:
    description: Allows to speak in voice chat
  voicechat.groups:
    description: Allows to join groups
  voicechat.admin:
    description: Allows to test voice chat connections
softdepend: [PlaceholderAPI, ViaVersion]