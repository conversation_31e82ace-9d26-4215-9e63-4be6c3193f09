# 独立语音服务器配置文件

# 语音服务器配置
server:
  # 绑定地址，0.0.0.0 表示绑定所有网络接口
  host: "0.0.0.0"
  # UDP端口，用于语音数据传输
  port: 24454
  # 特定绑定地址，留空使用上面的host
  bind_address: ""

# API服务器配置
api:
  # API服务器地址
  host: "0.0.0.0"
  # API服务器端口
  port: 8080
  # 认证令牌，Minecraft服务器需要使用此令牌访问API
  auth_token: "change-this-secret-token"

# 语音配置
voice:
  # 音频编解码器：VOIP, AUDIO, RESTRICTED_LOWDELAY
  # VOIP: 针对语音通话优化，提供最佳的语音质量和低延迟
  # AUDIO: 针对音频内容优化，适用于音乐和高质量音频内容
  # RESTRICTED_LOWDELAY: 受限低延迟模式，提供最低延迟但可能牺牲音质
  codec: "VOIP"
  # 最大传输单元大小（字节）
  mtu_size: 1024
  # 心跳间隔（毫秒）
  keep_alive: 1000
  # 最大语音距离
  max_distance: 48.0
  # 耳语距离倍数（相对于最大距离）
  whisper_distance_multiplier: 0.5
  # 是否启用群组聊天
  groups_enabled: true
  # 是否允许录音
  allow_recording: true

  # Opus编解码器高级设置
  # 比特率（bps）- 控制音频质量和带宽使用
  # VOIP推荐: 24000, AUDIO推荐: 64000, RESTRICTED_LOWDELAY推荐: 16000
  bitrate: 24000
  # 复杂度（0-10）- 控制编码质量和CPU使用
  # 更高的复杂度提供更好的音质但消耗更多CPU
  complexity: 5
  # DTX（不连续传输）- 在静音时停止传输以节省带宽
  dtx_enabled: true
  # FEC（前向纠错）- 提高网络丢包时的音频质量
  fec_enabled: true

# 安全配置
security:
  # 是否启用加密
  encryption_enabled: true
  # 认证超时时间（毫秒）
  auth_timeout: 30000
