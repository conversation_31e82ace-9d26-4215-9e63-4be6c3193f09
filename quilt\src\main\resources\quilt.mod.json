{"schema_version": 1, "quilt_loader": {"group": "de.maxhenkel.voicechat", "id": "voicechat", "version": "${mod_version}", "intermediate_mappings": "net.fabricmc:intermediary", "metadata": {"name": "Simple Voice Chat", "description": "A working voice chat in Minecraft!", "contributors": {"Max Henkel": "Owner"}, "contact": {"homepage": "https://modrepo.de/minecraft/voicechat/overview", "issues": "https://github.com/henkelmax/simple-voice-chat/issues", "sources": "https://github.com/henkelmax/simple-voice-chat"}, "license": {"name": "All Rights Reserved", "id": "ARR", "url": "https://github.com/henkelmax/simple-voice-chat/blob/1.20.1/license", "description": "All Rights Reserved"}, "icon": "icon.png"}, "entrypoints": {"client_init": ["de.maxhenkel.voicechat.QuiltVoicechatClientMod"], "init": ["de.maxhenkel.voicechat.QuiltVoicechatMod"], "modmenu": ["de.maxhenkel.voicechat.integration.ModMenu"]}, "depends": [{"id": "quilt_loader", "versions": "${quilt_loader_dependency}"}, {"id": "minecraft", "versions": "${minecraft_dependency}"}, {"id": "java", "versions": ">=${java_version}"}, {"id": "quilt_base", "versions": ">=${qsl_version}"}, {"id": "quilt_lifecycle_events", "versions": ">=${qsl_version}"}, {"id": "quilt_resource_loader", "versions": ">=${qsl_version}"}, {"id": "quilt_networking", "versions": ">=${qsl_version}"}, {"id": "quilt_command", "versions": ">=${qsl_version}"}, {"id": "quilted_fabric_key_binding_api_v1", "versions": "*"}]}, "mixin": ["voicechat.mixins.json"], "modmenu": {"links": {"modmenu.discord": "https://discord.gg/4dH2zwTmyX"}}}