# 独立语音服务器架构设计

## 概述

本文档描述了如何将 Simple Voice Chat 的语音服务器从 Minecraft 服务器中独立出来的架构设计。

## 当前架构分析

### 核心组件
1. **Server** - UDP语音服务器主类
2. **PlayerStateManager** - 玩家状态管理
3. **ServerGroupManager** - 群组管理
4. **PingManager** - 连接管理和心跳
5. **ClientConnection** - 客户端连接抽象
6. **NetworkMessage** - 网络消息处理
7. **各种Packet类** - 语音数据包

### 主要依赖
- MinecraftServer - 获取玩家信息、权限检查
- ServerPlayer - 玩家对象和位置信息
- 权限系统 - 语音聊天权限验证
- 世界坐标系统 - 近距离语音计算

## 独立架构设计

### 1. 架构概览

```
┌─────────────────┐    HTTP/WebSocket    ┌──────────────────────┐
│  Minecraft      │ ◄─────────────────► │  独立语音服务器        │
│  Server         │                     │  (Standalone Voice   │
│  (Plugin/Mod)   │                     │   Server)            │
└─────────────────┘                     └──────────────────────┘
         │                                        │
         │ TCP (Player Data)                      │ UDP (Voice Data)
         │                                        │
         ▼                                        ▼
┌─────────────────┐                     ┌──────────────────────┐
│  Game Clients   │ ◄─────────────────► │  Voice Clients       │
│                 │      UDP Voice      │                      │
└─────────────────┘                     └──────────────────────┘
```

### 2. 独立语音服务器组件

#### 2.1 核心服务器 (StandaloneVoiceServer)
- 独立的Java应用程序
- 处理UDP语音数据包
- 管理客户端连接
- 提供HTTP API接口

#### 2.2 玩家数据适配器 (PlayerDataAdapter)
- 从Minecraft服务器获取玩家信息
- 缓存玩家位置、权限等数据
- 提供统一的玩家数据接口

#### 2.3 通信桥梁 (CommunicationBridge)
- HTTP/WebSocket API服务器
- 处理来自Minecraft服务器的请求
- 实时同步玩家状态

### 3. API接口设计

#### 3.1 玩家管理API
```http
POST /api/players/login
POST /api/players/logout
PUT /api/players/{uuid}/position
PUT /api/players/{uuid}/permissions
GET /api/players/{uuid}/state
```

#### 3.2 群组管理API
```http
POST /api/groups
DELETE /api/groups/{groupId}
POST /api/groups/{groupId}/members
DELETE /api/groups/{groupId}/members/{playerId}
```

#### 3.3 配置API
```http
GET /api/config
PUT /api/config
```

### 4. 数据模型

#### 4.1 玩家数据 (PlayerData)
```java
public class PlayerData {
    private UUID uuid;
    private String name;
    private Position position;
    private World world;
    private Set<Permission> permissions;
    private boolean online;
    private long lastUpdate;
}
```

#### 4.2 位置信息 (Position)
```java
public class Position {
    private String worldId;
    private double x, y, z;
    private long timestamp;
}
```

#### 4.3 权限信息 (Permission)
```java
public enum Permission {
    SPEAK,
    GROUPS,
    ADMIN
}
```

### 5. 通信协议

#### 5.1 Minecraft服务器 → 语音服务器
- **玩家登录**: 发送玩家基本信息和权限
- **位置更新**: 定期发送玩家位置信息
- **权限变更**: 实时更新玩家权限
- **玩家登出**: 通知玩家离线

#### 5.2 语音服务器 → Minecraft服务器
- **连接状态**: 报告玩家语音连接状态
- **群组变更**: 通知群组成员变化
- **错误报告**: 发送错误和警告信息

### 6. 配置文件设计

#### 6.1 独立语音服务器配置 (voice-server.yml)
```yaml
server:
  host: "0.0.0.0"
  port: 24454
  bind_address: ""

api:
  host: "0.0.0.0"
  port: 8080
  auth_token: "your-secret-token"

# 注意：独立服务器采用被动连接模式
# Minecraft服务器通过适配器主动连接到独立服务器
# 不需要在独立服务器中配置Minecraft服务器信息

voice:
  codec: "VOIP"
  mtu_size: 1024
  keep_alive: 1000
  max_distance: 48.0
  groups_enabled: true
  allow_recording: true

security:
  encryption_enabled: true
  auth_timeout: 30000
```

#### 6.2 Minecraft服务器配置更新
```yaml
voicechat:
  mode: "standalone"  # 新增：standalone 或 integrated
  standalone:
    voice_server_host: "voice.example.com"
    voice_server_port: 24454
    api_endpoint: "http://voice.example.com:8080"
    auth_token: "your-secret-token"
    sync_interval: 1000  # 位置同步间隔(ms)
```

### 7. 部署架构

#### 7.1 单服务器部署
```
┌─────────────────────────────────────┐
│  物理服务器                          │
│  ┌─────────────────┐                │
│  │ Minecraft Server│                │
│  │ Port: 25565     │                │
│  └─────────────────┘                │
│  ┌─────────────────┐                │
│  │ Voice Server    │                │
│  │ UDP: 24454      │                │
│  │ HTTP: 8080      │                │
│  └─────────────────┘                │
└─────────────────────────────────────┘
```

#### 7.2 分布式部署
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Minecraft       │    │ Minecraft       │    │ Voice Server    │
│ Server 1        │    │ Server 2        │    │                 │
│ Port: 25565     │    │ Port: 25565     │    │ UDP: 24454      │
│                 │    │                 │    │ HTTP: 8080      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Load Balancer   │
                    │ (Optional)      │
                    └─────────────────┘
```

### 8. 实现计划

1. **阶段1**: 创建独立语音服务器核心
2. **阶段2**: 实现API接口和通信桥梁
3. **阶段3**: 修改Minecraft服务器适配器
4. **阶段4**: 更新客户端连接逻辑
5. **阶段5**: 测试和优化

### 9. 优势

1. **性能隔离**: 语音处理不影响游戏性能
2. **横向扩展**: 多个游戏服务器共享语音服务
3. **独立维护**: 可以独立更新和重启
4. **资源优化**: 专门的语音服务器可以优化网络和CPU
5. **高可用**: 语音服务器故障不影响游戏服务器

### 10. 挑战和解决方案

#### 10.1 网络延迟
- **问题**: 增加了一层网络通信
- **解决**: 使用高效的序列化和缓存机制

#### 10.2 数据一致性
- **问题**: 玩家位置和状态同步
- **解决**: 实现增量更新和冲突解决机制

#### 10.3 故障恢复
- **问题**: 服务器间连接中断
- **解决**: 实现重连机制和状态恢复

#### 10.4 安全性
- **问题**: 跨服务器通信安全
- **解决**: 使用Token认证和HTTPS加密
