#!/bin/bash

# 独立语音服务器部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v java &> /dev/null; then
        log_error "Java 17+ 未安装"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | awk -F '.' '{print $1}')
    if [ "$JAVA_VERSION" -lt 17 ]; then
        log_error "需要 Java 17 或更高版本，当前版本: $JAVA_VERSION"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        log_warn "Docker 未安装，将使用本地部署模式"
        DOCKER_AVAILABLE=false
    else
        DOCKER_AVAILABLE=true
    fi
    
    log_info "依赖检查完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    # 构建独立语音服务器
    cd standalone-voice-server
    if [ -f "./gradlew" ]; then
        ./gradlew shadowJar
    else
        gradle shadowJar
    fi
    cd ..
    
    # 构建Minecraft适配器
    cd minecraft-adapter
    if [ -f "./gradlew" ]; then
        ./gradlew shadowJar
    else
        gradle shadowJar
    fi
    cd ..
    
    log_info "项目构建完成"
}

# 本地部署
deploy_local() {
    log_info "开始本地部署..."
    
    # 创建部署目录
    DEPLOY_DIR="./deployment"
    mkdir -p "$DEPLOY_DIR"/{voice-server,minecraft-adapter,logs,data}
    
    # 复制文件
    cp standalone-voice-server/build/libs/standalone-voice-server-1.0.0.jar "$DEPLOY_DIR/voice-server/"
    cp standalone-voice-server/voice-server.yml "$DEPLOY_DIR/voice-server/"
    cp standalone-voice-server/start.sh "$DEPLOY_DIR/voice-server/"
    cp standalone-voice-server/start.bat "$DEPLOY_DIR/voice-server/"
    
    cp minecraft-adapter/build/libs/minecraft-adapter-1.0.0.jar "$DEPLOY_DIR/minecraft-adapter/"
    
    # 设置权限
    chmod +x "$DEPLOY_DIR/voice-server/start.sh"
    
    log_info "本地部署完成，文件位于: $DEPLOY_DIR"
    log_info "启动语音服务器: cd $DEPLOY_DIR/voice-server && ./start.sh"
}

# Docker部署
deploy_docker() {
    log_info "开始Docker部署..."
    
    # 构建Docker镜像
    docker build -t voice-server:latest ./standalone-voice-server
    
    # 启动服务
    docker-compose up -d
    
    log_info "Docker部署完成"
    log_info "检查服务状态: docker-compose ps"
    log_info "查看日志: docker-compose logs -f voice-server"
}

# 生成配置文件
generate_config() {
    log_info "生成配置文件..."
    
    # 生成随机认证令牌
    AUTH_TOKEN=$(openssl rand -hex 32 2>/dev/null || echo "change-this-secret-token-$(date +%s)")
    
    # 更新语音服务器配置
    if [ -f "standalone-voice-server/voice-server.yml" ]; then
        sed -i.bak "s/change-this-secret-token/$AUTH_TOKEN/g" standalone-voice-server/voice-server.yml
        log_info "已更新语音服务器认证令牌"
    fi
    
    # 更新适配器配置
    if [ -f "minecraft-adapter/src/main/resources/config.yml" ]; then
        sed -i.bak "s/change-this-secret-token/$AUTH_TOKEN/g" minecraft-adapter/src/main/resources/config.yml
        log_info "已更新适配器认证令牌"
    fi
    
    log_info "配置文件生成完成，认证令牌: $AUTH_TOKEN"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    cd standalone-voice-server
    if [ -f "./gradlew" ]; then
        ./gradlew test
    else
        gradle test
    fi
    cd ..
    
    cd minecraft-adapter
    if [ -f "./gradlew" ]; then
        ./gradlew test
    else
        gradle test
    fi
    cd ..
    
    log_info "测试完成"
}

# 清理
clean() {
    log_info "清理构建文件..."
    
    cd standalone-voice-server
    if [ -f "./gradlew" ]; then
        ./gradlew clean
    else
        gradle clean
    fi
    cd ..
    
    cd minecraft-adapter
    if [ -f "./gradlew" ]; then
        ./gradlew clean
    else
        gradle clean
    fi
    cd ..
    
    # 清理Docker资源
    if [ "$DOCKER_AVAILABLE" = true ]; then
        docker-compose down --volumes --remove-orphans 2>/dev/null || true
        docker rmi voice-server:latest 2>/dev/null || true
    fi
    
    # 清理部署目录
    rm -rf ./deployment
    
    log_info "清理完成"
}

# 显示帮助
show_help() {
    echo "独立语音服务器部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build       构建项目"
    echo "  test        运行测试"
    echo "  config      生成配置文件"
    echo "  deploy      部署项目 (本地模式)"
    echo "  docker      Docker部署"
    echo "  clean       清理构建文件"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build          # 构建项目"
    echo "  $0 config deploy  # 生成配置并部署"
    echo "  $0 docker         # Docker部署"
}

# 主函数
main() {
    if [ $# -eq 0 ]; then
        show_help
        exit 0
    fi
    
    check_dependencies
    
    for arg in "$@"; do
        case $arg in
            build)
                build_project
                ;;
            test)
                run_tests
                ;;
            config)
                generate_config
                ;;
            deploy)
                deploy_local
                ;;
            docker)
                if [ "$DOCKER_AVAILABLE" = true ]; then
                    deploy_docker
                else
                    log_error "Docker 不可用，请使用本地部署模式"
                    exit 1
                fi
                ;;
            clean)
                clean
                ;;
            help)
                show_help
                ;;
            *)
                log_error "未知选项: $arg"
                show_help
                exit 1
                ;;
        esac
    done
}

# 执行主函数
main "$@"
