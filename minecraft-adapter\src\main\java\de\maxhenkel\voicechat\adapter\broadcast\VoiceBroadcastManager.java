package de.maxhenkel.voicechat.adapter.broadcast;

import de.maxhenkel.voicechat.adapter.VoiceChatAdapterPlugin;
import de.maxhenkel.voicechat.adapter.network.VoiceServerClient;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 语音广播管理器 - 使用LocationSound实现服务器级语音广播
 */
public class VoiceBroadcastManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(VoiceBroadcastManager.class);
    
    private final VoiceChatAdapterPlugin plugin;
    private final VoiceServerClient voiceServerClient;
    private final Map<UUID, BroadcastSession> activeBroadcasts;
    private final Set<UUID> authorizedBroadcasters;
    
    public VoiceBroadcastManager(VoiceChatAdapterPlugin plugin) {
        this.plugin = plugin;
        this.voiceServerClient = plugin.getVoiceServerClient();
        this.activeBroadcasts = new ConcurrentHashMap<>();
        this.authorizedBroadcasters = ConcurrentHashMap.newKeySet();
    }
    
    /**
     * 开始语音广播会话
     */
    public boolean startBroadcast(Player broadcaster, String broadcastName) {
        if (!canBroadcast(broadcaster)) {
            return false;
        }
        
        UUID broadcasterId = broadcaster.getUniqueId();
        
        // 检查是否已经在广播
        if (activeBroadcasts.containsKey(broadcasterId)) {
            return false;
        }
        
        try {
            // 在语音服务器上开始广播
            boolean success = voiceServerClient.startBroadcast(broadcasterId);
            if (!success) {
                return false;
            }

            // 创建广播会话
            BroadcastSession session = new BroadcastSession(broadcasterId, broadcastName, System.currentTimeMillis());
            activeBroadcasts.put(broadcasterId, session);

            // 通知所有玩家广播开始
            notifyBroadcastStart(broadcaster, broadcastName);

            // 启动广播音频传输任务
            startBroadcastTask(session);

            LOGGER.info("Player {} started voice broadcast: '{}'", broadcaster.getName(), broadcastName);
            return true;

        } catch (Exception e) {
            LOGGER.error("Failed to start broadcast for {}: {}", broadcaster.getName(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 停止语音广播会话
     */
    public boolean stopBroadcast(Player broadcaster) {
        UUID broadcasterId = broadcaster.getUniqueId();
        BroadcastSession session = activeBroadcasts.remove(broadcasterId);
        
        if (session == null) {
            return false;
        }
        
        try {
            // 在语音服务器上停止广播
            voiceServerClient.stopBroadcast(broadcasterId);

            // 停止广播任务
            if (session.getTask() != null) {
                session.getTask().cancel();
            }

            // 通知所有玩家广播结束
            notifyBroadcastEnd(broadcaster, session.getBroadcastName());

            LOGGER.info("Player {} stopped voice broadcast: '{}'", broadcaster.getName(), session.getBroadcastName());
            return true;

        } catch (Exception e) {
            LOGGER.error("Failed to stop broadcast for {}: {}", broadcaster.getName(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查玩家是否可以进行广播
     */
    public boolean canBroadcast(Player player) {
        return player.hasPermission("voicechat.admin") || 
               player.isOp() || 
               authorizedBroadcasters.contains(player.getUniqueId());
    }
    
    /**
     * 授权玩家进行广播
     */
    public void authorizeBroadcaster(UUID playerUuid) {
        authorizedBroadcasters.add(playerUuid);
        LOGGER.info("Authorized player {} for voice broadcasting", playerUuid);
    }
    
    /**
     * 取消玩家的广播授权
     */
    public void unauthorizeBroadcaster(UUID playerUuid) {
        authorizedBroadcasters.remove(playerUuid);
        
        // 如果玩家正在广播，停止广播
        BroadcastSession session = activeBroadcasts.remove(playerUuid);
        if (session != null && session.getTask() != null) {
            session.getTask().cancel();
            Player player = Bukkit.getPlayer(playerUuid);
            if (player != null) {
                notifyBroadcastEnd(player, session.getBroadcastName());
            }
        }
        
        LOGGER.info("Removed broadcast authorization for player {}", playerUuid);
    }
    
    /**
     * 获取当前活跃的广播会话
     */
    public Map<UUID, BroadcastSession> getActiveBroadcasts() {
        return new HashMap<>(activeBroadcasts);
    }
    
    /**
     * 获取授权的广播员列表
     */
    public Set<UUID> getAuthorizedBroadcasters() {
        return new HashSet<>(authorizedBroadcasters);
    }
    
    /**
     * 通知所有玩家广播开始
     */
    private void notifyBroadcastStart(Player broadcaster, String broadcastName) {
        String message = "§6[BROADCAST] §e" + broadcaster.getName() + " §fstarted broadcasting: §a" + broadcastName;
        
        for (Player player : Bukkit.getOnlinePlayers()) {
            player.sendMessage(message);
            // 播放提示音
            player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_CHIME, 1.0f, 1.0f);
        }
    }
    
    /**
     * 通知所有玩家广播结束
     */
    private void notifyBroadcastEnd(Player broadcaster, String broadcastName) {
        String message = "§6[BROADCAST] §e" + broadcaster.getName() + " §fstopped broadcasting: §c" + broadcastName;
        
        for (Player player : Bukkit.getOnlinePlayers()) {
            player.sendMessage(message);
            // 播放结束音
            player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_BASS, 1.0f, 0.8f);
        }
    }
    
    /**
     * 启动广播任务
     */
    private void startBroadcastTask(BroadcastSession session) {
        BukkitRunnable task = new BukkitRunnable() {
            @Override
            public void run() {
                Player broadcaster = Bukkit.getPlayer(session.getBroadcasterId());
                if (broadcaster == null || !broadcaster.isOnline()) {
                    // 广播员离线，停止广播
                    activeBroadcasts.remove(session.getBroadcasterId());
                    this.cancel();
                    return;
                }
                
                // 这里实现语音数据的广播逻辑
                // 由于我们使用LocationSound，实际的语音传输会通过语音聊天系统处理
                // 这个任务主要用于维护广播会话状态
                
                session.updateLastActivity();
            }
        };
        
        session.setTask(task);
        task.runTaskTimerAsynchronously(plugin, 0L, 20L); // 每秒检查一次
    }
    
    /**
     * 清理过期的广播会话
     */
    public void cleanupExpiredBroadcasts() {
        long currentTime = System.currentTimeMillis();
        long timeout = 5 * 60 * 1000; // 5分钟超时
        
        activeBroadcasts.entrySet().removeIf(entry -> {
            BroadcastSession session = entry.getValue();
            if (currentTime - session.getLastActivity() > timeout) {
                if (session.getTask() != null) {
                    session.getTask().cancel();
                }
                
                Player broadcaster = Bukkit.getPlayer(session.getBroadcasterId());
                if (broadcaster != null) {
                    notifyBroadcastEnd(broadcaster, session.getBroadcastName());
                }
                
                LOGGER.info("Cleaned up expired broadcast session: {}", session.getBroadcastName());
                return true;
            }
            return false;
        });
    }
    
    /**
     * 关闭广播管理器
     */
    public void shutdown() {
        // 停止所有活跃的广播
        for (BroadcastSession session : activeBroadcasts.values()) {
            if (session.getTask() != null) {
                session.getTask().cancel();
            }
        }
        activeBroadcasts.clear();
        authorizedBroadcasters.clear();
        
        LOGGER.info("Voice broadcast manager shutdown complete");
    }
    
    /**
     * 广播会话类
     */
    public static class BroadcastSession {
        private final UUID broadcasterId;
        private final String broadcastName;
        private final long startTime;
        private long lastActivity;
        private BukkitRunnable task;
        
        public BroadcastSession(UUID broadcasterId, String broadcastName, long startTime) {
            this.broadcasterId = broadcasterId;
            this.broadcastName = broadcastName;
            this.startTime = startTime;
            this.lastActivity = startTime;
        }
        
        public UUID getBroadcasterId() { return broadcasterId; }
        public String getBroadcastName() { return broadcastName; }
        public long getStartTime() { return startTime; }
        public long getLastActivity() { return lastActivity; }
        public BukkitRunnable getTask() { return task; }
        
        public void setTask(BukkitRunnable task) { this.task = task; }
        public void updateLastActivity() { this.lastActivity = System.currentTimeMillis(); }
        
        public long getDuration() {
            return System.currentTimeMillis() - startTime;
        }
    }
}
