# PlayerState 连接状态同步修复

## 🎯 问题描述

**现象：** 双方都显示为插座断开（disconnected），但实际上他们都连接到了独立服务器。

**根本原因：** 独立服务端和 minecraft-adapter 之间缺少连接状态的双向通信。当玩家连接到独立语音服务器时，独立服务端知道玩家已连接，但 minecraft-adapter 不知道，所以 PlayerState 仍然显示为 disconnected。

## ✅ 解决方案

我实现了一个**连接状态同步机制**，让 minecraft-adapter 能够定期从独立服务端获取玩家的真实连接状态。

### 1. 独立服务端新增 API 端点

**新增 API：** `GET /api/players/connections`

<augment_code_snippet path="standalone-voice-server/src/main/java/de/maxhenkel/voicechat/standalone/api/PlayerApiHandler.java" mode="EXCERPT">
````java
/**
 * 获取所有玩家的连接状态
 * GET /api/players/connections
 */
public void handleGetPlayerConnections(Context ctx) {
    Map<String, Boolean> connectionStates = new HashMap<>();
    
    // 获取所有已知玩家的连接状态
    for (PlayerData player : voiceServer.getPlayerManager().getAllPlayers()) {
        UUID playerUuid = player.getUuid();
        boolean isConnected = voiceServer.isPlayerConnected(playerUuid);
        connectionStates.put(playerUuid.toString(), isConnected);
    }
    
    ctx.json(connectionStates);
}
````
</augment_code_snippet>

### 2. minecraft-adapter 新增状态查询功能

**新增方法：** `VoiceServerClient.getPlayerConnectionStates()`

<augment_code_snippet path="minecraft-adapter/src/main/java/de/maxhenkel/voicechat/adapter/network/VoiceServerClient.java" mode="EXCERPT">
````java
/**
 * 获取玩家连接状态
 */
public Map<UUID, Boolean> getPlayerConnectionStates() throws Exception {
    Request request = new Request.Builder()
            .url(baseUrl + "/api/players/connections")
            .header("Authorization", authHeader)
            .get()
            .build();

    try (Response response = httpClient.newCall(request).execute()) {
        // 解析响应并返回连接状态映射
        Map<UUID, Boolean> connectionStates = new HashMap<>();
        // ... 解析逻辑
        return connectionStates;
    }
}
````
</augment_code_snippet>

### 3. 定期同步任务

**新增功能：** `PlayerStateManager` 中的连接状态同步任务

<augment_code_snippet path="minecraft-adapter/src/main/java/de/maxhenkel/voicechat/adapter/managers/PlayerStateManager.java" mode="EXCERPT">
````java
/**
 * 同步连接状态
 */
private void syncConnectionStates() {
    try {
        Map<UUID, Boolean> connectionStates = voiceServerClient.getPlayerConnectionStates();
        
        for (Map.Entry<UUID, Boolean> entry : connectionStates.entrySet()) {
            UUID playerUuid = entry.getKey();
            boolean isConnected = entry.getValue();
            
            PlayerState state = states.get(playerUuid);
            if (state != null) {
                boolean shouldBeDisconnected = !isConnected;
                
                if (state.isDisconnected() != shouldBeDisconnected) {
                    state.setDisconnected(shouldBeDisconnected);
                    broadcastState(state);
                    LOGGER.debug("Updated connection state for {}: connected={}", playerUuid, isConnected);
                }
            }
        }
    } catch (Exception e) {
        LOGGER.warn("Failed to sync connection states: {}", e.getMessage());
    }
}
````
</augment_code_snippet>

## 🔧 实现细节

### 1. 同步频率
- **间隔：** 每5秒同步一次
- **方式：** 异步任务，不影响游戏性能
- **启动：** 插件初始化时自动启动
- **停止：** 插件关闭时自动停止

### 2. 错误处理
- 网络请求失败时只记录警告，不影响游戏
- 无效的 UUID 会被跳过并记录警告
- 独立服务端不可用时会跳过同步

### 3. 性能优化
- 只有状态真正发生变化时才广播更新
- 使用异步任务避免阻塞主线程
- 批量处理多个玩家的状态更新

## 📋 修改的文件

### 独立服务端：
1. `PlayerApiHandler.java` - 新增连接状态查询 API
2. `ApiServer.java` - 注册新的 API 路由
3. `VoiceServer.java` - 新增 isPlayerConnected 方法
4. `ConnectionManager.java` - 新增连接状态检查方法

### minecraft-adapter：
1. `VoiceServerClient.java` - 新增状态查询方法
2. `PlayerStateManager.java` - 新增同步任务和逻辑

## 🎯 预期效果

修复后，应该能看到：

1. **实时状态更新**：玩家连接/断开语音聊天时，状态会在5秒内同步
2. **正确的图标显示**：
   - 连接的玩家：正常显示（无断开图标）
   - 断开的玩家：显示插座断开图标
3. **群组HUD正常**：群组中的玩家信息正确显示

## 🚀 测试步骤

1. **重启服务器**：重启 Minecraft 服务器和独立语音服务器
2. **玩家加入**：让玩家加入服务器
3. **观察状态**：
   - 初始状态应该显示为断开
   - 玩家连接语音聊天后，5秒内状态应该更新为连接
   - 玩家断开语音聊天后，5秒内状态应该更新为断开

## 📊 日志监控

在服务器日志中查找以下信息：
```
[PlayerStateManager] Updated connection state for <UUID>: connected=true/false
[PlayerStateManager] Synchronized connection states for X players
[PlayerStateNetworkManager] Sent PlayerState to <PlayerName>
```

**现在连接状态应该能正确同步了！** 🎉
