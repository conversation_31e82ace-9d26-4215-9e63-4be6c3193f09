mod_loader=fabric

included_projects=:api, :common, :common-client

minecraft_dependency=1.21.8

# Fabric API
import_fabric_api=true
fabric_api_version=0.129.0+1.21.8
fabric_api_dependency_breaks=<0.129.0+1.21.8

included_fabric_api_modules=\
  fabric-api-base, \
  fabric-command-api-v2, \
  fabric-lifecycle-events-v1, \
  fabric-networking-api-v1, \
  fabric-resource-loader-v0, \
  fabric-key-binding-api-v1, \
  fabric-rendering-v1

add_quilt_supported_tag=false

# Dependencies
modmenu_version=15.0.0-beta.3
fabric_permission_api_version=0.3.3
viaversion_version=5.0.0

# Optional dependencies
curseforge_upload_optional_dependencies=modmenu, sound-physics-remastered, cloth-config, audioplayer, luckperms, viafabric
modrinth_upload_optional_dependencies=modmenu, sound-physics-remastered, cloth-config, audioplayer, luckperms, viafabric
