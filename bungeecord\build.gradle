plugins {
    id 'java'
}

apply plugin: 'com.gradleup.shadow'
apply plugin: 'com.modrinth.minotaur'
apply plugin: 'io.papermc.hangar-publish-plugin'

apply from: "https://raw.githubusercontent.com/henkelmax/mod-gradle-scripts/${mod_gradle_script_version}/mod.gradle"

processResources {
    filesMatching('bungee.yml') {
        expand 'mod_version': mod_version
    }
}

repositories {
    mavenCentral()
    maven { url = 'https://oss.sonatype.org/content/repositories/snapshots/' }
}

dependencies {
    compileOnly "net.md-5:bungeecord-api:${bungeecord_version}"
}

tasks.register('generateJava', Copy) {
    from project(':common-proxy').file('src/template/java')
    into "${layout.buildDirectory.asFile.get()}/generated/java"
    expand 'mod_version': mod_version,
            'compatibility_version': voicechat_compatibility_version
}

sourceSets.main.java {
    srcDir "${layout.buildDirectory.asFile.get()}/generated/java"
}

compileJava.dependsOn generateJava
build.dependsOn shadowJar