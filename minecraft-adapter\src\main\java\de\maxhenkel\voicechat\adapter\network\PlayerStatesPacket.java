package de.maxhenkel.voicechat.adapter.network;

import de.maxhenkel.voicechat.adapter.model.PlayerState;
import de.maxhenkel.voicechat.adapter.util.FriendlyByteBuf;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 多个玩家状态数据包
 * 用于向客户端发送所有玩家的状态信息
 */
public class PlayerStatesPacket {
    
    public static final String CHANNEL = "voicechat:player_states";
    
    private Map<UUID, PlayerState> playerStates;
    
    public PlayerStatesPacket() {
        this.playerStates = new HashMap<>();
    }
    
    public PlayerStatesPacket(Map<UUID, PlayerState> playerStates) {
        this.playerStates = playerStates;
    }
    
    public Map<UUID, PlayerState> getPlayerStates() {
        return playerStates;
    }
    
    /**
     * 从字节缓冲区读取数据包
     */
    public void fromBytes(FriendlyByteBuf buf) throws IOException {
        playerStates = new HashMap<>();
        int count = buf.readInt(); // 使用readInt而不是readVarInt，与原版保持一致
        for (int i = 0; i < count; i++) {
            PlayerState playerState = PlayerState.fromBytes(buf);
            playerStates.put(playerState.getUuid(), playerState);
        }
    }

    /**
     * 将数据包写入字节缓冲区
     */
    public void toBytes(FriendlyByteBuf buf) throws IOException {
        buf.writeInt(playerStates.size()); // 使用writeInt而不是writeVarInt，与原版保持一致
        for (Map.Entry<UUID, PlayerState> entry : playerStates.entrySet()) {
            entry.getValue().toBytes(buf);
        }
    }
    
    /**
     * 转换为字节数组（用于插件消息）
     */
    public byte[] toByteArray() throws IOException {
        try (FriendlyByteBuf buf = new FriendlyByteBuf()) {
            toBytes(buf);
            return buf.toByteArray();
        }
    }
    
    /**
     * 从字节数组创建数据包（用于插件消息）
     */
    public static PlayerStatesPacket fromByteArray(byte[] data) throws IOException {
        try (FriendlyByteBuf buf = new FriendlyByteBuf(data)) {
            PlayerStatesPacket packet = new PlayerStatesPacket();
            packet.fromBytes(buf);
            return packet;
        }
    }
}
