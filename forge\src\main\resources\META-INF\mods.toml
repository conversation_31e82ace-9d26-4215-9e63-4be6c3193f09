modLoader = "javafml"
loaderVersion = "*"
license = "All rights reserved"
issueTrackerURL = "https://github.com/henkelmax/simple-voice-chat/issues"
[[mods]]
modId = "voicechat"
version = "${mod_version}"
displayName = "Simple Voice Chat"
updateJSONURL = "https://update.maxhenkel.de/forge/voicechat"
displayURL = "https://modrepo.de/minecraft/voicechat/overview"
logoFile = "icon.png"
authors = "Max Henkel"
description = '''A working voice chat in Minecraft!'''
displayTest = "NONE"
[[dependencies.voicechat]]
modId = "forge"
mandatory = true
versionRange = "${forge_dependency}"
ordering = "NONE"
side = "BOTH"
[[dependencies.voicechat]]
modId = "minecraft"
mandatory = true
versionRange = "[1.21.7, 1.21.8]"
ordering = "NONE"
side = "BOTH"
[[dependencies.voicechat]]
modId = "cloth_config"
mandatory = false
versionRange = "[${cloth_config_version},)"
ordering = "AFTER"
side = "BOTH"
