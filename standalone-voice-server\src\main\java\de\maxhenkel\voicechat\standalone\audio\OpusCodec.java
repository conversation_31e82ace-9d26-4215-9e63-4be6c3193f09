package de.maxhenkel.voicechat.standalone.audio;

/**
 * Opus编解码器类型枚举
 */
public enum OpusCodec {
    /**
     * VOIP模式 - 针对语音通话优化
     * 适用于语音聊天，提供最佳的语音质量和低延迟
     */
    VOIP("VOIP", "Voice over IP optimized", 0),
    
    /**
     * AUDIO模式 - 针对音频内容优化
     * 适用于音乐和高质量音频内容
     */
    AUDIO("AUDIO", "Audio content optimized", 1),
    
    /**
     * RESTRICTED_LOWDELAY模式 - 受限低延迟模式
     * 提供最低延迟，但可能牺牲一些音质
     */
    RESTRICTED_LOWDELAY("RESTRICTED_LOWDELAY", "Restricted low delay", 2);
    
    private final String name;
    private final String description;
    private final int value;
    
    OpusCodec(String name, String description, int value) {
        this.name = name;
        this.description = description;
        this.value = value;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public int getValue() {
        return value;
    }
    
    /**
     * 从字符串获取编解码器类型
     */
    public static OpusCodec fromString(String name) {
        if (name == null) {
            return VOIP; // 默认值
        }
        
        for (OpusCodec codec : values()) {
            if (codec.name.equalsIgnoreCase(name)) {
                return codec;
            }
        }
        
        // 如果找不到匹配的，返回默认值
        return VOIP;
    }
    
    /**
     * 获取编解码器的推荐比特率
     */
    public int getRecommendedBitrate() {
        switch (this) {
            case VOIP:
                return 24000; // 24 kbps - 语音聊天的标准比特率
            case AUDIO:
                return 64000; // 64 kbps - 音频内容的高质量比特率
            case RESTRICTED_LOWDELAY:
                return 16000; // 16 kbps - 低延迟模式的较低比特率
            default:
                return 24000;
        }
    }
    
    /**
     * 获取编解码器的复杂度设置
     */
    public int getComplexity() {
        switch (this) {
            case VOIP:
                return 5; // 中等复杂度，平衡质量和性能
            case AUDIO:
                return 8; // 高复杂度，追求最佳音质
            case RESTRICTED_LOWDELAY:
                return 3; // 低复杂度，优先考虑延迟
            default:
                return 5;
        }
    }
    
    /**
     * 获取编解码器的帧大小（毫秒）
     */
    public int getFrameSize() {
        switch (this) {
            case VOIP:
                return 20; // 20ms - 语音聊天的标准帧大小
            case AUDIO:
                return 20; // 20ms - 音频内容也使用20ms
            case RESTRICTED_LOWDELAY:
                return 10; // 10ms - 低延迟模式使用更小的帧
            default:
                return 20;
        }
    }
    
    /**
     * 是否启用DTX（不连续传输）
     */
    public boolean isDtxEnabled() {
        switch (this) {
            case VOIP:
                return true; // 语音聊天启用DTX以节省带宽
            case AUDIO:
                return false; // 音频内容不启用DTX以保持质量
            case RESTRICTED_LOWDELAY:
                return false; // 低延迟模式不启用DTX以减少处理时间
            default:
                return true;
        }
    }
    
    /**
     * 获取编解码器的预测模式
     */
    public boolean isInbandFecEnabled() {
        switch (this) {
            case VOIP:
                return true; // 语音聊天启用FEC以提高容错性
            case AUDIO:
                return false; // 音频内容通常不需要FEC
            case RESTRICTED_LOWDELAY:
                return false; // 低延迟模式不启用FEC以减少延迟
            default:
                return true;
        }
    }
    
    @Override
    public String toString() {
        return name;
    }
}
