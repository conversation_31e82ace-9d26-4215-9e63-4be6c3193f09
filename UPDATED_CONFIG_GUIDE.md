# 更新的配置文件指南

## 问题解决

您遇到的跨服务器群组通信问题现在可以通过更新的配置文件来解决。新的配置文件包含了跨服务器和跨世界通信的详细设置。

## Minecraft Adapter 配置 (config.yml)

```yaml
# Voice Chat Adapter Configuration

# 语音服务器配置
voice-server:
  # 语音服务器地址
  host: "localhost"
  # 语音服务器UDP端口
  port: 24454
  # API端点地址
  api-endpoint: "http://localhost:8080"
  # 认证令牌（必须与语音服务器配置一致）
  auth-token: "change-this-secret-token"

# 同步配置
sync:
  # 位置同步间隔（毫秒），0表示禁用定时同步
  position-interval: 1000
  # 权限同步间隔（毫秒），0表示禁用定时同步
  permission-interval: 5000
  # 是否在玩家移动时立即同步位置
  sync-on-move: true
  # 最小移动距离（方块），小于此距离的移动不会触发同步
  min-move-distance: 1.0

# 服务器名称（用于区分不同的Minecraft服务器）
# 重要：此名称必须在所有连接到同一独立语音服务器的Adapter中保持唯一
# 如果名称冲突，插件将拒绝启动
server-name: "survival-server"  # 改为您的服务器名称

# 跨服务器通信配置
cross-server:
  # 是否启用跨服务器通信（允许与其他服务器的玩家进行语音通信）
  enabled: true
  # 允许通信的服务器列表（留空表示允许与所有注册的服务器通信）
  allowed-servers: []
  # 示例：只允许与特定服务器通信
  # allowed-servers:
  #   - "creative-server"
  #   - "minigame-server"

# 跨世界通信配置
cross-world:
  # 是否启用跨世界通信（允许不同世界的玩家进行语音通信）
  enabled: true
  # 允许通信的世界列表（留空表示允许所有世界间通信）
  allowed-worlds: []
  # 示例：只允许特定世界间通信
  # allowed-worlds:
  #   - "world"
  #   - "world_nether"

# 权限配置
permissions:
  # 默认权限设置（当玩家没有特定权限时使用）
  defaults:
    # 基本语音权限
    listen: true          # 是否可以听到其他玩家
    speak: true           # 是否可以说话
    # 群组权限
    groups: true          # 是否可以使用群组功能
    create-group: true    # 是否可以创建群组
    join-group: true      # 是否可以加入群组
    # 跨区域权限 - 关键设置！
    cross-world: true     # 是否可以跨世界通信
    cross-server: true    # 是否可以跨服务器通信 - 设为true解决群组问题
    # 高级权限
    record: false         # 是否可以录音
    admin: false          # 是否有管理员权限
```

## 独立语音服务器配置 (voice-server.yml)

```yaml
# 独立语音服务器配置文件

# 语音服务器配置
server:
  host: "0.0.0.0"
  port: 24454
  bind_address: ""

# API服务器配置
api:
  host: "0.0.0.0"
  port: 8080
  auth_token: "change-this-secret-token"

# 语音配置
voice:
  codec: "VOIP"
  mtu_size: 1024
  keep_alive: 1000
  max_distance: 48.0
  whisper_distance_multiplier: 0.5
  groups_enabled: true
  allow_recording: true
  bitrate: 24000
  complexity: 5
  dtx_enabled: true
  fec_enabled: true

# 安全配置
security:
  encryption_enabled: true
  auth_timeout: 30000

# 跨服务器通信配置 - 关键设置！
cross-server:
  # 是否自动启用跨服务器通信（当服务器注册时自动配置）
  auto_enable: true
  # 默认是否允许跨服务器通信
  default_allowed: true
  # 服务器注册超时时间（毫秒）
  registration_timeout: 30000
  # Keepalive超时时间（毫秒）
  keepalive_timeout: 1800000  # 30分钟
  # 清理间隔（毫秒）
  cleanup_interval: 1800000   # 30分钟

# 跨世界通信配置
cross-world:
  default_allowed: true
  config_timeout: 30000

# 权限管理配置 - 关键设置！
permissions:
  # 默认权限设置
  defaults:
    listen: true
    speak: true
    groups: true
    create_group: true
    join_group: true
    # 跨区域权限 - 设为true解决群组通信问题
    cross_world: true
    cross_server: true
    # 管理权限
    manage_group: false
    admin: false
    record: false
    spectator_interaction: false
    spectator_possession: false

# 服务器管理配置
server-management:
  # 是否启用服务器自动注册
  auto_registration: true
  # 是否在服务器注册时自动配置跨服务器通信 - 重要！
  auto_configure_cross_server: true
  # 服务器名称验证规则
  name_pattern: "^[a-zA-Z0-9_-]{1,32}$"
  allow_duplicate_names: false

# 监控和日志配置
monitoring:
  verbose_logging: false
  log_voice_stats: true
  stats_interval: 300000  # 5分钟
  performance_monitoring: true
```

## 解决跨服务器群组通信问题的关键设置

### 1. 在Adapter配置中：
```yaml
# 确保启用跨服务器通信
cross-server:
  enabled: true

# 确保玩家有跨服务器权限
permissions:
  defaults:
    cross-server: true
```

### 2. 在独立语音服务器配置中：
```yaml
# 启用自动跨服务器配置
cross-server:
  auto_enable: true
  default_allowed: true

# 设置默认跨服务器权限
permissions:
  defaults:
    cross_server: true

# 启用自动配置
server-management:
  auto_configure_cross_server: true
```

## 配置步骤

### 1. 更新配置文件
- 将新的配置内容复制到您的 `config.yml` 和 `voice-server.yml` 文件中
- 修改 `server-name` 为每个服务器的唯一名称（如 "survival-server", "creative-server"）
- 确保 `auth-token` 在所有配置文件中一致

### 2. 重启服务
1. 先重启独立语音服务器
2. 然后重启所有Minecraft服务器的Adapter插件

### 3. 验证配置
检查日志中是否有以下消息：
```
[INFO] Server 'your-server-name' registered successfully with voice server
[INFO] Enabled cross-server communication for server: your-server-name
[INFO] Enabled default cross-server permission for all players
```

## 测试跨服务器群组通信

1. **A服玩家创建群组**：使用语音聊天客户端创建一个群组
2. **B服玩家加入群组**：B服玩家应该能够看到并加入A服玩家创建的群组
3. **测试语音通信**：A服和B服玩家现在应该能够在群组中互相听到对方的语音

## 故障排除

### 如果仍然无法跨服务器通信：

1. **检查服务器注册**：
```bash
curl -X GET "http://localhost:8080/api/servers/register" \
  -H "Authorization: Bearer your-auth-token"
```

2. **检查跨服务器规则**：
```bash
curl -X GET "http://localhost:8080/api/servers/cross-server-rules" \
  -H "Authorization: Bearer your-auth-token"
```

3. **手动添加跨服务器规则**（如果自动配置失败）：
```bash
curl -X POST "http://localhost:8080/api/servers/cross-server-rules" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{"fromServer": "server-a", "toServer": "server-b"}'
```

## 重要提示

- **服务器名称必须唯一**：每个连接到同一独立语音服务器的Adapter必须有不同的 `server-name`
- **权限是关键**：确保 `cross-server: true` 在权限配置中设置
- **自动配置**：新版本会在服务器注册时自动配置跨服务器通信
- **重启顺序**：先重启独立语音服务器，再重启Adapter插件
