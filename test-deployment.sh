#!/bin/bash

# 独立语音服务器测试部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    if ! command -v java &> /dev/null; then
        log_error "Java 未安装"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | awk -F '.' '{print $1}')
    if [ "$JAVA_VERSION" -lt 17 ]; then
        log_error "需要 Java 17+，当前版本: $JAVA_VERSION"
        exit 1
    fi
    
    log_info "Java 版本检查通过: $JAVA_VERSION"
}

# 构建项目
build_projects() {
    log_step "构建独立语音服务器..."
    
    cd standalone-voice-server
    if [ -f "./gradlew" ]; then
        ./gradlew shadowJar
    else
        gradle shadowJar
    fi
    cd ..
    
    log_step "构建Minecraft适配器..."
    
    cd minecraft-adapter
    if [ -f "./gradlew" ]; then
        ./gradlew shadowJar
    else
        gradle shadowJar
    fi
    cd ..
    
    log_info "项目构建完成"
}

# 启动独立语音服务器
start_voice_server() {
    log_step "启动独立语音服务器..."
    
    cd standalone-voice-server
    
    # 检查JAR文件
    JAR_FILE="build/libs/standalone-voice-server-1.0.0.jar"
    if [ ! -f "$JAR_FILE" ]; then
        log_error "找不到JAR文件: $JAR_FILE"
        exit 1
    fi
    
    # 启动服务器（后台运行）
    nohup java -jar "$JAR_FILE" voice-server.yml > voice-server.log 2>&1 &
    VOICE_SERVER_PID=$!
    echo $VOICE_SERVER_PID > voice-server.pid
    
    cd ..
    
    log_info "语音服务器已启动，PID: $VOICE_SERVER_PID"
    
    # 等待服务器启动
    log_info "等待语音服务器启动..."
    sleep 5
    
    # 检查服务器是否正常运行
    if ! ps -p $VOICE_SERVER_PID > /dev/null; then
        log_error "语音服务器启动失败"
        cat standalone-voice-server/voice-server.log
        exit 1
    fi
}

# 测试语音服务器API
test_voice_server_api() {
    log_step "测试语音服务器API..."
    
    # 测试健康检查
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        log_info "健康检查通过"
    else
        log_error "健康检查失败"
        return 1
    fi
    
    # 测试状态API（需要认证）
    if curl -f -H "Authorization: Bearer change-this-secret-token" \
            http://localhost:8080/api/status > /dev/null 2>&1; then
        log_info "状态API测试通过"
    else
        log_warn "状态API测试失败（可能是认证问题）"
    fi
    
    log_info "API测试完成"
}

# 测试UDP端口
test_udp_port() {
    log_step "测试UDP端口..."
    
    if command -v nc &> /dev/null; then
        # 使用netcat测试UDP端口
        timeout 2 nc -u -z localhost 24454
        if [ $? -eq 0 ]; then
            log_info "UDP端口 24454 可访问"
        else
            log_warn "UDP端口 24454 测试失败"
        fi
    else
        log_warn "netcat 未安装，跳过UDP端口测试"
    fi
}

# 模拟客户端连接测试
test_client_connection() {
    log_step "模拟客户端连接测试..."
    
    # 创建测试脚本
    cat > test_client.py << 'EOF'
#!/usr/bin/env python3
import socket
import struct
import uuid
import time

def test_voice_server_connection():
    try:
        # 创建UDP socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(5)
        
        # 构造简单的测试数据包
        test_data = b'\xFF' + struct.pack('>Q', int(time.time() * 1000)) + b'\x04' + b'\x00' * 8
        
        # 发送到语音服务器
        sock.sendto(test_data, ('localhost', 24454))
        
        print("✓ UDP数据包发送成功")
        
        # 尝试接收响应（可能会超时，这是正常的）
        try:
            data, addr = sock.recvfrom(1024)
            print(f"✓ 收到响应: {len(data)} 字节")
        except socket.timeout:
            print("! 未收到响应（正常，因为没有有效认证）")
        
        sock.close()
        return True
        
    except Exception as e:
        print(f"✗ 连接测试失败: {e}")
        return False

if __name__ == "__main__":
    test_voice_server_connection()
EOF
    
    if command -v python3 &> /dev/null; then
        python3 test_client.py
        rm -f test_client.py
    else
        log_warn "Python3 未安装，跳过客户端连接测试"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_step "部署信息"
    
    echo "=================================="
    echo "独立语音服务器部署完成！"
    echo "=================================="
    echo "语音服务器地址: localhost:24454 (UDP)"
    echo "API服务器地址: http://localhost:8080"
    echo "日志文件: standalone-voice-server/voice-server.log"
    echo "PID文件: standalone-voice-server/voice-server.pid"
    echo ""
    echo "Minecraft适配器JAR: minecraft-adapter/build/libs/minecraft-adapter-1.0.0.jar"
    echo ""
    echo "下一步："
    echo "1. 将适配器JAR复制到Minecraft服务器的plugins目录"
    echo "2. 配置Minecraft服务器使用独立语音服务器"
    echo "3. 重启Minecraft服务器"
    echo ""
    echo "停止语音服务器: kill \$(cat standalone-voice-server/voice-server.pid)"
    echo "=================================="
}

# 清理函数
cleanup() {
    log_info "清理资源..."
    
    if [ -f "standalone-voice-server/voice-server.pid" ]; then
        PID=$(cat standalone-voice-server/voice-server.pid)
        if ps -p $PID > /dev/null; then
            kill $PID
            log_info "已停止语音服务器 (PID: $PID)"
        fi
        rm -f standalone-voice-server/voice-server.pid
    fi
    
    rm -f test_client.py
}

# 主函数
main() {
    echo "独立语音服务器测试部署"
    echo "======================="
    
    # 注册清理函数
    trap cleanup EXIT
    
    check_dependencies
    build_projects
    start_voice_server
    test_voice_server_api
    test_udp_port
    test_client_connection
    show_deployment_info
    
    log_info "测试部署完成！"
}

# 处理命令行参数
case "${1:-}" in
    "stop")
        cleanup
        exit 0
        ;;
    "test")
        test_voice_server_api
        test_udp_port
        test_client_connection
        exit 0
        ;;
    *)
        main
        ;;
esac
