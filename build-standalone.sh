#!/bin/bash

# 独立语音服务器构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 设置环境变量
setup_environment() {
    # 设置构建所需的环境变量（避免警告）
    export CURSEFORGE_API_KEY="${CURSEFORGE_API_KEY:-dummy-key-for-local-build}"
    export MODRINTH_TOKEN="${MODRINTH_TOKEN:-dummy-token-for-local-build}"
    export MOD_UPDATE_API_KEY="${MOD_UPDATE_API_KEY:-dummy-key-for-local-build}"
    export HANGAR_API_KEY="${HANGAR_API_KEY:-dummy-key-for-local-build}"
}

# 检查环境
check_environment() {
    log_step "检查构建环境..."

    # 设置环境变量
    setup_environment

    # 检查Java版本
    if ! command -v java &> /dev/null; then
        log_error "Java 未安装"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | awk -F '.' '{print $1}')
    if [ "$JAVA_VERSION" -lt 17 ]; then
        log_error "需要 Java 17+，当前版本: $JAVA_VERSION"
        exit 1
    fi
    
    log_info "Java 版本: $JAVA_VERSION ✓"
    
    # 检查Gradle
    if [ -f "./gradlew" ]; then
        log_info "使用项目自带的Gradle Wrapper"
        GRADLE_CMD="./gradlew"
    elif command -v gradle &> /dev/null; then
        log_info "使用系统安装的Gradle"
        GRADLE_CMD="gradle"
    else
        log_error "Gradle 未找到，请安装Gradle或确保gradlew存在"
        exit 1
    fi
}

# 清理之前的构建
clean_build() {
    log_step "清理之前的构建..."
    
    $GRADLE_CMD clean
    
    log_info "清理完成"
}

# 构建独立语音服务器
build_voice_server() {
    log_step "构建独立语音服务器..."
    
    $GRADLE_CMD :standalone-voice-server:shadowJar
    
    if [ -f "standalone-voice-server/build/libs/standalone-voice-server-1.0.0.jar" ]; then
        log_info "独立语音服务器构建成功 ✓"
    else
        log_error "独立语音服务器构建失败"
        exit 1
    fi
}

# 构建Minecraft适配器
build_adapter() {
    log_step "构建Minecraft适配器..."
    
    $GRADLE_CMD :minecraft-adapter:shadowJar
    
    if [ -f "minecraft-adapter/build/libs/minecraft-adapter-1.0.0.jar" ]; then
        log_info "Minecraft适配器构建成功 ✓"
    else
        log_error "Minecraft适配器构建失败"
        exit 1
    fi
}

# 运行测试
run_tests() {
    log_step "运行测试..."
    
    $GRADLE_CMD :standalone-voice-server:test :minecraft-adapter:test
    
    log_info "测试完成"
}

# 显示构建结果
show_results() {
    log_step "构建结果"
    
    echo "=================================="
    echo "🎉 构建完成！"
    echo "=================================="
    echo ""
    echo "📦 构建产物："
    echo "独立语音服务器: $(ls -lh standalone-voice-server/build/libs/*.jar 2>/dev/null | awk '{print $9 " (" $5 ")"}')"
    echo "Minecraft适配器: $(ls -lh minecraft-adapter/build/libs/*.jar 2>/dev/null | awk '{print $9 " (" $5 ")"}')"
    echo ""
    echo "🚀 下一步："
    echo "1. 启动独立语音服务器:"
    echo "   cd standalone-voice-server && ./start.sh"
    echo ""
    echo "2. 安装Minecraft适配器:"
    echo "   cp minecraft-adapter/build/libs/minecraft-adapter-1.0.0.jar /path/to/minecraft/plugins/"
    echo ""
    echo "3. 配置Minecraft服务器使用独立语音服务器"
    echo "=================================="
}

# 主函数
main() {
    echo "独立语音服务器构建脚本"
    echo "======================"
    
    check_environment
    
    case "${1:-all}" in
        "clean")
            clean_build
            ;;
        "voice-server")
            build_voice_server
            ;;
        "adapter")
            build_adapter
            ;;
        "test")
            run_tests
            ;;
        "all")
            clean_build
            build_voice_server
            build_adapter
            run_tests
            show_results
            ;;
        *)
            echo "用法: $0 [clean|voice-server|adapter|test|all]"
            echo ""
            echo "选项:"
            echo "  clean        清理构建文件"
            echo "  voice-server 只构建独立语音服务器"
            echo "  adapter      只构建Minecraft适配器"
            echo "  test         运行测试"
            echo "  all          构建所有组件（默认）"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
