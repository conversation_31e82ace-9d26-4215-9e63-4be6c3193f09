#!/bin/bash

# 独立语音服务器启动脚本

# 设置Java选项
JAVA_OPTS="-Xmx1G -Xms512M"
JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC"
JAVA_OPTS="$JAVA_OPTS -XX:+UnlockExperimentalVMOptions"
JAVA_OPTS="$JAVA_OPTS -XX:MaxGCPauseMillis=100"
JAVA_OPTS="$JAVA_OPTS -XX:+DisableExplicitGC"
JAVA_OPTS="$JAVA_OPTS -XX:TargetSurvivorRatio=90"
JAVA_OPTS="$JAVA_OPTS -XX:G1NewSizePercent=50"
JAVA_OPTS="$JAVA_OPTS -XX:G1MaxNewSizePercent=80"
JAVA_OPTS="$JAVA_OPTS -XX:G1MixedGCLiveThresholdPercent=35"
JAVA_OPTS="$JAVA_OPTS -XX:+AlwaysPreTouch"

# 设置日志配置
JAVA_OPTS="$JAVA_OPTS -Dlogback.configurationFile=logback.xml"

# 设置工作目录
cd "$(dirname "$0")"

# 检查Java版本
JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | awk -F '.' '{print $1}')
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo "错误：需要Java 17或更高版本"
    exit 1
fi

# 检查JAR文件是否存在
JAR_FILE="standalone-voice-server-1.0.0.jar"
if [ ! -f "$JAR_FILE" ]; then
    echo "错误：找不到JAR文件 $JAR_FILE"
    echo "请先运行 './gradlew shadowJar' 构建项目"
    exit 1
fi

# 检查配置文件
CONFIG_FILE="voice-server.yml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "警告：找不到配置文件 $CONFIG_FILE，将使用默认配置"
fi

echo "启动独立语音服务器..."
echo "Java选项: $JAVA_OPTS"
echo "JAR文件: $JAR_FILE"
echo "配置文件: $CONFIG_FILE"
echo ""

# 启动服务器
exec java $JAVA_OPTS -jar "$JAR_FILE" "$CONFIG_FILE"
