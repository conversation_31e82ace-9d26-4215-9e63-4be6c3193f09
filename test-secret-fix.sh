#!/bin/bash

# 密钥生成修复测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 测试API端点
test_api_endpoints() {
    log_step "测试独立语音服务器API端点..."
    
    # 测试健康检查
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        log_info "健康检查端点正常 ✓"
    else
        log_error "健康检查端点失败"
        return 1
    fi
    
    # 测试状态端点
    if curl -f -H "Authorization: Bearer change-this-secret-token" \
            http://localhost:8080/api/status > /dev/null 2>&1; then
        log_info "状态API端点正常 ✓"
    else
        log_warn "状态API端点失败（可能是认证问题）"
    fi
    
    # 测试玩家登录端点
    TEST_UUID="550e8400-e29b-41d4-a716-************"
    LOGIN_DATA='{"uuid":"'$TEST_UUID'","name":"TestPlayer","serverName":"test"}'
    
    if curl -f -H "Authorization: Bearer change-this-secret-token" \
            -H "Content-Type: application/json" \
            -d "$LOGIN_DATA" \
            http://localhost:8080/api/players/login > /dev/null 2>&1; then
        log_info "玩家登录端点正常 ✓"
        
        # 测试密钥生成端点
        if curl -f -H "Authorization: Bearer change-this-secret-token" \
                -X POST \
                http://localhost:8080/api/players/$TEST_UUID/secret > /dev/null 2>&1; then
            log_info "密钥生成端点正常 ✓"
        else
            log_error "密钥生成端点失败"
            return 1
        fi
    else
        log_error "玩家登录端点失败"
        return 1
    fi
}

# 显示修复内容
show_fixes() {
    log_step "密钥生成修复内容"
    
    echo "=================================="
    echo "🎉 密钥生成问题修复完成！"
    echo "=================================="
    echo ""
    echo "📋 修复的问题："
    echo "❌ 之前: Secret generation failed: 404 Not Found"
    echo "✅ 现在: 自动注册玩家并生成密钥"
    echo ""
    echo "🔧 具体修复："
    echo "1. ✅ 添加了玩家自动注册逻辑:"
    echo "   - 在生成密钥前先注册玩家"
    echo "   - 发送玩家信息到独立语音服务器"
    echo ""
    echo "2. ✅ 改进了错误处理:"
    echo "   - 注册失败时记录警告但继续尝试"
    echo "   - 更详细的错误日志"
    echo ""
    echo "3. ✅ 修复了API调用顺序:"
    echo "   - 先调用 /api/players/login"
    echo "   - 再调用 /api/players/{uuid}/secret"
    echo ""
    echo "🎯 现在的工作流程："
    echo "1. 客户端请求密钥"
    echo "2. 适配器自动注册玩家到独立语音服务器"
    echo "3. 适配器从独立语音服务器生成密钥"
    echo "4. 适配器发送SecretPacket到客户端"
    echo "5. 客户端连接到独立语音服务器"
    echo ""
    echo "🚀 部署步骤："
    echo "1. 停止Paper服务器"
    echo "2. 替换适配器JAR:"
    echo "   cp minecraft-adapter/build/libs/minecraft-adapter-1.0.0.jar /path/to/paper/plugins/"
    echo ""
    echo "3. 确保独立语音服务器运行:"
    echo "   curl http://localhost:8080/health"
    echo ""
    echo "4. 重启Paper服务器"
    echo ""
    echo "🔍 预期结果："
    echo "- 不再有 '404 Not Found' 错误"
    echo "- 玩家可以正常获取语音聊天密钥"
    echo "- 群组功能可以正常使用"
    echo "- 服务器日志显示成功的密钥生成"
    echo "=================================="
}

# 显示故障排除信息
show_troubleshooting() {
    log_step "故障排除指南"
    
    echo ""
    echo "🔧 如果仍然有问题："
    echo ""
    echo "1. 检查独立语音服务器日志:"
    echo "   tail -f voice-server.log"
    echo "   应该看到玩家登录和密钥生成的日志"
    echo ""
    echo "2. 检查适配器配置:"
    echo "   plugins/VoiceChatAdapter/config.yml"
    echo "   确保 api-endpoint 和 auth-token 正确"
    echo ""
    echo "3. 手动测试API端点:"
    echo "   # 测试玩家登录"
    echo "   curl -H \"Authorization: Bearer your-token\" \\"
    echo "        -H \"Content-Type: application/json\" \\"
    echo "        -d '{\"uuid\":\"test-uuid\",\"name\":\"TestPlayer\",\"serverName\":\"test\"}' \\"
    echo "        http://localhost:8080/api/players/login"
    echo ""
    echo "   # 测试密钥生成"
    echo "   curl -H \"Authorization: Bearer your-token\" \\"
    echo "        -X POST \\"
    echo "        http://localhost:8080/api/players/test-uuid/secret"
    echo ""
    echo "4. 检查认证令牌:"
    echo "   确保适配器和独立语音服务器使用相同的认证令牌"
    echo ""
    echo "5. 检查网络连接:"
    echo "   确保Paper服务器可以访问独立语音服务器的8080端口"
}

# 主函数
main() {
    echo "密钥生成修复验证"
    echo "================"
    
    if test_api_endpoints; then
        log_info "API端点测试通过 ✓"
    else
        log_warn "API端点测试失败，请检查独立语音服务器状态"
    fi
    
    show_fixes
    show_troubleshooting
    
    log_info "修复验证完成！现在可以部署新版本的适配器了。"
}

# 执行主函数
main
