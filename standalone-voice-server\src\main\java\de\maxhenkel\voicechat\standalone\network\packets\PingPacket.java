package de.maxhenkel.voicechat.standalone.network.packets;

import de.maxhenkel.voicechat.standalone.network.Packet;
import io.netty.buffer.ByteBuf;

/**
 * Ping数据包
 */
public class PingPacket extends Packet<PingPacket> {
    
    public static final byte PACKET_ID = (byte) 0x7;
    
    private long timestamp;
    
    public PingPacket() {
    }
    
    public PingPacket(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    @Override
    public void fromBytes(ByteBuf buf) {
        timestamp = buf.readLong();
    }
    
    @Override
    public void toBytes(ByteBuf buf) {
        buf.writeLong(timestamp);
    }
    
    @Override
    public byte getPacketId() {
        return PACKET_ID;
    }

    @Override
    public Class<PingPacket> getPacketClass() {
        return PingPacket.class;
    }
}
