package de.maxhenkel.voicechat.compatibility;

import de.maxhenkel.voicechat.Voicechat;

/**
 * 独立语音服务器兼容性工具类
 */
public class StandaloneCompatibility {
    
    /**
     * 检查是否启用独立语音服务器模式
     */
    public static boolean isStandaloneModeEnabled() {
        try {
            return Voicechat.SERVER_CONFIG.useStandaloneVoiceServer.get();
        } catch (Exception e) {
            // 如果配置不存在（旧版本），返回false
            Voicechat.LOGGER.debug("Standalone voice server config not available, using integrated mode");
            return false;
        }
    }
    
    /**
     * 获取独立语音服务器地址
     */
    public static String getStandaloneVoiceHost() {
        try {
            return Voicechat.SERVER_CONFIG.standaloneVoiceHost.get();
        } catch (Exception e) {
            Voicechat.LOGGER.warn("Failed to get standalone voice host", e);
            return "localhost";
        }
    }
    
    /**
     * 获取独立语音服务器端口
     */
    public static int getStandaloneVoicePort() {
        try {
            return Voicechat.SERVER_CONFIG.standaloneVoicePort.get();
        } catch (Exception e) {
            Voicechat.LOGGER.warn("Failed to get standalone voice port", e);
            return 24454;
        }
    }
    
    /**
     * 验证独立语音服务器配置
     */
    public static boolean validateStandaloneConfig() {
        if (!isStandaloneModeEnabled()) {
            return true; // 集成模式总是有效的
        }
        
        String host = getStandaloneVoiceHost();
        int port = getStandaloneVoicePort();
        
        if (host == null || host.trim().isEmpty()) {
            Voicechat.LOGGER.error("Standalone voice server host is not configured");
            return false;
        }
        
        if (port <= 0 || port > 65535) {
            Voicechat.LOGGER.error("Standalone voice server port is invalid: {}", port);
            return false;
        }
        
        return true;
    }
    
    /**
     * 记录当前模式信息
     */
    public static void logCurrentMode() {
        if (isStandaloneModeEnabled()) {
            Voicechat.LOGGER.info("Voice chat running in STANDALONE mode - Server: {}:{}", 
                    getStandaloneVoiceHost(), getStandaloneVoicePort());
        } else {
            Voicechat.LOGGER.info("Voice chat running in INTEGRATED mode");
        }
    }
}
