package de.maxhenkel.voicechat.adapter.permission;

import de.maxhenkel.voicechat.adapter.config.AdapterConfig;
import de.maxhenkel.voicechat.adapter.network.VoiceServerClient;
// import de.maxhenkel.voicechat.permission.PermissionManager; // 暂时注释掉，因为这个类可能不存在
import org.bukkit.entity.Player;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 权限同步管理器
 * 负责将Minecraft权限同步到独立语音服务器
 */
public class PermissionSyncManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionSyncManager.class);
    
    private final VoiceServerClient voiceServerClient;
    private final AdapterConfig config;
    private final ScheduledExecutorService scheduler;
    
    // 权限映射缓存 <PlayerUUID, LastSyncTime>
    private final Map<UUID, Long> lastSyncTimes = new ConcurrentHashMap<>();
    
    // 权限映射配置：Minecraft权限 -> 独立服务器权限
    private final Map<String, String> permissionMapping = new HashMap<>();
    
    public PermissionSyncManager(VoiceServerClient voiceServerClient, AdapterConfig config) {
        this.voiceServerClient = voiceServerClient;
        this.config = config;
        this.scheduler = Executors.newScheduledThreadPool(2);
        
        initializePermissionMapping();
        startPeriodicSync();
    }
    
    /**
     * 初始化权限映射
     */
    private void initializePermissionMapping() {
        // 基本权限映射
        permissionMapping.put("voicechat.listen", "voicechat.listen");
        permissionMapping.put("voicechat.speak", "voicechat.speak");
        permissionMapping.put("voicechat.groups", "voicechat.groups");
        permissionMapping.put("voicechat.groups.create", "voicechat.groups.create");
        permissionMapping.put("voicechat.groups.join", "voicechat.groups.join");
        permissionMapping.put("voicechat.groups.manage", "voicechat.groups.manage");
        permissionMapping.put("voicechat.admin", "voicechat.admin");
        permissionMapping.put("voicechat.record", "voicechat.record");
        permissionMapping.put("voicechat.spectator.interaction", "voicechat.spectator.interaction");
        permissionMapping.put("voicechat.spectator.possession", "voicechat.spectator.possession");
        permissionMapping.put("voicechat.cross_world", "voicechat.cross_world");
        permissionMapping.put("voicechat.cross_server", "voicechat.cross_server");
        
        LOGGER.info("Initialized permission mapping with {} entries", permissionMapping.size());
    }
    
    /**
     * 启动定期同步
     */
    private void startPeriodicSync() {
        // 每5分钟进行一次权限同步
        scheduler.scheduleAtFixedRate(this::syncAllPlayerPermissions, 5, 5, TimeUnit.MINUTES);
        LOGGER.info("Started periodic permission sync (every 5 minutes)");
    }
    
    /**
     * 同步玩家权限到独立服务器
     */
    public void syncPlayerPermissions(Player player) {
        if (player == null) {
            return;
        }
        
        UUID playerUuid = player.getUniqueId();
        
        // 检查是否需要同步（避免频繁同步）
        Long lastSync = lastSyncTimes.get(playerUuid);
        long now = System.currentTimeMillis();
        if (lastSync != null && (now - lastSync) < 30000) { // 30秒内不重复同步
            return;
        }
        
        scheduler.execute(() -> {
            try {
                Map<String, Boolean> permissions = collectPlayerPermissions(player);
                voiceServerClient.updatePlayerPermissions(playerUuid, permissions);
                
                // 同步权限组
                syncPlayerPermissionGroups(player);
                
                lastSyncTimes.put(playerUuid, now);
                LOGGER.debug("Synced permissions for player {}", player.getName());
                
            } catch (Exception e) {
                LOGGER.error("Failed to sync permissions for player {}", player.getName(), e);
            }
        });
    }
    
    /**
     * 收集玩家权限
     */
    private Map<String, Boolean> collectPlayerPermissions(Player player) {
        Map<String, Boolean> permissions = new HashMap<>();
        
        for (Map.Entry<String, String> entry : permissionMapping.entrySet()) {
            String minecraftPerm = entry.getKey();
            String serverPerm = entry.getValue();
            
            boolean hasPermission = player.hasPermission(minecraftPerm);
            permissions.put(serverPerm, hasPermission);
        }
        
        return permissions;
    }
    
    /**
     * 同步玩家权限组
     */
    private void syncPlayerPermissionGroups(Player player) {
        try {
            UUID playerUuid = player.getUniqueId();
            
            // 检查管理员权限
            if (player.hasPermission("voicechat.admin") || player.isOp()) {
                voiceServerClient.addPlayerToPermissionGroup(playerUuid, "admin");
            } else if (player.hasPermission("voicechat.groups.manage")) {
                voiceServerClient.addPlayerToPermissionGroup(playerUuid, "moderator");
            } else {
                voiceServerClient.addPlayerToPermissionGroup(playerUuid, "user");
            }
            
        } catch (Exception e) {
            LOGGER.error("Failed to sync permission groups for player {}", player.getName(), e);
        }
    }
    
    /**
     * 同步所有在线玩家权限
     */
    public void syncAllPlayerPermissions() {
        try {
            org.bukkit.Bukkit.getOnlinePlayers().forEach(this::syncPlayerPermissions);
            LOGGER.debug("Completed periodic permission sync for {} players", org.bukkit.Bukkit.getOnlinePlayers().size());
        } catch (Exception e) {
            LOGGER.error("Error during periodic permission sync", e);
        }
    }
    
    /**
     * 玩家离线时清理缓存
     */
    public void onPlayerDisconnect(UUID playerUuid) {
        lastSyncTimes.remove(playerUuid);
    }
    
    /**
     * 检查玩家是否有指定权限（从独立服务器查询）
     */
    public boolean checkPlayerPermission(UUID playerUuid, String permission) {
        try {
            return voiceServerClient.checkPlayerPermission(playerUuid, permission);
        } catch (Exception e) {
            LOGGER.error("Failed to check permission {} for player {}", permission, playerUuid, e);
            return false;
        }
    }
    
    /**
     * 强制同步玩家权限（忽略时间限制）
     */
    public void forceSyncPlayerPermissions(Player player) {
        if (player == null) {
            return;
        }
        
        UUID playerUuid = player.getUniqueId();
        lastSyncTimes.remove(playerUuid); // 移除时间限制
        syncPlayerPermissions(player);
    }
    
    /**
     * 添加自定义权限映射
     */
    public void addPermissionMapping(String minecraftPermission, String serverPermission) {
        permissionMapping.put(minecraftPermission, serverPermission);
        LOGGER.info("Added custom permission mapping: {} -> {}", minecraftPermission, serverPermission);
    }
    
    /**
     * 移除权限映射
     */
    public void removePermissionMapping(String minecraftPermission) {
        String removed = permissionMapping.remove(minecraftPermission);
        if (removed != null) {
            LOGGER.info("Removed permission mapping: {} -> {}", minecraftPermission, removed);
        }
    }
    
    /**
     * 获取权限映射
     */
    public Map<String, String> getPermissionMapping() {
        return new HashMap<>(permissionMapping);
    }
    
    /**
     * 获取同步统计信息
     */
    public Map<String, Object> getSyncStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalMappings", permissionMapping.size());
        stats.put("cachedPlayers", lastSyncTimes.size());
        stats.put("lastSyncTimes", new HashMap<>(lastSyncTimes));
        return stats;
    }
    
    /**
     * 关闭权限同步管理器
     */
    public void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        LOGGER.info("Permission sync manager shutdown completed");
    }
}
