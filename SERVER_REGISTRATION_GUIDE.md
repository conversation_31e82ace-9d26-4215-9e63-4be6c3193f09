# 服务器注册和Keepalive功能使用指南

## 功能概述

本功能实现了Minecraft Adapter服务器向独立语音服务器的注册和保活机制，包括：

1. **服务器名称冲突检查**：启动时检查服务器名称是否已被注册
2. **自动服务器注册**：Adapter启动时自动向独立服务器注册
3. **定期Keepalive**：每5分钟发送一次keepalive保持注册状态
4. **自动清理**：独立服务器每30分钟清理超过30分钟未发送keepalive的服务器

## 配置说明

### Adapter配置 (config.yml)

```yaml
# 服务器名称（用于区分不同的Minecraft服务器）
# 必须是唯一的，如果与已注册的服务器冲突，插件将拒绝启动
server-name: "my-unique-server"

# 语音服务器配置
voice-server:
  host: "localhost"
  port: 24454
  api-endpoint: "http://localhost:8080"
  auth-token: "change-this-secret-token"
```

**重要提示**：`server-name` 必须在所有连接到同一独立语音服务器的Adapter中保持唯一。

## 工作流程

### 1. 启动时注册

当Adapter插件启动时：

1. 检查配置的服务器名称是否可用
2. 如果名称已被占用，插件将停止启动并显示错误信息
3. 如果名称可用，自动注册服务器到独立语音服务器
4. 注册成功后开始正常运行

### 2. 定期Keepalive

- Adapter每5分钟向独立服务器发送一次keepalive
- 如果keepalive失败，会尝试重新注册服务器
- 所有keepalive操作都在后台异步执行

### 3. 服务器清理

独立语音服务器：
- 每30分钟检查一次所有注册的服务器
- 移除超过30分钟未发送keepalive的服务器
- 被移除的服务器可以重新注册

### 4. 关闭时清理

当Adapter插件关闭时：
- 自动向独立服务器发送取消注册请求
- 清理本地资源

## API端点

独立语音服务器提供以下REST API端点：

### 服务器注册
```
POST /api/servers/register
Content-Type: application/json
Authorization: Bearer <auth-token>

{
  "serverName": "my-server",
  "host": "*************",
  "port": 25565
}
```

### 发送Keepalive
```
POST /api/servers/{serverName}/keepalive
Authorization: Bearer <auth-token>
```

### 检查服务器名称可用性
```
GET /api/servers/{serverName}/available
Authorization: Bearer <auth-token>
```

### 获取服务器信息
```
GET /api/servers/{serverName}
Authorization: Bearer <auth-token>
```

### 获取所有注册的服务器
```
GET /api/servers
Authorization: Bearer <auth-token>
```

### 获取统计信息
```
GET /api/servers/statistics
Authorization: Bearer <auth-token>
```

### 取消注册服务器
```
DELETE /api/servers/{serverName}
Authorization: Bearer <auth-token>
```

## 错误处理

### 服务器名称冲突

如果启动时检测到服务器名称冲突，会看到类似错误：

```
================================================================================
SERVER NAME CONFLICT DETECTED!
The server name 'my-server' is already registered with the voice server.
Please change the 'server-name' in your config.yml file to a unique name.
Current server name: my-server
================================================================================
```

**解决方案**：修改config.yml中的`server-name`为唯一值。

### 网络连接问题

- 如果无法连接到独立语音服务器，Adapter会继续尝试连接
- Keepalive失败时会自动重试注册
- 所有网络错误都会记录在日志中

## 日志信息

### 正常启动日志
```
[INFO] Server 'my-server' registered successfully with voice server
[INFO] Keepalive task started with interval: 5 minutes
```

### Keepalive日志
```
[DEBUG] Keepalive sent successfully for server: my-server
```

### 清理日志（独立服务器）
```
[INFO] Removed expired server 'old-server' (last keepalive: 35 minutes ago)
[INFO] Cleanup completed: removed 1 expired servers
```

## 故障排除

### 1. 服务器名称冲突
- 检查是否有其他Adapter使用相同的服务器名称
- 修改config.yml中的server-name为唯一值

### 2. 注册失败
- 检查独立语音服务器是否正在运行
- 验证API端点和认证令牌配置是否正确
- 检查网络连接

### 3. Keepalive失败
- 检查网络连接稳定性
- 查看独立服务器日志是否有错误
- 确认服务器未被意外关闭

## 监控建议

1. **定期检查日志**：关注keepalive失败和重连消息
2. **监控API端点**：使用`/api/servers/statistics`获取注册状态
3. **设置告警**：对连续的keepalive失败设置告警
4. **备份配置**：确保server-name的唯一性记录

## 技术细节

- **Keepalive间隔**：5分钟（300秒）
- **超时时间**：30分钟（1800秒）
- **清理间隔**：30分钟（1800秒）
- **重试机制**：keepalive失败时自动重试注册
- **线程安全**：所有操作都是线程安全的
- **异步处理**：网络操作异步执行，不阻塞主线程
