# 独立语音服务器配置示例

本文档提供了配置独立语音服务器的详细示例。

## 1. Minecraft 服务器配置

### 1.1 使用独立语音服务器

编辑 `config/voicechat-server.toml`:

```toml
# 启用独立语音服务器模式
use_standalone_voice_server = true

# 独立语音服务器地址
standalone_voice_host = "voice.example.com"

# 独立语音服务器端口
standalone_voice_port = 24454

# 其他语音聊天设置
voice_chat_distance = 48.0
groups_enabled = true
allow_recording = true
```

### 1.2 传统集成模式（向后兼容）

```toml
# 使用集成语音服务器（默认）
use_standalone_voice_server = false

# 传统配置仍然有效
voice_host = ""
port = 24454
voice_chat_distance = 48.0
```

## 2. 独立语音服务器配置

### 2.1 基本配置 (voice-server.yml)

```yaml
# 服务器配置
server:
  host: "0.0.0.0"
  port: 24454
  bind_address: ""

# API配置
api:
  host: "0.0.0.0"
  port: 8080
  auth_token: "your-secret-token-here"

# 语音配置
voice:
  codec: "VOIP"
  mtu_size: 1024
  keep_alive: 1000
  max_distance: 48.0
  groups_enabled: true
  allow_recording: true

# 安全配置
security:
  encryption_enabled: true
  auth_timeout: 30000
```

### 2.2 多服务器支持

独立语音服务器支持多个Minecraft服务器连接，每个Minecraft服务器通过适配器插件连接到独立服务器。

配置示例：
```yaml
# 每个Minecraft服务器的适配器配置 (config.yml)
voice-server:
  host: "voice.example.com"
  port: 24454
  api-endpoint: "http://voice.example.com:8080"
  auth-token: "shared-secret-token"

server-name: "survival"  # 区分不同的服务器
```

## 3. Minecraft 适配器配置

### 3.1 Bukkit/Spigot/Paper 插件配置

编辑 `plugins/VoiceChatAdapter/config.yml`:

```yaml
# 语音服务器配置
voice-server:
  host: "voice.example.com"
  port: 24454
  api-endpoint: "http://voice.example.com:8080"
  auth-token: "your-secret-token-here"

# 同步配置
sync:
  position-interval: 1000
  permission-interval: 5000
  sync-on-move: true
  min-move-distance: 1.0

# 服务器标识
server-name: "survival"
```

## 4. 部署场景示例

### 4.1 单服务器部署

```
┌─────────────────────────────────────┐
│  服务器 (*************)              │
│  ┌─────────────────┐                │
│  │ Minecraft       │                │
│  │ Port: 25565     │                │
│  │ Config:         │                │
│  │ standalone_host │                │
│  │ = "localhost"   │                │
│  └─────────────────┘                │
│  ┌─────────────────┐                │
│  │ Voice Server    │                │
│  │ UDP: 24454      │                │
│  │ HTTP: 8080      │                │
│  └─────────────────┘                │
└─────────────────────────────────────┘
```

**Minecraft 配置**:
```toml
use_standalone_voice_server = true
standalone_voice_host = "localhost"
standalone_voice_port = 24454
```

### 4.2 分离部署

```
┌─────────────────┐    ┌─────────────────┐
│ Minecraft       │    │ Voice Server    │
│ *************   │    │ *************   │
│ Port: 25565     │    │ UDP: 24454      │
│                 │    │ HTTP: 8080      │
└─────────────────┘    └─────────────────┘
```

**Minecraft 配置**:
```toml
use_standalone_voice_server = true
standalone_voice_host = "*************"
standalone_voice_port = 24454
```

**语音服务器配置**:
```yaml
server:
  host: "0.0.0.0"  # 监听所有接口
  port: 24454

api:
  host: "0.0.0.0"
  port: 8080
```

### 4.3 公网部署

```
Internet
    │
    ▼
┌─────────────────┐    ┌─────────────────┐
│ Minecraft       │    │ Voice Server    │
│ mc.example.com  │    │ voice.example.com│
│ Port: 25565     │    │ UDP: 24454      │
│                 │    │ HTTP: 8080      │
└─────────────────┘    └─────────────────┘
```

**Minecraft 配置**:
```toml
use_standalone_voice_server = true
standalone_voice_host = "voice.example.com"
standalone_voice_port = 24454
```

**防火墙配置**:
```bash
# 语音服务器
sudo ufw allow 24454/udp
sudo ufw allow 8080/tcp

# Minecraft服务器
sudo ufw allow 25565/tcp
```

### 4.4 负载均衡部署

```
┌─────────────────┐    ┌─────────────────┐
│ Minecraft 1     │    │ Minecraft 2     │
│ mc1.example.com │    │ mc2.example.com │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────────────────┼───────────────────────┐
                                 │                       │
                    ┌─────────────────┐                 │
                    │ Voice Server    │                 │
                    │ voice.example.com│                │
                    │ UDP: 24454      │                 │
                    └─────────────────┘                 │
```

**两个Minecraft服务器都使用相同配置**:
```toml
use_standalone_voice_server = true
standalone_voice_host = "voice.example.com"
standalone_voice_port = 24454
```

## 5. 故障排除配置

### 5.1 调试模式

**Minecraft服务器** (logback.xml):
```xml
<logger name="de.maxhenkel.voicechat" level="DEBUG" />
```

**语音服务器** (logback.xml):
```xml
<logger name="de.maxhenkel.voicechat.standalone" level="DEBUG" />
```

### 5.2 网络测试

```bash
# 测试UDP端口
nc -u voice.example.com 24454

# 测试HTTP API
curl http://voice.example.com:8080/health

# 测试认证
curl -H "Authorization: Bearer your-token" \
     http://voice.example.com:8080/api/status
```

### 5.3 常见问题配置

**问题**: 客户端连接超时
**解决**: 检查防火墙和网络配置
```yaml
# 增加超时时间
voice:
  keep_alive: 2000
security:
  auth_timeout: 60000
```

**问题**: 高延迟
**解决**: 优化网络配置
```yaml
voice:
  mtu_size: 512  # 减小MTU
  keep_alive: 500  # 减少心跳间隔
```

## 6. 迁移指南

### 6.1 从集成模式迁移到独立模式

1. **部署独立语音服务器**
2. **更新Minecraft服务器配置**:
   ```toml
   # 从
   use_standalone_voice_server = false
   
   # 改为
   use_standalone_voice_server = true
   standalone_voice_host = "voice.example.com"
   standalone_voice_port = 24454
   ```
3. **重启Minecraft服务器**
4. **验证客户端连接**

### 6.2 回滚到集成模式

```toml
# 简单设置为false即可回滚
use_standalone_voice_server = false
```

## 7. 性能调优配置

### 7.1 高并发配置

```yaml
# 语音服务器
voice:
  mtu_size: 1024
  keep_alive: 500
  
# JVM参数
JAVA_OPTS: "-Xmx2G -XX:+UseG1GC -XX:MaxGCPauseMillis=100"
```

### 7.2 低延迟配置

```yaml
voice:
  codec: "RESTRICTED_LOWDELAY"
  mtu_size: 512
  keep_alive: 250
```

这些配置示例涵盖了各种部署场景，可以根据实际需求进行调整。
