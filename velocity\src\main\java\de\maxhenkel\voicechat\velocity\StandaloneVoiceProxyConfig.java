package de.maxhenkel.voicechat.velocity;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Properties;

/**
 * 独立语音服务器代理配置
 */
public class StandaloneVoiceProxyConfig {
    
    private static final String CONFIG_FILE = "standalone-voice.properties";
    
    private final boolean standaloneMode;
    private final String voiceServerHost;
    private final int voiceServerPort;
    
    public StandaloneVoiceProxyConfig(boolean standaloneMode, String voiceServerHost, int voiceServerPort) {
        this.standaloneMode = standaloneMode;
        this.voiceServerHost = voiceServerHost;
        this.voiceServerPort = voiceServerPort;
    }
    
    /**
     * 从配置目录加载配置
     */
    public static StandaloneVoiceProxyConfig load(Path dataDirectory) throws IOException {
        Path configFile = dataDirectory.resolve(CONFIG_FILE);
        
        if (!Files.exists(configFile)) {
            // 创建默认配置文件
            createDefaultConfig(configFile);
        }
        
        Properties props = new Properties();
        props.load(Files.newInputStream(configFile));
        
        boolean standaloneMode = Boolean.parseBoolean(props.getProperty("standalone.enabled", "false"));
        String voiceServerHost = props.getProperty("standalone.voice_server_host", "localhost");
        int voiceServerPort = Integer.parseInt(props.getProperty("standalone.voice_server_port", "24454"));
        
        return new StandaloneVoiceProxyConfig(standaloneMode, voiceServerHost, voiceServerPort);
    }
    
    /**
     * 创建默认配置文件
     */
    private static void createDefaultConfig(Path configFile) throws IOException {
        String defaultConfig = """
                # Standalone Voice Server Configuration for Velocity Proxy
                # 
                # Set to true to enable standalone voice server mode
                # When enabled, all voice traffic will be directed to a single standalone voice server
                # instead of proxying to individual backend servers
                standalone.enabled=false
                
                # Standalone voice server host
                standalone.voice_server_host=localhost
                
                # Standalone voice server UDP port
                standalone.voice_server_port=24454
                
                # Note: When standalone mode is enabled, the voice chat proxy functionality is disabled
                # and players will connect directly to the standalone voice server
                """;
        
        Files.createDirectories(configFile.getParent());
        Files.writeString(configFile, defaultConfig);
    }
    
    /**
     * 修改SecretPacket，将语音服务器地址改为独立语音服务器地址
     */
    public static ByteBuffer patchSecretPacket(ByteBuffer originalMessage, StandaloneVoiceProxyConfig config) {
        // 重置ByteBuffer位置
        originalMessage.rewind();
        
        // 读取原始数据
        byte[] originalData = new byte[originalMessage.remaining()];
        originalMessage.get(originalData);
        
        // 创建新的ByteBuffer来构建修改后的数据包
        ByteBuffer newBuffer = ByteBuffer.allocate(originalData.length + 64); // 预留额外空间
        
        try {
            // 复制前面的数据（玩家UUID、密钥等）
            // 这里需要根据SecretPacket的具体格式来解析和重构
            // 简化实现：直接替换端口信息
            
            // 假设端口信息在特定位置，这里需要根据实际的SecretPacket格式调整
            newBuffer.put(originalData, 0, originalData.length - 4); // 复制除了最后4字节（端口）的所有数据
            newBuffer.putInt(config.getVoiceServerPort()); // 替换为独立语音服务器端口
            
            newBuffer.flip();
            return newBuffer;
            
        } catch (Exception e) {
            // 如果修改失败，返回原始数据
            return ByteBuffer.wrap(originalData);
        }
    }
    
    // Getters
    public boolean isStandaloneMode() {
        return standaloneMode;
    }
    
    public String getVoiceServerHost() {
        return voiceServerHost;
    }
    
    public int getVoiceServerPort() {
        return voiceServerPort;
    }
    
    @Override
    public String toString() {
        return "StandaloneVoiceProxyConfig{" +
                "standaloneMode=" + standaloneMode +
                ", voiceServerHost='" + voiceServerHost + '\'' +
                ", voiceServerPort=" + voiceServerPort +
                '}';
    }
}
