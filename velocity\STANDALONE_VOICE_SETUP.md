# Velocity独立语音服务器配置指南

本指南说明如何配置Velocity代理以支持独立语音服务器模式。

## 🎯 架构概述

### 传统代理模式
```
客户端 ↔ Velocity代理 ↔ 后端服务器(带语音插件)
```

### 独立语音服务器模式
```
客户端 ↔ Velocity代理 ↔ 后端服务器(带适配器插件)
客户端 ↗ 独立语音服务器
```

## 📋 配置步骤

### 1. 安装插件

在Velocity代理服务器上安装修改后的Simple Voice Chat插件。

### 2. 配置Velocity插件

在 `plugins/voicechat/` 目录下创建 `standalone-voice.properties` 文件：

```properties
# 启用独立语音服务器模式
standalone.enabled=true

# 独立语音服务器地址
standalone.voice_server_host=your-voice-server-ip

# 独立语音服务器UDP端口
standalone.voice_server_port=24454
```

### 3. 配置后端服务器

在每个后端Minecraft服务器上：

1. **安装语音聊天适配器插件** (minecraft-adapter)
2. **配置适配器** 连接到独立语音服务器：

```yaml
# minecraft-adapter/config.yml
voice-server:
  host: "your-voice-server-ip"
  port: 24454
  api-endpoint: "http://your-voice-server-ip:8080"
  auth-token: "your-secret-token"

server-name: "survival"  # 每个服务器使用不同的名称
```

### 4. 启动独立语音服务器

确保独立语音服务器正在运行并监听正确的端口。

## 🔧 工作原理

### 独立模式下的消息处理

1. **客户端请求语音连接** → Velocity拦截SecretPacket
2. **修改服务器信息** → 将语音服务器地址改为独立服务器
3. **客户端直连语音服务器** → 绕过Velocity代理
4. **后端服务器同步数据** → 通过适配器插件同步玩家位置等信息

### 配置验证

启动后检查日志：

```
[INFO] Standalone voice server mode enabled - connecting to your-voice-server-ip:24454
```

## 🚨 注意事项

1. **端口开放**: 确保独立语音服务器的UDP端口对客户端开放
2. **网络配置**: 客户端需要能够直接访问独立语音服务器
3. **防火墙**: 配置防火墙允许UDP流量通过
4. **负载均衡**: 如果使用负载均衡，确保UDP流量正确路由

## 🔄 切换模式

### 从代理模式切换到独立模式

1. 修改 `standalone.enabled=true`
2. 重启Velocity代理
3. 在后端服务器安装适配器插件

### 从独立模式切换回代理模式

1. 修改 `standalone.enabled=false`
2. 重启Velocity代理
3. 在后端服务器安装原版语音聊天插件

## 🐛 故障排除

### 客户端无法连接语音服务器

1. 检查独立语音服务器是否运行
2. 验证端口配置是否正确
3. 检查网络连通性
4. 查看Velocity日志中的错误信息

### 玩家位置同步问题

1. 确认后端服务器安装了适配器插件
2. 检查适配器配置中的API端点
3. 验证认证令牌是否匹配

### 配置重载

使用 `/velocity reload` 命令重新加载配置，无需重启整个代理。
