import groovy.json.JsonSlurper

buildscript {
    dependencies {
        classpath "info.u-team.curse_gradle_uploader:curse_gradle_uploader:${curse_gradle_uploader_version}"
    }
}

plugins {
    id 'java'
}

apply plugin: 'com.gradleup.shadow'
apply plugin: 'com.modrinth.minotaur'
apply plugin: 'info.u_team.curse_gradle_uploader'
apply plugin: 'io.papermc.hangar-publish-plugin'
apply plugin: 'xyz.jpenilla.run-paper'

apply from: "https://raw.githubusercontent.com/henkelmax/mod-gradle-scripts/${mod_gradle_script_version}/mod.gradle"

java.toolchain.languageVersion = JavaLanguageVersion.of(21)

repositories {
    mavenCentral()
    maven { url = 'https://libraries.minecraft.net/' }
    maven { url = 'https://repo.extendedclip.com/content/repositories/placeholderapi/' }
    maven { url = 'https://repo.viaversion.com/' }
}

dependencies {
    implementation "org.json:json:${json_version}"
    shadow "org.json:json:${json_version}"

    implementation "me.lucko:commodore:${commodore_version}"
    shadow "me.lucko:commodore:${commodore_version}"

    implementation "org.concentus:Concentus:${concentus_version}"
    shadow "org.concentus:Concentus:${concentus_version}"

    compileOnly "me.clip:placeholderapi:${placeholder_api_version}"

    compileOnly "com.viaversion:viaversion-bukkit:${viaversion_version}"
    compileOnly "com.viaversion:viaversion-common:${viaversion_version}"
}

processResources {
    filesMatching('plugin.yml') {
        expand 'version': version
    }
}

sourceSets {
    template {
        java {
            srcDir 'src/template/java'
        }
    }
    main {
        compileClasspath += sourceSets.template.output
        runtimeClasspath += sourceSets.template.output
    }
}

tasks.register('generateBuildConstants', Copy) {
    from 'src/template/java'
    into "${layout.buildDirectory.asFile.get()}/generated/java"
    expand 'compatibility_version': voicechat_compatibility_version,
            'target_bukkit_version': bukkit_version,
            'plugin_name': mod_display_name
}
compileJava.dependsOn generateBuildConstants

tasks.register('generateFallbackTranslations') {
    def inputFile = project(":common").file('src/main/resources/assets/voicechat/lang/en_us.json')
    def outputFile = file("${layout.buildDirectory.asFile.get()}/generated/java/de/maxhenkel/voicechat/FallbackTranslations.java")
    inputs.file inputFile
    outputs.file outputFile

    doLast {
        def json = new JsonSlurper().parse(inputFile)
        outputFile.getParentFile().mkdirs()

        def stringBuilder = new StringBuilder()
        json.each { key, value ->
            def escapedKey = key.toString().replace('\\', '\\\\').replace('\n', '\\n').replace('"', '\\"')
            def escapedValue = value.toString().replace('\\', '\\\\').replace('\n', '\\n').replace('"', '\\"')
            stringBuilder.append("        translations.put(\"${escapedKey}\", \"${escapedValue}\");\n")
        }

        outputFile.text = """package de.maxhenkel.voicechat;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class FallbackTranslations {

    public static final Map<String, String> FALLBACK_TRANSLATIONS;

    static {
        Map<String, String> translations = new HashMap<>();
${stringBuilder.toString()}
        FALLBACK_TRANSLATIONS = Collections.unmodifiableMap(translations);
    }
}
"""
    }
}
compileJava.dependsOn generateFallbackTranslations

sourceSets.main.java {
    srcDir "${layout.buildDirectory.asFile.get()}/generated/java"
}

shadowJar {
    relocate 'org.json', 'de.maxhenkel.voicechat.org.json'
    relocate 'com.google.gson', 'de.maxhenkel.voicechat.gson'
    relocate 'me.lucko.commodore', 'de.maxhenkel.voicechat.commodore'
    relocate 'org.concentus', 'de.maxhenkel.voicechat.concentus'
    dependencies {
        exclude(dependency('com.mojang:brigadier:.*'))
        exclude(dependency('com.google.code.findbugs:jsr305:.*'))
    }
}

tasks.register('copyJarToServer') {
    doLast {
        delete {
            delete "${projectDir}/run/plugins/${archivesBaseName}-${version}.jar"
        }
        copy {
            from "${layout.buildDirectory.asFile.get()}/libs/${archivesBaseName}-${version}.jar"
            into "${projectDir}/run/plugins"
        }
    }
}

build.finalizedBy(copyJarToServer)

tasks.register('runBukkitServer') {
    doLast {
        javaexec {
            debug = true
            workingDir = "${projectDir}/run"
            main = '-jar';
            args = [
                    "${projectDir}/run/craftbukkit.jar",
                    'nogui'
            ]
        }
    }
}

tasks {
    runServer {
        minecraftVersion(minecraft_version)
    }
}

build.dependsOn(shadowJar)
runBukkitServer.dependsOn(build)
runServer.dependsOn(build)
