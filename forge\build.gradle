plugins {
    id 'net.minecraftforge.gradle' version "${forgegradle_version}"
    id 'org.spongepowered.mixin' version "${mixingradle_version}"
}

apply plugin: 'com.gradleup.shadow'
apply plugin: 'com.matthewprenger.cursegradle'
apply plugin: 'com.modrinth.minotaur'
apply plugin: 'mod-update'

apply from: "https://raw.githubusercontent.com/henkelmax/mod-gradle-scripts/${mod_gradle_script_version}/mod.gradle"

processResources {
    filesMatching('**/*.toml') {
        expand 'mod_version': mod_version,
                'forge_dependency': forge_dependency,
                'minecraft_version': minecraft_version,
                'cloth_config_version': cloth_config_version
    }
}

repositories {
    maven { url = 'https://maven.shedaniel.me/' }
}

dependencies {
    implementation 'org.jetbrains:annotations:23.0.0'

    minecraftLibrary "org.concentus:Concentus:${concentus_version}"
    shadow "org.concentus:Concentus:${concentus_version}"

    minecraftLibrary "de.maxhenkel.opus4j:opus4j:${opus4j_version}"
    shadow "de.maxhenkel.opus4j:opus4j:${opus4j_version}"

    minecraftLibrary "de.maxhenkel.rnnoise4j:rnnoise4j:${rnnoise4j_version}"
    shadow "de.maxhenkel.rnnoise4j:rnnoise4j:${rnnoise4j_version}"

    minecraftLibrary "de.maxhenkel.lame4j:lame4j:${lame4j_version}"
    shadow "de.maxhenkel.lame4j:lame4j:${lame4j_version}"

    implementation('net.sf.jopt-simple:jopt-simple:5.0.4') { version { strictly '5.0.4' } }
}

tasks.register('generateJava', Copy) {
    from project(':common').file('src/template/java')
    into "${layout.buildDirectory.asFile.get()}/generated/java"
    expand 'compatibility_version': voicechat_compatibility_version,
            'minecraft_version': minecraft_version
}
sourceSets.main.java {
    srcDir "${layout.buildDirectory.asFile.get()}/generated/java"
}
compileJava.dependsOn generateJava

shadowJar {
    relocate 'org.concentus', 'de.maxhenkel.voicechat.concentus'
    exclude 'natives/*-x86/*'
}