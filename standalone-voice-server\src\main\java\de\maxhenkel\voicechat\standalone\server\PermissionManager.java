package de.maxhenkel.voicechat.standalone.server;

import de.maxhenkel.voicechat.standalone.model.Permission;
import de.maxhenkel.voicechat.standalone.model.PlayerData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 独立语音服务器权限管理器
 */
public class PermissionManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionManager.class);
    
    // 玩家权限缓存 <PlayerUUID, <Permission, Boolean>>
    private final Map<UUID, Map<Permission, Boolean>> playerPermissions = new ConcurrentHashMap<>();
    
    // 默认权限设置
    private final Map<Permission, Boolean> defaultPermissions = new EnumMap<>(Permission.class);
    
    // 权限组 <GroupName, Permissions>
    private final Map<String, Set<Permission>> permissionGroups = new ConcurrentHashMap<>();
    
    // 玩家权限组 <PlayerUUID, GroupNames>
    private final Map<UUID, Set<String>> playerGroups = new ConcurrentHashMap<>();
    
    public PermissionManager() {
        initializeDefaultPermissions();
        initializeDefaultGroups();
    }
    
    /**
     * 初始化默认权限
     */
    private void initializeDefaultPermissions() {
        // 基本权限默认开启
        defaultPermissions.put(Permission.LISTEN, true);
        defaultPermissions.put(Permission.SPEAK, true);
        defaultPermissions.put(Permission.GROUPS, true);
        defaultPermissions.put(Permission.CREATE_GROUP, true);
        defaultPermissions.put(Permission.JOIN_GROUP, true);
        
        // 管理权限默认关闭
        defaultPermissions.put(Permission.MANAGE_GROUP, false);
        defaultPermissions.put(Permission.ADMIN, false);
        defaultPermissions.put(Permission.RECORD, false);
        defaultPermissions.put(Permission.SPECTATOR_INTERACTION, false);
        defaultPermissions.put(Permission.SPECTATOR_POSSESSION, false);
        defaultPermissions.put(Permission.CROSS_WORLD, false);
        defaultPermissions.put(Permission.CROSS_SERVER, false);
        
        LOGGER.info("Initialized default permissions");
    }
    
    /**
     * 初始化默认权限组
     */
    private void initializeDefaultGroups() {
        // 管理员组
        Set<Permission> adminPermissions = EnumSet.allOf(Permission.class);
        permissionGroups.put("admin", adminPermissions);
        
        // 版主组
        Set<Permission> moderatorPermissions = EnumSet.of(
            Permission.LISTEN, Permission.SPEAK, Permission.GROUPS,
            Permission.CREATE_GROUP, Permission.JOIN_GROUP, Permission.MANAGE_GROUP,
            Permission.RECORD, Permission.CROSS_WORLD
        );
        permissionGroups.put("moderator", moderatorPermissions);
        
        // 普通用户组
        Set<Permission> userPermissions = EnumSet.of(
            Permission.LISTEN, Permission.SPEAK, Permission.GROUPS,
            Permission.CREATE_GROUP, Permission.JOIN_GROUP
        );
        permissionGroups.put("user", userPermissions);
        
        LOGGER.info("Initialized default permission groups: {}", permissionGroups.keySet());
    }
    
    /**
     * 检查玩家是否有指定权限
     */
    public boolean hasPermission(UUID playerUuid, Permission permission) {
        if (playerUuid == null || permission == null) {
            return false;
        }
        
        // 检查玩家特定权限
        Map<Permission, Boolean> playerPerms = playerPermissions.get(playerUuid);
        if (playerPerms != null && playerPerms.containsKey(permission)) {
            return playerPerms.get(permission);
        }
        
        // 检查权限组
        Set<String> groups = playerGroups.get(playerUuid);
        if (groups != null) {
            for (String groupName : groups) {
                Set<Permission> groupPerms = permissionGroups.get(groupName);
                if (groupPerms != null && groupPerms.contains(permission)) {
                    return true;
                }
            }
        }
        
        // 返回默认权限
        return defaultPermissions.getOrDefault(permission, false);
    }
    
    /**
     * 检查玩家是否有指定权限（使用PlayerData）
     */
    public boolean hasPermission(PlayerData player, Permission permission) {
        if (player == null) {
            return false;
        }
        return hasPermission(player.getUuid(), permission);
    }
    
    /**
     * 设置玩家权限
     */
    public void setPlayerPermission(UUID playerUuid, Permission permission, boolean granted) {
        if (playerUuid == null || permission == null) {
            return;
        }
        
        playerPermissions.computeIfAbsent(playerUuid, k -> new ConcurrentHashMap<>())
                         .put(permission, granted);
        
     //   LOGGER.debug("Set permission {} = {} for player {}", permission, granted, playerUuid);
    }
    
    /**
     * 移除玩家权限（恢复到默认值）
     */
    public void removePlayerPermission(UUID playerUuid, Permission permission) {
        if (playerUuid == null || permission == null) {
            return;
        }
        
        Map<Permission, Boolean> playerPerms = playerPermissions.get(playerUuid);
        if (playerPerms != null) {
            playerPerms.remove(permission);
            if (playerPerms.isEmpty()) {
                playerPermissions.remove(playerUuid);
            }
        }
        
        LOGGER.debug("Removed permission {} for player {}", permission, playerUuid);
    }
    
    /**
     * 将玩家添加到权限组
     */
    public void addPlayerToGroup(UUID playerUuid, String groupName) {
        if (playerUuid == null || groupName == null || !permissionGroups.containsKey(groupName)) {
            return;
        }
        
        playerGroups.computeIfAbsent(playerUuid, k -> ConcurrentHashMap.newKeySet())
                   .add(groupName);
        
        LOGGER.info("Added player {} to permission group {}", playerUuid, groupName);
    }
    
    /**
     * 从权限组移除玩家
     */
    public void removePlayerFromGroup(UUID playerUuid, String groupName) {
        if (playerUuid == null || groupName == null) {
            return;
        }
        
        Set<String> groups = playerGroups.get(playerUuid);
        if (groups != null) {
            groups.remove(groupName);
            if (groups.isEmpty()) {
                playerGroups.remove(playerUuid);
            }
        }
        
        LOGGER.info("Removed player {} from permission group {}", playerUuid, groupName);
    }
    
    /**
     * 获取玩家的所有权限组
     */
    public Set<String> getPlayerGroups(UUID playerUuid) {
        return playerGroups.getOrDefault(playerUuid, Collections.emptySet());
    }
    
    /**
     * 获取玩家的所有权限
     */
    public Map<Permission, Boolean> getPlayerPermissions(UUID playerUuid) {
        Map<Permission, Boolean> result = new EnumMap<>(Permission.class);
        
        // 添加默认权限
        result.putAll(defaultPermissions);
        
        // 添加权限组权限
        Set<String> groups = playerGroups.get(playerUuid);
        if (groups != null) {
            for (String groupName : groups) {
                Set<Permission> groupPerms = permissionGroups.get(groupName);
                if (groupPerms != null) {
                    for (Permission perm : groupPerms) {
                        result.put(perm, true);
                    }
                }
            }
        }
        
        // 添加玩家特定权限（覆盖前面的设置）
        Map<Permission, Boolean> playerPerms = playerPermissions.get(playerUuid);
        if (playerPerms != null) {
            result.putAll(playerPerms);
        }
        
        return result;
    }
    
    /**
     * 清理玩家权限数据
     */
    public void clearPlayerData(UUID playerUuid) {
        if (playerUuid == null) {
            return;
        }
        
        playerPermissions.remove(playerUuid);
        playerGroups.remove(playerUuid);
        
        LOGGER.debug("Cleared permission data for player {}", playerUuid);
    }
    
    /**
     * 创建权限组
     */
    public void createPermissionGroup(String groupName, Set<Permission> permissions) {
        if (groupName == null || permissions == null) {
            return;
        }
        
        permissionGroups.put(groupName, new HashSet<>(permissions));
        LOGGER.info("Created permission group {} with {} permissions", groupName, permissions.size());
    }
    
    /**
     * 删除权限组
     */
    public void deletePermissionGroup(String groupName) {
        if (groupName == null || "admin".equals(groupName) || "user".equals(groupName)) {
            return; // 不能删除默认组
        }
        
        permissionGroups.remove(groupName);
        
        // 从所有玩家中移除该组
        playerGroups.values().forEach(groups -> groups.remove(groupName));
        
        LOGGER.info("Deleted permission group {}", groupName);
    }
    
    /**
     * 获取所有权限组
     */
    public Map<String, Set<Permission>> getAllPermissionGroups() {
        return new HashMap<>(permissionGroups);
    }
    
    /**
     * 获取统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalPlayers", playerPermissions.size());
        stats.put("totalGroups", permissionGroups.size());
        stats.put("playersWithCustomPermissions", playerPermissions.size());
        stats.put("playersInGroups", playerGroups.size());
        
        Map<String, Integer> groupMemberCounts = new HashMap<>();
        for (Map.Entry<String, Set<Permission>> entry : permissionGroups.entrySet()) {
            String groupName = entry.getKey();
            long memberCount = playerGroups.values().stream()
                    .mapToLong(groups -> groups.contains(groupName) ? 1 : 0)
                    .sum();
            groupMemberCounts.put(groupName, (int) memberCount);
        }
        stats.put("groupMemberCounts", groupMemberCounts);
        
        return stats;
    }
}
