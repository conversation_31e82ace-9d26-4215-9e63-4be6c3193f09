# 连接可靠性改进

## 🎯 解决的问题

### 1. ❌ "no secret" 问题
- **问题**：独立服务端显示玩家 "no secret"，导致语音连接失败
- **原因**：密钥验证后立即删除，客户端重连时找不到密钥

### 2. ❌ 依赖性问题
- **问题**：minecraft-adapter 对独立服务端依赖性强，连接断开后无法自动恢复
- **原因**：缺乏断线重连机制和异常处理

## ✅ 解决方案

### 1. 修复密钥管理机制

#### 🔧 独立服务端密钥管理改进

**修改文件：** `standalone-voice-server/src/main/java/de/maxhenkel/voicechat/standalone/server/ConnectionManager.java`

**关键修改：**
```java
public boolean validateSecret(UUID playerUuid, UUID secret) {
    UUID storedSecret = playerSecrets.get(playerUuid);
    if (storedSecret == null) {
        LOGGER.warn("No secret found for player {}", playerUuid);
        return false;
    }
    
    boolean valid = storedSecret.equals(secret);
    if (valid) {
        LOGGER.debug("Secret validated for player {}", playerUuid);
        // ✅ 不删除密钥，允许重连使用同一个密钥
    } else {
        LOGGER.warn("Invalid secret for player {}", playerUuid);
    }
    
    return valid;
}
```

**新增功能：**
- **密钥重用**：验证成功后不删除密钥，支持客户端重连
- **智能清理**：只在玩家离线时清理密钥
- **定期清理**：每5分钟清理一次离线玩家的密钥

#### 🔧 定期清理任务

**修改文件：** `standalone-voice-server/src/main/java/de/maxhenkel/voicechat/standalone/server/VoiceServer.java`

```java
// 密钥清理任务（每5分钟执行一次）
scheduler.scheduleAtFixedRate(this::cleanupSecrets, 300000, 300000, TimeUnit.MILLISECONDS);

private void cleanupSecrets() {
    try {
        connectionManager.cleanupOfflinePlayerSecrets();
        LOGGER.debug("Cleaned up offline player secrets");
    } catch (Exception e) {
        LOGGER.warn("Failed to cleanup secrets", e);
    }
}
```

### 2. 实现断线重连机制

#### 🔧 新增 ConnectionManager

**新文件：** `minecraft-adapter/src/main/java/de/maxhenkel/voicechat/adapter/network/ConnectionManager.java`

**核心功能：**
- **连接状态监控**：实时监控与独立服务端的连接状态
- **自动重连**：连接断开时自动尝试重连
- **指数退避**：重连失败时使用指数退避策略
- **健康检查**：定期检查连接健康状态
- **异常处理**：全面的异常捕获和处理

**重连配置：**
```java
private static final int MAX_RETRY_ATTEMPTS = 10;        // 最大重试次数
private static final long INITIAL_RETRY_DELAY = 5000;    // 初始重试延迟：5秒
private static final long MAX_RETRY_DELAY = 300000;      // 最大重试延迟：5分钟
private static final long CONNECTION_TIMEOUT = 30000;    // 连接超时：30秒
private static final long HEALTH_CHECK_INTERVAL = 30000; // 健康检查间隔：30秒
```

#### 🔧 HTTP请求重试机制

**修改文件：** `minecraft-adapter/src/main/java/de/maxhenkel/voicechat/adapter/network/VoiceServerClient.java`

**新增功能：**
```java
/**
 * 测试与独立服务端的连接
 */
public boolean testConnection() {
    try {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/health")
                .header("Authorization", authHeader)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            boolean isHealthy = response.isSuccessful();
            if (isHealthy != connected) {
                connected = isHealthy;
                if (isHealthy) {
                    LOGGER.info("Connection to voice server restored");
                } else {
                    LOGGER.warn("Connection to voice server lost");
                }
            }
            return isHealthy;
        }
    } catch (Exception e) {
        if (connected) {
            LOGGER.warn("Connection test failed: {}", e.getMessage());
            connected = false;
        }
        return false;
    }
}

/**
 * 安全执行HTTP请求，带有异常处理和重试
 */
private <T> T executeWithRetry(String operation, HttpRequestExecutor<T> executor) throws Exception {
    Exception lastException = null;
    
    for (int attempt = 1; attempt <= 3; attempt++) {
        try {
            return executor.execute();
        } catch (Exception e) {
            lastException = e;
            LOGGER.warn("{} failed (attempt {}/3): {}", operation, attempt, e.getMessage());
            
            if (attempt < 3) {
                try {
                    Thread.sleep(1000 * attempt); // 递增延迟
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new Exception("Operation interrupted", ie);
                }
            }
        }
    }
    
    throw new Exception(operation + " failed after 3 attempts", lastException);
}
```

## 🚀 改进效果

### ✅ 密钥管理改进
- **解决 "no secret" 问题**：玩家重连时可以使用相同密钥
- **智能清理**：只清理真正离线的玩家密钥
- **内存优化**：定期清理避免内存泄漏

### ✅ 连接可靠性提升
- **自动重连**：网络问题时自动恢复连接
- **健康监控**：主动发现连接问题
- **异常处理**：全面的错误处理和恢复机制
- **状态透明**：清晰的连接状态信息

### ✅ 用户体验改善
- **无感知重连**：用户无需手动重启插件
- **快速恢复**：网络恢复后快速重新连接
- **状态反馈**：管理员可以查看连接状态

## 📋 使用方法

### 1. 查看连接状态
```
/voicechat status
```

### 2. 手动重连
```
/voicechat reconnect
```

### 3. 重载配置
```
/voicechat reload
```

## 🔧 配置说明

### 重连参数（代码中配置）
- `MAX_RETRY_ATTEMPTS`: 最大重试次数（默认：10次）
- `INITIAL_RETRY_DELAY`: 初始重试延迟（默认：5秒）
- `MAX_RETRY_DELAY`: 最大重试延迟（默认：5分钟）
- `HEALTH_CHECK_INTERVAL`: 健康检查间隔（默认：30秒）

### 密钥清理参数
- 清理间隔：5分钟
- 清理条件：玩家离线且不在连接列表中

## 🎯 技术特点

### 1. 指数退避重连
- 第1次失败：5秒后重试
- 第2次失败：10秒后重试
- 第3次失败：20秒后重试
- ...
- 最大延迟：5分钟

### 2. 健康检查机制
- 定期发送 `/api/health` 请求
- 检测连接状态变化
- 自动触发重连流程

### 3. 异常处理策略
- HTTP请求重试（最多3次）
- 网络异常自动恢复
- 线程安全的状态管理

## 🔍 日志监控

### 连接状态日志
```
[INFO] Connection to voice server restored
[WARN] Connection to voice server lost
[INFO] Scheduling reconnection in 10 seconds
```

### 密钥管理日志
```
[DEBUG] Secret validated for player <uuid>
[DEBUG] Cleaning up secret for offline player: <uuid>
[DEBUG] Cleaned up offline player secrets
```

## 🎉 总结

这次改进大大提升了 minecraft-adapter 与独立服务端之间的连接可靠性：

1. **解决了 "no secret" 问题**：通过改进密钥管理机制
2. **实现了自动重连**：网络问题时自动恢复
3. **增强了异常处理**：全面的错误处理和恢复
4. **提升了用户体验**：无感知的连接管理

现在系统能够自动处理各种网络问题，确保语音聊天功能的稳定运行！
