package de.maxhenkel.voicechat.adapter.network;

import de.maxhenkel.voicechat.adapter.VoiceChatAdapterPlugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 连接管理器 - 处理与独立服务端的连接状态和断线重连
 */
public class ConnectionManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ConnectionManager.class);
    
    private final VoiceChatAdapterPlugin plugin;
    private final VoiceServerClient voiceServerClient;
    
    // 连接状态
    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    private final AtomicBoolean isReconnecting = new AtomicBoolean(false);
    private final AtomicLong lastSuccessfulConnection = new AtomicLong(0);
    private final AtomicInteger consecutiveFailures = new AtomicInteger(0);
    
    // 重连配置
    private static final int MAX_RETRY_ATTEMPTS = 10;
    private static final long INITIAL_RETRY_DELAY = 5000; // 5秒
    private static final long MAX_RETRY_DELAY = 300000; // 5分钟
    private static final long CONNECTION_TIMEOUT = 30000; // 30秒
    private static final long HEALTH_CHECK_INTERVAL = 30000; // 30秒
    private static final long KEEPALIVE_INTERVAL = 5 * 60 * 1000; // 5分钟

    // 任务
    private BukkitTask healthCheckTask;
    private BukkitTask reconnectTask;
    private BukkitTask keepaliveTask;
    
    public ConnectionManager(VoiceChatAdapterPlugin plugin, VoiceServerClient voiceServerClient) {
        this.plugin = plugin;
        this.voiceServerClient = voiceServerClient;
    }
    
    /**
     * 启动连接管理器
     */
    public void start() {
        LOGGER.info("Starting connection manager");
        
        // 初始连接
        attemptConnection();
        
        // 启动健康检查任务
        startHealthCheckTask();

        // 启动keepalive任务
        startKeepaliveTask();
    }
    
    /**
     * 停止连接管理器
     */
    public void stop() {
        LOGGER.info("Stopping connection manager");
        
        // 停止所有任务
        stopHealthCheckTask();
        stopReconnectTask();
        stopKeepaliveTask();
        
        // 重置状态
        isConnected.set(false);
        isReconnecting.set(false);
    }
    
    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return isConnected.get();
    }
    
    /**
     * 检查是否正在重连
     */
    public boolean isReconnecting() {
        return isReconnecting.get();
    }
    
    /**
     * 获取连续失败次数
     */
    public int getConsecutiveFailures() {
        return consecutiveFailures.get();
    }
    
    /**
     * 获取最后成功连接时间
     */
    public long getLastSuccessfulConnection() {
        return lastSuccessfulConnection.get();
    }
    
    /**
     * 尝试连接到独立服务端
     */
    private void attemptConnection() {
        if (isReconnecting.get()) {
            return; // 已经在重连中
        }
        
        try {
            LOGGER.debug("Attempting connection to voice server");
            
            // 测试连接
            if (voiceServerClient.testConnection()) {
                onConnectionSuccess();
            } else {
                onConnectionFailure(new Exception("Connection test failed"));
            }
            
        } catch (Exception e) {
            onConnectionFailure(e);
        }
    }
    
    /**
     * 连接成功处理
     */
    private void onConnectionSuccess() {
        LOGGER.info("Successfully connected to voice server");
        
        isConnected.set(true);
        isReconnecting.set(false);
        lastSuccessfulConnection.set(System.currentTimeMillis());
        consecutiveFailures.set(0);
        
        // 停止重连任务
        stopReconnectTask();
    }
    
    /**
     * 连接失败处理
     */
    private void onConnectionFailure(Exception e) {
        int failures = consecutiveFailures.incrementAndGet();
        isConnected.set(false);
        
        LOGGER.warn("Connection to voice server failed (attempt {}): {}", failures, e.getMessage());
        
        if (failures >= MAX_RETRY_ATTEMPTS) {
            LOGGER.error("Max retry attempts reached ({}), stopping reconnection attempts", MAX_RETRY_ATTEMPTS);
            isReconnecting.set(false);
            return;
        }
        
        // 启动重连任务
        scheduleReconnect();
    }
    
    /**
     * 调度重连任务
     */
    private void scheduleReconnect() {
        if (isReconnecting.get()) {
            return; // 已经在重连中
        }
        
        isReconnecting.set(true);
        
        // 计算重连延迟（指数退避）
        long delay = Math.min(INITIAL_RETRY_DELAY * (1L << Math.min(consecutiveFailures.get() - 1, 6)), MAX_RETRY_DELAY);
        
        LOGGER.info("Scheduling reconnection in {} seconds", delay / 1000);
        
        reconnectTask = new BukkitRunnable() {
            @Override
            public void run() {
                attemptConnection();
            }
        }.runTaskLaterAsynchronously(plugin, delay / 50); // 转换为ticks
    }
    
    /**
     * 启动健康检查任务
     */
    private void startHealthCheckTask() {
        healthCheckTask = new BukkitRunnable() {
            @Override
            public void run() {
                performHealthCheck();
            }
        }.runTaskTimerAsynchronously(plugin, HEALTH_CHECK_INTERVAL / 50, HEALTH_CHECK_INTERVAL / 50);
        
        LOGGER.debug("Started health check task");
    }
    
    /**
     * 执行健康检查
     */
    private void performHealthCheck() {
        if (!isConnected.get()) {
            return; // 已知未连接，跳过检查
        }
        
        try {
            if (!voiceServerClient.testConnection()) {
                LOGGER.warn("Health check failed - connection lost");
                onConnectionFailure(new Exception("Health check failed"));
            } else {
                LOGGER.debug("Health check passed");
            }
        } catch (Exception e) {
            LOGGER.warn("Health check failed with exception: {}", e.getMessage());
            onConnectionFailure(e);
        }
    }
    
    /**
     * 停止健康检查任务
     */
    private void stopHealthCheckTask() {
        if (healthCheckTask != null) {
            healthCheckTask.cancel();
            healthCheckTask = null;
            LOGGER.debug("Stopped health check task");
        }
    }
    
    /**
     * 停止重连任务
     */
    private void stopReconnectTask() {
        if (reconnectTask != null) {
            reconnectTask.cancel();
            reconnectTask = null;
            LOGGER.debug("Stopped reconnect task");
        }
    }
    
    /**
     * 手动触发重连
     */
    public void forceReconnect() {
        LOGGER.info("Force reconnection requested");
        
        isConnected.set(false);
        consecutiveFailures.set(0);
        
        stopReconnectTask();
        attemptConnection();
    }
    
    /**
     * 获取连接状态信息
     */
    public String getConnectionStatus() {
        if (isConnected.get()) {
            long uptime = System.currentTimeMillis() - lastSuccessfulConnection.get();
            return String.format("Connected (uptime: %d seconds)", uptime / 1000);
        } else if (isReconnecting.get()) {
            return String.format("Reconnecting (failures: %d/%d)", consecutiveFailures.get(), MAX_RETRY_ATTEMPTS);
        } else {
            return "Disconnected";
        }
    }

    /**
     * 启动keepalive任务
     */
    private void startKeepaliveTask() {
        if (keepaliveTask != null) {
            keepaliveTask.cancel();
        }

        // 每5分钟发送一次keepalive
        keepaliveTask = plugin.getServer().getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            try {
                String serverName = plugin.getAdapterConfig().getServerName();
                boolean success = voiceServerClient.sendKeepalive(serverName);

                if (!success) {
                    LOGGER.warn("Failed to send keepalive for server: {}", serverName);
                    // 如果keepalive失败，可能需要重新注册服务器
                    tryReregisterServer();
                }
            } catch (Exception e) {
                LOGGER.error("Error sending keepalive", e);
            }
        }, KEEPALIVE_INTERVAL / 50, KEEPALIVE_INTERVAL / 50); // 转换为ticks (50ms per tick)

        LOGGER.debug("Keepalive task started with interval: {} minutes", KEEPALIVE_INTERVAL / 60000);
    }

    /**
     * 停止keepalive任务
     */
    private void stopKeepaliveTask() {
        if (keepaliveTask != null) {
            keepaliveTask.cancel();
            keepaliveTask = null;
            LOGGER.debug("Keepalive task stopped");
        }
    }

    /**
     * 尝试重新注册服务器（当keepalive失败时）
     */
    private void tryReregisterServer() {
        try {
            String serverName = plugin.getAdapterConfig().getServerName();
            String host = plugin.getServer().getIp().isEmpty() ? "localhost" : plugin.getServer().getIp();
            int port = plugin.getServer().getPort();

            boolean success = voiceServerClient.registerServer(serverName, host, port);
            if (success) {
                LOGGER.info("Successfully re-registered server: {}", serverName);
            } else {
                LOGGER.warn("Failed to re-register server: {}", serverName);
            }
        } catch (Exception e) {
            LOGGER.warn("Error re-registering server: {}", e.getMessage());
        }
    }
}
