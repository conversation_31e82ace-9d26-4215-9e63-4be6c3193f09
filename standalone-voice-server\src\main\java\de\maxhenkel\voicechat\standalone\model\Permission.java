package de.maxhenkel.voicechat.standalone.model;

/**
 * 语音聊天权限枚举
 */
public enum Permission {
    /**
     * 监听语音权限
     */
    LISTEN("voicechat.listen"),

    /**
     * 基本语音聊天权限
     */
    SPEAK("voicechat.speak"),

    /**
     * 群组聊天权限
     */
    GROUPS("voicechat.groups"),

    /**
     * 创建群组权限
     */
    CREATE_GROUP("voicechat.groups.create"),

    /**
     * 加入群组权限
     */
    JOIN_GROUP("voicechat.groups.join"),

    /**
     * 管理群组权限（踢人、修改设置等）
     */
    MANAGE_GROUP("voicechat.groups.manage"),

    /**
     * 管理员权限
     */
    ADMIN("voicechat.admin"),

    /**
     * 录音权限
     */
    RECORD("voicechat.record"),

    /**
     * 旁观者交互权限
     */
    SPECTATOR_INTERACTION("voicechat.spectator.interaction"),

    /**
     * 旁观者附身权限
     */
    SPECTATOR_POSSESSION("voicechat.spectator.possession"),

    /**
     * 跨世界语音权限
     */
    CROSS_WORLD("voicechat.cross_world"),

    /**
     * 跨服务器语音权限
     */
    CROSS_SERVER("voicechat.cross_server");
    
    private final String permissionNode;
    
    Permission(String permissionNode) {
        this.permissionNode = permissionNode;
    }
    
    public String getPermissionNode() {
        return permissionNode;
    }
    
    /**
     * 从权限节点字符串获取权限枚举
     */
    public static Permission fromNode(String node) {
        for (Permission permission : values()) {
            if (permission.permissionNode.equals(node)) {
                return permission;
            }
        }
        return null;
    }

    /**
     * 检查是否为群组相关权限
     */
    public boolean isGroupPermission() {
        return this == GROUPS || this == CREATE_GROUP || this == JOIN_GROUP || this == MANAGE_GROUP;
    }

    /**
     * 检查是否为管理员权限
     */
    public boolean isAdminPermission() {
        return this == ADMIN || this == MANAGE_GROUP;
    }

    /**
     * 检查是否为跨边界权限（跨世界或跨服务器）
     */
    public boolean isCrossBoundaryPermission() {
        return this == CROSS_WORLD || this == CROSS_SERVER;
    }

    @Override
    public String toString() {
        return permissionNode;
    }
}
