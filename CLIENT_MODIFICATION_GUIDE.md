# 客户端连接修改指南

本文档描述了如何修改 Simple Voice Chat 客户端以支持连接到独立的语音服务器。

## 概述

客户端需要进行以下主要修改：

1. **修改服务器发现逻辑** - 从Minecraft服务器获取独立语音服务器地址
2. **更新连接流程** - 支持连接到不同地址的语音服务器
3. **适配认证机制** - 使用新的认证流程

## 主要修改点

### 1. SecretPacket 修改

原始的 `SecretPacket` 需要包含独立语音服务器的地址信息：

```java
// 在 common/src/main/java/de/maxhenkel/voicechat/net/SecretPacket.java 中
public class SecretPacket {
    // 现有字段...
    private String voiceServerHost;  // 新增：独立语音服务器地址
    private int voiceServerPort;     // 新增：独立语音服务器端口
    
    // 构造函数需要更新以包含这些字段
    public SecretPacket(Player player, UUID secret, String voiceServerHost, int voiceServerPort, ServerConfig serverConfig) {
        // 现有逻辑...
        this.voiceServerHost = voiceServerHost;
        this.voiceServerPort = voiceServerPort;
    }
    
    // 添加getter方法
    public String getVoiceServerHost() { return voiceServerHost; }
    public int getVoiceServerPort() { return voiceServerPort; }
}
```

### 2. InitializationData 修改

更新 `InitializationData` 类以使用独立语音服务器地址：

```java
// 在 common-client/src/main/java/de/maxhenkel/voicechat/voice/client/InitializationData.java 中
public class InitializationData {
    
    public InitializationData(String serverIP, SecretPacket secretPacket) {
        // 使用独立语音服务器地址而不是Minecraft服务器地址
        String voiceServerHost = secretPacket.getVoiceServerHost();
        int voiceServerPort = secretPacket.getVoiceServerPort();
        
        if (voiceServerHost != null && !voiceServerHost.isEmpty()) {
            this.serverIP = voiceServerHost;
            this.serverPort = voiceServerPort;
        } else {
            // 回退到原始逻辑（向后兼容）
            HostData hostData = parseAddress(secretPacket.getVoiceHost(), serverIP, secretPacket.getServerPort());
            this.serverIP = hostData.ip;
            this.serverPort = hostData.port;
        }
        
        // 其他字段保持不变...
    }
}
```

### 3. 服务器端修改

#### Bukkit/Spigot 适配器

在 `minecraft-adapter` 项目中，需要修改数据包发送逻辑：

```java
// 在适配器中发送SecretPacket时
public void sendSecretPacket(Player player) {
    try {
        // 从语音服务器获取认证密钥
        String secret = voiceServerClient.generatePlayerSecret(player.getUniqueId());
        
        // 创建包含独立语音服务器地址的SecretPacket
        SecretPacket packet = new SecretPacket(
            player,
            UUID.fromString(secret),
            config.getVoiceServer().getHost(),      // 独立语音服务器地址
            config.getVoiceServer().getPort(),      // 独立语音服务器端口
            serverConfig
        );
        
        // 发送给客户端
        sendPacketToClient(player, packet);
        
    } catch (Exception e) {
        logger.error("Failed to send secret packet", e);
    }
}
```

### 4. 配置文件修改

#### 服务器配置

在 Minecraft 服务器的配置中添加独立语音服务器设置：

```yaml
# 在 minecraft-adapter/src/main/resources/config.yml 中
voice-server:
  # 独立语音服务器配置
  host: "voice.example.com"
  port: 24454
  api-endpoint: "http://voice.example.com:8080"
  auth-token: "your-secret-token"

# 向后兼容设置
compatibility:
  # 是否使用独立语音服务器模式
  standalone-mode: true
  # 如果为false，则使用原始的集成模式
```

### 5. 网络协议兼容性

为了保持向后兼容性，建议：

1. **版本检测**: 在握手过程中检测服务器是否支持独立模式
2. **渐进式升级**: 客户端首先尝试独立模式，失败时回退到集成模式
3. **协议版本**: 在网络协议中添加版本字段

```java
// 示例：在ClientManager中添加兼容性检测
public class ClientManager {
    
    private void authenticate(SecretPacket secretPacket) {
        // 检测是否为独立模式
        if (secretPacket.getVoiceServerHost() != null) {
            // 使用独立语音服务器模式
            connectToStandaloneServer(secretPacket);
        } else {
            // 使用传统集成模式
            connectToIntegratedServer(secretPacket);
        }
    }
    
    private void connectToStandaloneServer(SecretPacket secretPacket) {
        try {
            InitializationData data = new InitializationData(
                secretPacket.getVoiceServerHost(), 
                secretPacket
            );
            client.connect(data);
        } catch (Exception e) {
            logger.error("Failed to connect to standalone voice server", e);
        }
    }
}
```

## 实现步骤

### 阶段1：基础修改
1. 修改 `SecretPacket` 类添加独立服务器字段
2. 更新 `InitializationData` 类支持新的连接方式
3. 修改客户端连接逻辑

### 阶段2：服务器适配
1. 实现 Minecraft 服务器适配器
2. 修改数据包发送逻辑
3. 添加配置文件支持

### 阶段3：兼容性和测试
1. 实现向后兼容性
2. 添加错误处理和重连机制
3. 进行全面测试

## 注意事项

1. **安全性**: 确保认证令牌的安全传输
2. **性能**: 独立模式可能增加网络延迟
3. **错误处理**: 添加完善的错误处理和用户提示
4. **配置验证**: 验证语音服务器地址的有效性

## 测试建议

1. **单元测试**: 测试新的连接逻辑
2. **集成测试**: 测试完整的连接流程
3. **兼容性测试**: 确保与现有版本的兼容性
4. **性能测试**: 测试网络延迟和稳定性

## 部署注意事项

1. **渐进式部署**: 先在测试环境验证
2. **监控**: 添加连接状态监控
3. **回退计划**: 准备快速回退到集成模式的方案
4. **文档**: 更新用户文档和管理员指南
