package de.maxhenkel.voicechat.standalone.network;

import io.netty.buffer.ByteBuf;

/**
 * 网络数据包基类
 */
public abstract class Packet<T extends Packet<T>> {
    
    /**
     * 将数据包写入字节缓冲区
     */
    public abstract void toBytes(ByteBuf buf);
    
    /**
     * 从字节缓冲区读取数据包
     */
    public abstract void fromBytes(ByteBuf buf);
    
    /**
     * 获取数据包ID
     */
    public abstract byte getPacketId();
    
    /**
     * 获取数据包类型
     */
    public abstract Class<T> getPacketClass();
}
