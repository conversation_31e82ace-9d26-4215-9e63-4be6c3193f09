# 跨服务器群组通信问题修复指南

## 问题描述

当A服和B服都连接到同一个独立语音服务器时：
- A服玩家创建群组，B服玩家可以正常加入
- 但加入后，A服玩家只能听到自己服的人，B服玩家只能听到自己服的人
- AB服彼此之间无法在群组内进行语音通信

## 问题原因

跨服务器群组通信失败的原因是缺少必要的权限和配置：

1. **权限问题**：`CROSS_SERVER`权限默认为`false`
2. **服务器配置**：`crossServerAllowed`默认为`false`
3. **跨服务器规则**：没有配置允许的服务器间通信规则

## 自动修复方案

我已经实现了自动配置功能，当服务器注册到独立语音服务器时会自动：

1. **启用跨服务器权限**：将`CROSS_SERVER`默认权限设置为`true`
2. **配置服务器设置**：为每个注册的服务器启用`crossServerAllowed`
3. **建立通信规则**：在所有注册的服务器之间建立双向通信规则

### 自动配置的工作流程

```
服务器A注册 -> 启用A的跨服务器通信
服务器B注册 -> 启用B的跨服务器通信 + 建立A↔B双向规则
服务器C注册 -> 启用C的跨服务器通信 + 建立A↔C、B↔C双向规则
```

## 手动配置方案（如果需要）

如果你需要手动配置或自定义设置，可以使用以下API：

### 1. 启用服务器跨服务器通信

```bash
# 为服务器A启用跨服务器通信
curl -X PUT "http://localhost:8080/api/servers/server-a" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "crossServerAllowed": true
  }'

# 为服务器B启用跨服务器通信
curl -X PUT "http://localhost:8080/api/servers/server-b" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "crossServerAllowed": true
  }'
```

### 2. 建立跨服务器通信规则

```bash
# 允许server-a与server-b通信
curl -X POST "http://localhost:8080/api/servers/cross-server-rules" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "fromServer": "server-a",
    "toServer": "server-b"
  }'

# 允许server-b与server-a通信（双向）
curl -X POST "http://localhost:8080/api/servers/cross-server-rules" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "fromServer": "server-b",
    "toServer": "server-a"
  }'
```

### 3. 为玩家设置跨服务器权限

```bash
# 为特定玩家启用跨服务器权限
curl -X PUT "http://localhost:8080/api/players/{playerUuid}/permissions" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "CROSS_SERVER": true
  }'
```

## 验证配置

### 1. 检查服务器配置

```bash
# 查看服务器A的配置
curl -X GET "http://localhost:8080/api/servers/server-a" \
  -H "Authorization: Bearer your-auth-token"

# 应该返回：
{
  "serverName": "server-a",
  "crossServerAllowed": true,
  ...
}
```

### 2. 检查跨服务器规则

```bash
# 查看所有跨服务器规则
curl -X GET "http://localhost:8080/api/servers/cross-server-rules" \
  -H "Authorization: Bearer your-auth-token"
```

### 3. 检查玩家权限

```bash
# 查看玩家权限
curl -X GET "http://localhost:8080/api/players/{playerUuid}/permissions" \
  -H "Authorization: Bearer your-auth-token"

# 应该包含：
{
  "CROSS_SERVER": true,
  ...
}
```

## 测试步骤

1. **重启独立语音服务器**：确保新的自动配置生效
2. **重启所有Adapter服务器**：让它们重新注册并获得新配置
3. **测试群组通信**：
   - A服玩家创建群组
   - B服玩家加入群组
   - 验证双方可以互相听到语音

## 故障排除

### 问题1：仍然无法跨服务器通信

**检查项目**：
- 确认独立语音服务器日志中有"Enabled cross-server communication"消息
- 确认所有服务器都已重新注册
- 检查玩家是否有CROSS_SERVER权限

**解决方案**：
```bash
# 手动重新注册服务器
curl -X POST "http://localhost:8080/api/servers/register" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "your-server-name",
    "host": "your-server-host",
    "port": 25565
  }'
```

### 问题2：部分玩家无法跨服务器通信

**原因**：个别玩家的权限被单独设置为false

**解决方案**：
```bash
# 重置玩家权限为默认值
curl -X DELETE "http://localhost:8080/api/players/{playerUuid}/permissions/CROSS_SERVER" \
  -H "Authorization: Bearer your-auth-token"
```

### 问题3：群组创建后无法加入

**检查项目**：
- 确认群组类型（NORMAL、OPEN、ISOLATED）
- 检查群组密码
- 验证玩家权限

## 日志监控

关注以下日志消息：

### 独立语音服务器日志
```
[INFO] Server 'server-a' registered successfully from localhost:25565
[INFO] Enabled cross-server communication for server: server-a
[INFO] Added cross-server rule: server-a -> server-b
[INFO] Added cross-server rule: server-b -> server-a
[INFO] Enabled default cross-server permission for all players
```

### Adapter日志
```
[INFO] Server 'server-a' registered successfully with voice server
[DEBUG] Keepalive sent successfully for server: server-a
```

## 配置文件示例

### 独立语音服务器配置 (voice-server.yml)
```yaml
voice:
  # 其他配置...
  
# 跨服务器通信会自动配置，无需手动设置
```

### Adapter配置 (config.yml)
```yaml
# 确保服务器名称唯一
server-name: "survival-server"  # 或 "creative-server" 等

voice-server:
  host: "localhost"
  port: 24454
  api-endpoint: "http://localhost:8080"
  auth-token: "your-secret-token"
```

## 性能考虑

- 跨服务器通信会增加网络流量
- 建议在同一数据中心内部署所有服务器
- 监控独立语音服务器的CPU和内存使用情况
- 考虑限制同时在线的跨服务器群组数量

## 安全考虑

- 确保独立语音服务器的API端点不对外暴露
- 使用强密码作为auth-token
- 定期检查和清理不活跃的服务器注册
- 监控异常的跨服务器通信模式
