package de.maxhenkel.voicechat.standalone.server;

import de.maxhenkel.voicechat.standalone.model.GroupType;
import de.maxhenkel.voicechat.standalone.model.PlayerData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 群组管理器
 */
public class GroupManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(GroupManager.class);
    
    private final ConcurrentHashMap<UUID, VoiceGroup> groups = new ConcurrentHashMap<>();
    
    /**
     * 语音群组类
     */
    public static class VoiceGroup {
        private final UUID id;
        private final String name;
        private final String password;
        private final UUID owner;
        private final Set<UUID> members = ConcurrentHashMap.newKeySet();
        private final Set<UUID> moderators = ConcurrentHashMap.newKeySet(); // 群组管理员
        private final Set<UUID> invitedPlayers = ConcurrentHashMap.newKeySet(); // 被邀请的玩家
        private final Set<UUID> bannedPlayers = ConcurrentHashMap.newKeySet(); // 被禁止的玩家
        private final long createdTime;
        private boolean persistent;
        private boolean open;
        private boolean inviteOnly; // 仅邀请模式
        private int maxMembers; // 最大成员数
        private GroupType type; // 群组类型
        
        public VoiceGroup(UUID id, String name, String password, UUID owner) {
            this(id, name, password, owner, GroupType.NORMAL);
        }

        public VoiceGroup(UUID id, String name, String password, UUID owner, GroupType type) {
            this.id = id;
            this.name = name;
            this.password = password;
            this.owner = owner;
            this.type = type;
            this.createdTime = System.currentTimeMillis();
            this.persistent = false;
            this.open = true;
            this.inviteOnly = false;
            this.maxMembers = 50; // 默认最大50人
        }
        
        // Getters and Setters
        public UUID getId() { return id; }
        public String getName() { return name; }
        public String getPassword() { return password; }
        public UUID getOwner() { return owner; }
        public Set<UUID> getMembers() { return new HashSet<>(members); }
        public long getCreatedTime() { return createdTime; }
        public boolean isPersistent() { return persistent; }
        public void setPersistent(boolean persistent) { this.persistent = persistent; }
        public boolean isOpen() { return open; }
        public void setOpen(boolean open) { this.open = open; }
        public GroupType getType() { return type; }
        public void setType(GroupType type) { this.type = type; }
        public void setInviteOnly(boolean inviteOnly) { this.inviteOnly = inviteOnly; }
        public void setMaxMembers(int maxMembers) { this.maxMembers = Math.max(1, maxMembers); }

        public boolean isInviteOnly() { return inviteOnly; }
        public int getMaxMembers() { return maxMembers; }
        public Set<UUID> getModerators() { return new HashSet<>(moderators); }
        public Set<UUID> getInvitedPlayers() { return new HashSet<>(invitedPlayers); }
        public Set<UUID> getBannedPlayers() { return new HashSet<>(bannedPlayers); }

        public boolean hasPassword() { return password != null && !password.isEmpty(); }
        public boolean checkPassword(String inputPassword) {
            return !hasPassword() || password.equals(inputPassword);
        }

        // 成员管理
        public void addMember(UUID playerUuid) {
            if (members.size() < maxMembers) {
                members.add(playerUuid);
                invitedPlayers.remove(playerUuid); // 加入后移除邀请状态
            }
        }
        public void removeMember(UUID playerUuid) {
            members.remove(playerUuid);
            moderators.remove(playerUuid); // 离开时也移除管理员身份
        }
        public boolean hasMember(UUID playerUuid) { return members.contains(playerUuid); }
        public int getMemberCount() { return members.size(); }
        public boolean isEmpty() { return members.isEmpty(); }
        public boolean isFull() { return members.size() >= maxMembers; }

        // 管理员管理
        public void addModerator(UUID playerUuid) {
            if (hasMember(playerUuid)) {
                moderators.add(playerUuid);
            }
        }
        public void removeModerator(UUID playerUuid) { moderators.remove(playerUuid); }
        public boolean isModerator(UUID playerUuid) { return moderators.contains(playerUuid); }
        public boolean isOwnerOrModerator(UUID playerUuid) {
            return owner.equals(playerUuid) || moderators.contains(playerUuid);
        }

        // 邀请管理
        public void invitePlayer(UUID playerUuid) {
            if (!bannedPlayers.contains(playerUuid)) {
                invitedPlayers.add(playerUuid);
            }
        }
        public void removeInvite(UUID playerUuid) { invitedPlayers.remove(playerUuid); }
        public boolean isInvited(UUID playerUuid) { return invitedPlayers.contains(playerUuid); }

        // 禁止管理
        public void banPlayer(UUID playerUuid) {
            bannedPlayers.add(playerUuid);
            members.remove(playerUuid);
            moderators.remove(playerUuid);
            invitedPlayers.remove(playerUuid);
        }
        public void unbanPlayer(UUID playerUuid) { bannedPlayers.remove(playerUuid); }
        public boolean isBanned(UUID playerUuid) { return bannedPlayers.contains(playerUuid); }

        // 权限检查
        public boolean canJoin(UUID playerUuid) {
            if (isBanned(playerUuid)) return false;
            if (isFull()) return false;
            if (inviteOnly && !isInvited(playerUuid) && !owner.equals(playerUuid)) return false;
            return true;
        }
    }
    
    /**
     * 创建群组
     */
    public VoiceGroup createGroup(String name, String password, UUID owner) {
        return createGroup(name, password, owner, GroupType.NORMAL);
    }

    /**
     * 创建群组（指定类型）
     */
    public VoiceGroup createGroup(String name, String password, UUID owner, GroupType type) {
        UUID groupId = UUID.randomUUID();
        VoiceGroup group = new VoiceGroup(groupId, name, password, owner, type);
        groups.put(groupId, group);

        LOGGER.info("Created group '{}' with ID {} by {} (type: {})", name, groupId, owner, type);
        return group;
    }
    
    /**
     * 删除群组
     */
    public boolean deleteGroup(UUID groupId) {
        VoiceGroup group = groups.remove(groupId);
        if (group != null) {
            LOGGER.info("Deleted group '{}' with ID {}", group.getName(), groupId);

            // 通知适配器群组已被删除
            notifyGroupDeleted(groupId);

            return true;
        }
        return false;
    }

    /**
     * 通知适配器群组已被删除
     */
    private void notifyGroupDeleted(UUID groupId) {
        // 这里可以通过回调或事件通知适配器
        // 暂时通过日志记录，适配器可以通过定期检查来发现已删除的群组
        LOGGER.info("Group {} has been deleted and should be removed from clients", groupId);
    }
    
    /**
     * 获取群组
     */
    public VoiceGroup getGroup(UUID groupId) {
        return groups.get(groupId);
    }
    
    /**
     * 根据名称获取群组
     */
    public VoiceGroup getGroupByName(String name) {
        return groups.values().stream()
                .filter(group -> group.getName().equalsIgnoreCase(name))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取所有群组
     */
    public Collection<VoiceGroup> getAllGroups() {
        return groups.values();
    }
    
    /**
     * 玩家加入群组
     */
    public boolean joinGroup(UUID groupId, UUID playerUuid, String password) {
        VoiceGroup group = groups.get(groupId);
        if (group == null) {
            return false;
        }

        // 检查是否可以加入
        if (!group.canJoin(playerUuid)) {
            LOGGER.warn("Player {} cannot join group '{}' - banned, full, or not invited", playerUuid, group.getName());
            return false;
        }

        // 检查密码
        if (!group.checkPassword(password)) {
            LOGGER.warn("Player {} failed to join group '{}' - wrong password", playerUuid, group.getName());
            return false;
        }

        group.addMember(playerUuid);
        LOGGER.info("Player {} joined group '{}'", playerUuid, group.getName());
        return true;
    }
    
    /**
     * 玩家离开群组
     */
    public boolean leaveGroup(UUID groupId, UUID playerUuid) {
        VoiceGroup group = groups.get(groupId);
        if (group == null) {
            return false;
        }

        group.removeMember(playerUuid);
        LOGGER.info("Player {} left group '{}'", playerUuid, group.getName());

        // 如果群组为空且不是持久化群组，则删除
        if (group.isEmpty() && !group.isPersistent()) {
            LOGGER.info("Group '{}' is now empty and will be deleted", group.getName());
            deleteGroup(groupId);
        }

        return true;
    }
    
    /**
     * 获取玩家所在的群组
     */
    public VoiceGroup getPlayerGroup(UUID playerUuid) {
        return groups.values().stream()
                .filter(group -> group.hasMember(playerUuid))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取玩家创建的群组
     */
    public Collection<VoiceGroup> getPlayerOwnedGroups(UUID playerUuid) {
        return groups.values().stream()
                .filter(group -> group.getOwner().equals(playerUuid))
                .toList();
    }
    
    /**
     * 踢出群组成员
     */
    public boolean kickMember(UUID groupId, UUID memberUuid, UUID kickerUuid) {
        VoiceGroup group = groups.get(groupId);
        if (group == null) {
            return false;
        }
        
        // 只有群组所有者可以踢人
        if (!group.getOwner().equals(kickerUuid)) {
            return false;
        }
        
        if (group.hasMember(memberUuid)) {
            group.removeMember(memberUuid);
            LOGGER.info("Player {} was kicked from group '{}' by {}", memberUuid, group.getName(), kickerUuid);
            return true;
        }
        
        return false;
    }
    
    /**
     * 转移群组所有权
     */
    public boolean transferOwnership(UUID groupId, UUID newOwnerUuid, UUID currentOwnerUuid) {
        VoiceGroup group = groups.get(groupId);
        if (group == null) {
            return false;
        }

        // 只有当前所有者可以转移所有权
        if (!group.getOwner().equals(currentOwnerUuid)) {
            return false;
        }

        // 新所有者必须是群组成员
        if (!group.hasMember(newOwnerUuid)) {
            return false;
        }

        // 创建新的群组对象（因为owner是final的）
        VoiceGroup newGroup = new VoiceGroup(group.getId(), group.getName(), group.getPassword(), newOwnerUuid, group.getType());
        newGroup.setPersistent(group.isPersistent());
        newGroup.setOpen(group.isOpen());
        newGroup.setInviteOnly(group.isInviteOnly());
        newGroup.setMaxMembers(group.getMaxMembers());

        // 复制所有成员和状态
        group.getMembers().forEach(newGroup::addMember);
        group.getModerators().forEach(newGroup::addModerator);
        group.getInvitedPlayers().forEach(newGroup::invitePlayer);
        group.getBannedPlayers().forEach(newGroup::banPlayer);

        groups.put(groupId, newGroup);
        LOGGER.info("Group '{}' ownership transferred from {} to {}", group.getName(), currentOwnerUuid, newOwnerUuid);
        return true;
    }

    /**
     * 邀请玩家加入群组
     */
    public boolean invitePlayer(UUID groupId, UUID inviterUuid, UUID inviteeUuid) {
        VoiceGroup group = groups.get(groupId);
        if (group == null) {
            return false;
        }

        // 只有群主或管理员可以邀请
        if (!group.isOwnerOrModerator(inviterUuid)) {
            LOGGER.warn("Player {} tried to invite to group '{}' without permission", inviterUuid, group.getName());
            return false;
        }

        // 检查被邀请者是否已被禁止
        if (group.isBanned(inviteeUuid)) {
            LOGGER.warn("Cannot invite banned player {} to group '{}'", inviteeUuid, group.getName());
            return false;
        }

        // 检查群组是否已满
        if (group.isFull()) {
            LOGGER.warn("Cannot invite player {} to full group '{}'", inviteeUuid, group.getName());
            return false;
        }

        group.invitePlayer(inviteeUuid);
        LOGGER.info("Player {} invited {} to group '{}'", inviterUuid, inviteeUuid, group.getName());
        return true;
    }

    /**
     * 撤销邀请
     */
    public boolean revokeInvite(UUID groupId, UUID revokerUuid, UUID inviteeUuid) {
        VoiceGroup group = groups.get(groupId);
        if (group == null) {
            return false;
        }

        // 只有群主或管理员可以撤销邀请
        if (!group.isOwnerOrModerator(revokerUuid)) {
            return false;
        }

        group.removeInvite(inviteeUuid);
        LOGGER.info("Player {} revoked invite for {} from group '{}'", revokerUuid, inviteeUuid, group.getName());
        return true;
    }

    /**
     * 踢出玩家
     */
    public boolean kickPlayer(UUID groupId, UUID kickerUuid, UUID kickeeUuid) {
        VoiceGroup group = groups.get(groupId);
        if (group == null) {
            return false;
        }

        // 不能踢出群主
        if (group.getOwner().equals(kickeeUuid)) {
            return false;
        }

        // 只有群主或管理员可以踢人
        if (!group.isOwnerOrModerator(kickerUuid)) {
            return false;
        }

        // 管理员不能踢出其他管理员（只有群主可以）
        if (group.isModerator(kickeeUuid) && !group.getOwner().equals(kickerUuid)) {
            return false;
        }

        group.removeMember(kickeeUuid);
        LOGGER.info("Player {} kicked {} from group '{}'", kickerUuid, kickeeUuid, group.getName());

        // 如果群组为空且不是持久化群组，则删除
        if (group.isEmpty() && !group.isPersistent()) {
            deleteGroup(groupId);
        }

        return true;
    }

    /**
     * 禁止玩家
     */
    public boolean banPlayer(UUID groupId, UUID bannerUuid, UUID banneeUuid) {
        VoiceGroup group = groups.get(groupId);
        if (group == null) {
            return false;
        }

        // 不能禁止群主
        if (group.getOwner().equals(banneeUuid)) {
            return false;
        }

        // 只有群主或管理员可以禁止玩家
        if (!group.isOwnerOrModerator(bannerUuid)) {
            return false;
        }

        // 管理员不能禁止其他管理员（只有群主可以）
        if (group.isModerator(banneeUuid) && !group.getOwner().equals(bannerUuid)) {
            return false;
        }

        group.banPlayer(banneeUuid);
        LOGGER.info("Player {} banned {} from group '{}'", bannerUuid, banneeUuid, group.getName());

        // 如果群组为空且不是持久化群组，则删除
        if (group.isEmpty() && !group.isPersistent()) {
            deleteGroup(groupId);
        }

        return true;
    }

    /**
     * 解除禁止
     */
    public boolean unbanPlayer(UUID groupId, UUID unbannerUuid, UUID unbanneeUuid) {
        VoiceGroup group = groups.get(groupId);
        if (group == null) {
            return false;
        }

        // 只有群主或管理员可以解除禁止
        if (!group.isOwnerOrModerator(unbannerUuid)) {
            return false;
        }

        group.unbanPlayer(unbanneeUuid);
        LOGGER.info("Player {} unbanned {} from group '{}'", unbannerUuid, unbanneeUuid, group.getName());
        return true;
    }

    /**
     * 设置管理员
     */
    public boolean setModerator(UUID groupId, UUID setterUuid, UUID targetUuid, boolean isModerator) {
        VoiceGroup group = groups.get(groupId);
        if (group == null) {
            return false;
        }

        // 只有群主可以设置管理员
        if (!group.getOwner().equals(setterUuid)) {
            return false;
        }

        // 目标必须是群组成员
        if (!group.hasMember(targetUuid)) {
            return false;
        }

        if (isModerator) {
            group.addModerator(targetUuid);
            LOGGER.info("Player {} set {} as moderator in group '{}'", setterUuid, targetUuid, group.getName());
        } else {
            group.removeModerator(targetUuid);
            LOGGER.info("Player {} removed {} as moderator in group '{}'", setterUuid, targetUuid, group.getName());
        }

        return true;
    }
    
    /**
     * 清理空的非持久化群组
     */
    public void cleanupEmptyGroups() {
        groups.entrySet().removeIf(entry -> {
            VoiceGroup group = entry.getValue();
            if (group.isEmpty() && !group.isPersistent()) {
                LOGGER.debug("Cleaned up empty group: {}", group.getName());
                return true;
            }
            return false;
        });
    }
    
    /**
     * 获取群组统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalGroups", groups.size());
        stats.put("persistentGroups", groups.values().stream()
                .mapToInt(group -> group.isPersistent() ? 1 : 0)
                .sum());
        stats.put("totalMembers", groups.values().stream()
                .mapToInt(VoiceGroup::getMemberCount)
                .sum());
        return stats;
    }
}
