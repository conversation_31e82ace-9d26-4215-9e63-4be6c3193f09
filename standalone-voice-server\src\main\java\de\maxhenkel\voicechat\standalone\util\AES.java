package de.maxhenkel.voicechat.standalone.util;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

/**
 * AES加密/解密工具类
 * 与原项目保持一致的加密逻辑
 */
public class AES {
    
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";

    /**
     * 将UUID转换为字节数组（与原项目保持一致）
     */
    public static byte[] getBytesFromUUID(UUID uuid) {
        ByteBuffer buffer = ByteBuffer.wrap(new byte[16]);
        buffer.putLong(uuid.getMostSignificantBits());
        buffer.putLong(uuid.getLeastSignificantBits());
        return buffer.array();
    }

    /**
     * 从字节数组转换为UUID（与原项目保持一致）
     */
    public static UUID getUUIDFromBytes(byte[] bytes) {
        ByteBuffer byteBuffer = ByteBuffer.wrap(bytes);
        long most = byteBuffer.getLong();
        long least = byteBuffer.getLong();
        return new UUID(most, least);
    }

    /**
     * 从UUID生成AES密钥（与原项目保持一致）
     */
    public static SecretKeySpec createKeySpec(UUID secret) {
        return new SecretKeySpec(getBytesFromUUID(secret), ALGORITHM);
    }
    
    /**
     * 解密数据（与原项目保持一致）
     */
    public static byte[] decrypt(UUID secret, byte[] payload) throws NoSuchPaddingException,
            NoSuchAlgorithmException, InvalidAlgorithmParameterException, InvalidKeyException,
            IllegalBlockSizeException, BadPaddingException {

        if (payload.length < 16) {
            throw new IllegalArgumentException("Payload too short for decryption");
        }

        // 提取IV（前16字节）
        byte[] iv = new byte[16];
        System.arraycopy(payload, 0, iv, 0, iv.length);

        // 提取加密数据（剩余字节）
        byte[] data = new byte[payload.length - iv.length];
        System.arraycopy(payload, iv.length, data, 0, data.length);

        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, createKeySpec(secret), ivSpec);

        return cipher.doFinal(data);
    }
    
    /**
     * 加密数据（与原项目保持一致）
     */
    public static byte[] encrypt(UUID secret, byte[] data) throws NoSuchPaddingException,
            NoSuchAlgorithmException, InvalidAlgorithmParameterException, InvalidKeyException,
            IllegalBlockSizeException, BadPaddingException {

        // 生成随机IV
        byte[] iv = new byte[16];
        new java.security.SecureRandom().nextBytes(iv);

        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, createKeySpec(secret), ivSpec);

        byte[] enc = cipher.doFinal(data);

        // 组合IV和加密数据
        byte[] payload = new byte[iv.length + enc.length];
        System.arraycopy(iv, 0, payload, 0, iv.length);
        System.arraycopy(enc, 0, payload, iv.length, enc.length);

        return payload;
    }
}
