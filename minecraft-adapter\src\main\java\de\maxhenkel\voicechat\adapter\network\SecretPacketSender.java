package de.maxhenkel.voicechat.adapter.network;

import de.maxhenkel.voicechat.adapter.VoiceChatAdapterPlugin;
import de.maxhenkel.voicechat.adapter.config.AdapterConfig;
import org.bukkit.entity.Player;
import org.bukkit.plugin.messaging.PluginMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.util.UUID;

/**
 * 负责发送SecretPacket到客户端
 */
public class SecretPacketSender implements PluginMessageListener {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(SecretPacketSender.class);
    
    // 语音聊天插件通道
    public static final String VOICE_CHAT_CHANNEL = "voicechat:secret";
    public static final String REQUEST_CHANNEL = "voicechat:request_secret";
    
    private final VoiceChatAdapterPlugin plugin;
    private final VoiceServerClient voiceServerClient;
    
    public SecretPacketSender(VoiceChatAdapterPlugin plugin, VoiceServerClient voiceServerClient) {
        this.plugin = plugin;
        this.voiceServerClient = voiceServerClient;
    }
    
    /**
     * 注册插件消息通道
     */
    public void register() {
        plugin.getServer().getMessenger().registerOutgoingPluginChannel(plugin, VOICE_CHAT_CHANNEL);
        plugin.getServer().getMessenger().registerIncomingPluginChannel(plugin, REQUEST_CHANNEL, this);
        LOGGER.info("Registered voice chat plugin message channels");
    }
    
    /**
     * 注销插件消息通道
     */
    public void unregister() {
        plugin.getServer().getMessenger().unregisterOutgoingPluginChannel(plugin, VOICE_CHAT_CHANNEL);
        plugin.getServer().getMessenger().unregisterIncomingPluginChannel(plugin, REQUEST_CHANNEL, this);
    }
    
    /**
     * 处理来自客户端的密钥请求
     */
    @Override
    public void onPluginMessageReceived(String channel, Player player, byte[] message) {
        if (!REQUEST_CHANNEL.equals(channel)) {
            return;
        }

        LOGGER.info("Received secret request from player: {}", player.getName());

        // 异步处理密钥请求
        plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
            try {
                // 检查语音服务器连接
                if (!voiceServerClient.isConnected()) {
                    LOGGER.error("Voice server is not connected, cannot send secret to {}", player.getName());

                    // 向玩家发送错误消息
                    plugin.getServer().getScheduler().runTask(plugin, () -> {
                        player.sendMessage("§c[VoiceChat] Voice server is not available. Please try again later.");
                    });
                    return;
                }

                sendSecretPacket(player);

            } catch (Exception e) {
                LOGGER.error("Failed to send secret packet to {}: {}", player.getName(), e.getMessage(), e);

                // 可选：向玩家发送错误消息
                plugin.getServer().getScheduler().runTask(plugin, () -> {
                    player.sendMessage("§c[VoiceChat] Failed to connect to voice server. Please try again.");
                });
            }
        });
    }
    
    /**
     * 发送SecretPacket到客户端
     * 使用与原版完全兼容的格式
     */
    public void sendSecretPacket(Player player) throws Exception {
        AdapterConfig config = plugin.getAdapterConfig();

        // 确保玩家已在语音服务器中注册
        try {
            voiceServerClient.ensurePlayerRegistered(player);
        } catch (Exception e) {
            LOGGER.warn("Failed to register player {}, attempting to continue: {}", player.getName(), e.getMessage());
        }

        // 从语音服务器获取认证密钥
        String secret = voiceServerClient.generatePlayerSecret(player.getUniqueId());

        // 使用Minecraft的FriendlyByteBuf格式（与原版一致）
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        DataOutputStream dos = new DataOutputStream(baos);

        try {
            // 按照原版SecretPacket的确切格式写入数据
            writeUUID(dos, UUID.fromString(secret));  // secret (16 bytes)
            dos.writeInt(config.getVoiceServer().getPort());  // serverPort (4 bytes)
            writeUUID(dos, player.getUniqueId());  // playerUUID (16 bytes)
            dos.writeByte(0);  // codec ordinal - VOIP=0 (1 byte)
            dos.writeInt(1024);  // mtuSize (4 bytes)
            dos.writeDouble(48.0);  // voiceChatDistance (8 bytes)
            dos.writeInt(1000);  // keepAlive (4 bytes)
            dos.writeBoolean(true);  // groupsEnabled (1 byte)

            // voiceHost - 关键字段，用于传递独立服务器地址
            String voiceHost = config.getVoiceServer().getHost() + ":" + config.getVoiceServer().getPort();
            writeMinecraftString(dos, voiceHost);  // voiceHost (variable length)

            dos.writeBoolean(true);  // allowRecording (1 byte)

            // 不写入任何额外字段，保持与原版客户端完全兼容

            byte[] data = baos.toByteArray();
            LOGGER.info("Sending SecretPacket to {}: {} bytes, voiceHost={}",
                       player.getName(), data.length, voiceHost);

            // 发送到客户端
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                player.sendPluginMessage(plugin, VOICE_CHAT_CHANNEL, data);
                LOGGER.info("SecretPacket sent to player: {}", player.getName());
            });

        } finally {
            dos.close();
            baos.close();
        }
    }

    /**
     * 写入Minecraft格式的字符串（使用VarInt长度前缀）
     */
    private void writeMinecraftString(DataOutputStream dos, String str) throws IOException {
        byte[] bytes = str.getBytes("UTF-8");
        writeVarInt(dos, bytes.length);  // 写入长度（VarInt格式）
        dos.write(bytes);  // 写入字符串字节
    }

    /**
     * 写入VarInt（Minecraft网络协议格式）
     */
    private void writeVarInt(DataOutputStream dos, int value) throws IOException {
        while ((value & 0xFFFFFF80) != 0) {
            dos.writeByte((value & 0x7F) | 0x80);
            value >>>= 7;
        }
        dos.writeByte(value & 0x7F);
    }
    
    /**
     * 写入UUID到数据流
     */
    private void writeUUID(DataOutputStream dos, UUID uuid) throws IOException {
        dos.writeLong(uuid.getMostSignificantBits());
        dos.writeLong(uuid.getLeastSignificantBits());
    }
}
