plugins {
    id 'java'
    id 'application'
    id 'com.gradleup.shadow' version '8.3.6'
}

group = 'de.maxhenkel.voicechat'
version = '1.0.0'

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

repositories {
    mavenCentral()
    maven {
        name = 'ParchmentMC'
        url = 'https://maven.parchmentmc.org'
    }
    maven {
        name = 'MaxHenkel Maven'
        url = 'https://maven.maxhenkel.de/repository/public'
    }
    maven {
        name = 'She<PERSON>ielMaven'
        url = 'https://maven.shedaniel.me/'
    }
    maven {
        name = 'JitPack'
        url = 'https://jitpack.io'
    }
}

dependencies {
    // 依赖common模块获取Packet类和其他核心类
    implementation project(':common')

    // HTTP Server
    implementation 'io.javalin:javalin:5.6.3'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.15.2'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2'
    
    // Logging
    implementation 'org.slf4j:slf4j-api:2.0.7'
    implementation 'ch.qos.logback:logback-classic:1.4.11'
    
    // Netty for UDP handling
    implementation 'io.netty:netty-all:4.1.94.Final'
    
    // Audio codec libraries - 使用与原项目相同的版本
    implementation 'de.maxhenkel.opus4j:opus4j:2.0.4'
    implementation 'org.concentus:Concentus:1.0.1'
    implementation 'de.maxhenkel.rnnoise4j:rnnoise4j:2.0.3'
    
    // Utilities
    implementation 'com.google.guava:guava:32.1.2-jre'
    implementation 'org.apache.commons:commons-lang3:3.13.0'
    
    // Configuration
    implementation 'org.yaml:snakeyaml:2.1'
    
    // Testing
    testImplementation 'org.junit.jupiter:junit-jupiter:5.10.0'
    testImplementation 'org.mockito:mockito-core:5.5.0'
}

application {
    mainClass = 'de.maxhenkel.voicechat.standalone.StandaloneVoiceServer'
}

shadowJar {
    archiveClassifier.set('')
    mergeServiceFiles()
}

test {
    useJUnitPlatform()
}

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}
