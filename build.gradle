plugins {
    id 'com.gradleup.shadow' version "${shadow_version}" apply false
    id 'com.matthewprenger.cursegradle' version "${cursegradle_version}" apply false
    id 'com.modrinth.minotaur' version "${minotaur_version}" apply false
    id 'mod-update' version "${mod_update_version}" apply false
    id 'fabric-loom' version "${fabric_loom_version}" apply false
    id 'io.papermc.hangar-publish-plugin' version "${hangar_publish_version}" apply false
    id 'io.papermc.paperweight.userdev' version "${paperweight_version}" apply false
    id 'xyz.jpenilla.run-paper' version "${run_task_version}" apply false
}

apply from: "https://raw.githubusercontent.com/henkelmax/mod-gradle-scripts/${mod_gradle_script_version}/taskutils.gradle"

tasks.register('01-publishBungeeCord') {
    group = 'voicechat'
    doLast {
        runGradleTasks(['common-proxy:clean', 'bungeecord:clean'], ['bungeecord:modrinth', 'bungeecord:publishAllPublicationsToHangar'])
    }
}

tasks.register('02-publishVelocity') {
    group = 'voicechat'
    doLast {
        runGradleTasks(['common-proxy:clean', 'velocity:clean'], ['velocity:modrinth', 'velocity:publishAllPublicationsToHangar'])
    }
}

tasks.register('03-publishBukkit') {
    group = 'voicechat'
    doLast {
        runGradleTasks(['bukkit:clean'], ['bukkit:curseforge', 'bukkit:modrinth', 'bukkit:publishAllPublicationsToHangar'])
    }
}

tasks.register('04-publishPaper') {
    group = 'voicechat'
    doLast {
        runGradleTasks(['common:clean'], ['paper:modrinth', 'paper:publishAllPublicationsToHangar'])
    }
}

tasks.register('05-publishQuilt', GradleBuild) {
    group = 'voicechat'
    dir = 'quilt'
    tasks = ['uploadMod']
}

tasks.register('06-publishForge') {
    group = 'voicechat'
    doLast {
        runGradleTasks(['common:clean', 'common-client:clean', 'forge:clean'], ['forge:curseforge', 'forge:modrinth', 'forge:modUpdate'])
    }
}

tasks.register('07-publishNeoForge') {
    group = 'voicechat'
    doLast {
        runGradleTasks(['common:clean', 'common-client:clean', 'neoforge:clean'], ['neoforge:curseforge', 'neoforge:modrinth', 'neoforge:modUpdate'])
    }
}

tasks.register('08-publishFabric') {
    group = 'voicechat'
    doLast {
        runGradleTasks(['common:clean', 'common-client:clean', 'fabric:clean'], ['fabric:curseforge', 'fabric:modrinth', 'fabric:modUpdate'])
    }
}

tasks.register('publishFabricSnapshot') {
    group = 'voicechat'
    doLast {
        runGradleTasks(['common:clean', 'common-client:clean', 'fabric:clean'], ['fabric:curseforge', 'fabric:modrinth'])
    }
}

tasks.register('publishApi') {
    group = 'voicechat'
    doLast {
        runGradleTasks(['api:clean', 'api:publish'], [])
    }
}

tasks.register('publishApiLocal') {
    group = 'voicechat'
    doLast {
        runGradleTasks(['api:clean', 'api:publishToMavenLocal'], [])
    }
}

// 新增：独立语音服务器构建任务
tasks.register('buildStandalone') {
    group = 'voicechat'
    description = '构建独立语音服务器和适配器'
    doLast {
        runGradleTasks(['standalone-voice-server:clean', 'minecraft-adapter:clean'],
                      ['standalone-voice-server:shadowJar', 'minecraft-adapter:shadowJar'])
    }
}

tasks.register('deployStandalone') {
    group = 'voicechat'
    description = '构建并部署独立语音服务器'
    dependsOn 'buildStandalone'
    doLast {
        println "✅ 独立语音服务器构建完成！"
        println "语音服务器JAR: standalone-voice-server/build/libs/standalone-voice-server-1.0.0.jar"
        println "适配器JAR: minecraft-adapter/build/libs/minecraft-adapter-1.0.0.jar"
        println ""
        println "下一步："
        println "1. cd standalone-voice-server && ./start.sh"
        println "2. 复制适配器JAR到Minecraft服务器plugins目录"
    }
}
