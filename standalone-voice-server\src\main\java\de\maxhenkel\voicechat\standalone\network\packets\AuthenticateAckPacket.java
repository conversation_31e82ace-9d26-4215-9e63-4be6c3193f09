package de.maxhenkel.voicechat.standalone.network.packets;

import de.maxhenkel.voicechat.standalone.network.Packet;
import io.netty.buffer.ByteBuf;

/**
 * 认证确认数据包
 */
public class AuthenticateAckPacket extends Packet<AuthenticateAckPacket> {

    public static final byte PACKET_ID = (byte) 0x6;

    public AuthenticateAckPacket() {
    }

    @Override
    public void fromBytes(ByteBuf buf) {
        // 认证确认包通常没有额外数据
    }

    @Override
    public void toBytes(ByteBuf buf) {
        // 认证确认包通常没有额外数据
    }

    @Override
    public byte getPacketId() {
        return PACKET_ID;
    }

    @Override
    public Class<AuthenticateAckPacket> getPacketClass() {
        return AuthenticateAckPacket.class;
    }
}
