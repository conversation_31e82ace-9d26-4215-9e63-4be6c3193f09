package de.maxhenkel.voicechat.standalone.network;

import de.maxhenkel.voicechat.standalone.network.packets.*;
import de.maxhenkel.voicechat.standalone.server.PlayerManager;
import de.maxhenkel.voicechat.standalone.util.AES;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.SocketAddress;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 网络消息处理器
 */
public class NetworkMessage {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(NetworkMessage.class);
    
    public static final byte MAGIC_BYTE = (byte) 0xFF;
    
    private final long timestamp;
    private final Packet<?> packet;
    private final SocketAddress address;
    
    // 数据包类型注册表
    private static final Map<Byte, Class<? extends Packet<?>>> PACKET_REGISTRY = new HashMap<>();
    
    static {
        // 注册所有语音聊天数据包类型（与原项目保持一致）
        registerPacket((byte) 0x1, MicPacket.class);           // MicPacket
        registerPacket((byte) 0x2, PlayerSoundPacket.class);   // PlayerSoundPacket
        registerPacket((byte) 0x3, GroupSoundPacket.class);    // GroupSoundPacket
        registerPacket((byte) 0x4, LocationSoundPacket.class); // LocationSoundPacket
        registerPacket((byte) 0x5, AuthenticatePacket.class);  // AuthenticatePacket
        registerPacket((byte) 0x6, AuthenticateAckPacket.class); // AuthenticateAckPacket
        registerPacket((byte) 0x7, PingPacket.class);          // PingPacket
        registerPacket((byte) 0x8, KeepAlivePacket.class);     // KeepAlivePacket
        registerPacket((byte) 0x9, ConnectionCheckPacket.class); // ConnectionCheckPacket
        registerPacket((byte) 0xA, ConnectionCheckAckPacket.class); // ConnectionCheckAckPacket

        // 添加其他可能的数据包类型（使用通用处理）
        // 对于未知的数据包，我们将创建一个通用的处理器
    }
    
    public NetworkMessage(Packet<?> packet, SocketAddress address) {
        this.timestamp = System.currentTimeMillis();
        this.packet = packet;
        this.address = address;
    }
    
    public NetworkMessage(long timestamp, Packet<?> packet, SocketAddress address) {
        this.timestamp = timestamp;
        this.packet = packet;
        this.address = address;
    }
    
    /**
     * 注册数据包类型
     */
    private static void registerPacket(byte id, Class<? extends Packet<?>> packetClass) {
        PACKET_REGISTRY.put(id, packetClass);
    }
    
    /**
     * 从字节数组解析网络消息
     * 注意：原项目的数据包格式是 Magic Byte + 加密数据
     */
    public static NetworkMessage fromBytes(byte[] data, SocketAddress address, PlayerManager playerManager) throws Exception {
        
        if (data.length < 21) { // Magic byte (1) + Player UUID (16) + Length (4) + 至少0字节加密数据
            LOGGER.debug("Packet too small from {}: {} bytes", address, data.length);
            return null;
        }

        // 检查魔术字节
        if (data[0] != MAGIC_BYTE) {
            LOGGER.debug("Invalid magic byte: {} from {}, expected {}", data[0], address, MAGIC_BYTE);
            return null;
        }

        // 使用FriendlyByteBuf按照原项目的方式解析
        ByteBuf buf = Unpooled.wrappedBuffer(data);

        try {
            // 跳过Magic Byte（已经验证过了）
            buf.readByte();

            // 读取Player UUID（16字节）
            long mostSig = buf.readLong();
            long leastSig = buf.readLong();
            UUID playerUuid = new UUID(mostSig, leastSig);

            // 读取加密数据长度（VarInt）
            int encryptedDataLength = readVarInt(buf);

            // 验证长度是否合理
            if (encryptedDataLength < 0 || encryptedDataLength > buf.readableBytes()) {
                LOGGER.warn("Invalid encrypted data length: {} (readable bytes: {})", encryptedDataLength, buf.readableBytes());
                return null;
            }

            // 读取加密数据
            byte[] encryptedData = new byte[encryptedDataLength];
            buf.readBytes(encryptedData);

            // 尝试解密数据包
            return tryDecryptPacket(encryptedData, address, playerManager, playerUuid);

        } finally {
            buf.release();
        }
    }

    /**
     * 读取VarInt（与原项目保持一致）
     */
    private static int readVarInt(ByteBuf buf) {
        int i = 0;
        int j = 0;

        byte b;
        do {
            b = buf.readByte();
            i |= (b & 127) << j++ * 7;
            if (j > 5) {
                throw new RuntimeException("VarInt too big");
            }
        } while ((b & 128) == 128);

        return i;
    }

    /**
     * 尝试解密数据包
     */
    private static NetworkMessage tryDecryptPacket(byte[] encryptedData, SocketAddress address, PlayerManager playerManager, UUID playerUuid) {
        // 获取指定玩家的密钥
        UUID secret = playerManager.getPlayerSecret(playerUuid);
        if (secret == null) {
            LOGGER.warn("No secret found for player {}", playerUuid);
            return null;
        }

        try {
            // 尝试解密
            byte[] decryptedData = AES.decrypt(secret, encryptedData);

            // 解析解密后的数据
            NetworkMessage message = parseDecryptedData(decryptedData, address, playerUuid);
            if (message != null) {
                return message;
            } else {
                LOGGER.debug("Decryption successful but packet parsing failed for player {}", playerUuid);
            }
        } catch (Exception e) {
            // 解密失败
            LOGGER.debug("Failed to decrypt packet from {} with player {}'s secret: {}",
                       address, playerUuid, e.getMessage());
        }

        return null;
    }

    /**
     * 解析解密后的数据
     */
    private static NetworkMessage parseDecryptedData(byte[] decryptedData, SocketAddress address, UUID playerUuid) throws Exception {
        if (decryptedData.length < 1) { // 至少需要数据包ID(1字节)
            LOGGER.debug("Decrypted data too small: {} bytes", decryptedData.length);
            return null;
        }

        ByteBuf buf = Unpooled.wrappedBuffer(decryptedData);

        try {
            // 直接读取数据包ID（与原项目一致）
            byte packetId = buf.readByte();

            // 检查是否是有效的数据包ID
            if (packetId < 0x1 || packetId > 0xA) {
                LOGGER.warn("Invalid packet ID: {} from {}", packetId, address);
                return null;
            }

            // 获取数据包类
            Class<? extends Packet<?>> packetClass = PACKET_REGISTRY.get(packetId);
            if (packetClass == null) {
                LOGGER.warn("Unknown packet ID: {} from {}", packetId, address);
                return null;
            }

            // 创建数据包实例
            Packet<?> packet = packetClass.getDeclaredConstructor().newInstance();
            packet.fromBytes(buf);

            // 如果是MicPacket，设置sender
            if (packet instanceof de.maxhenkel.voicechat.standalone.network.packets.MicPacket) {
                ((de.maxhenkel.voicechat.standalone.network.packets.MicPacket) packet).setSender(playerUuid);
            }

            // 使用当前时间戳（与原项目一致）
            return new NetworkMessage(System.currentTimeMillis(), packet, address);

        } finally {
            buf.release();
        }
    }
    
    /**
     * 将网络消息转换为字节数组（发送给指定玩家）
     * 使用与原项目相同的格式：Magic Byte + VarInt(Length) + EncryptedData
     */
    public byte[] toBytes(UUID targetPlayerUuid, UUID secret) throws Exception {
        // 1. 创建数据包内容
        ByteBuf contentBuf = Unpooled.buffer();
        try {
            // 写入数据包ID
            contentBuf.writeByte(packet.getPacketId());

            // 写入数据包内容
            packet.toBytes(contentBuf);

            // 转换为字节数组
            byte[] packetData = new byte[contentBuf.readableBytes()];
            contentBuf.readBytes(packetData);

            // 2. 加密数据包内容
            byte[] encryptedData = AES.encrypt(secret, packetData);

            // 3. 创建最终的UDP数据包（与原项目格式一致）
            ByteBuf finalBuf = Unpooled.buffer();
            try {
                // 写入魔术字节
                finalBuf.writeByte(MAGIC_BYTE);

                // 写入加密数据（使用writeByteArray格式：VarInt长度 + 数据）
                writeByteArray(finalBuf, encryptedData);

                // 转换为字节数组
                byte[] result = new byte[finalBuf.readableBytes()];
                finalBuf.readBytes(result);
                return result;

            } finally {
                finalBuf.release();
            }

        } finally {
            contentBuf.release();
        }
    }

    /**
     * 写入字节数组（与FriendlyByteBuf.writeByteArray()保持一致）
     */
    private static void writeByteArray(ByteBuf buf, byte[] data) {
        writeVarInt(buf, data.length);
        buf.writeBytes(data);
    }

    /**
     * 写入VarInt
     */
    private static void writeVarInt(ByteBuf buf, int value) {
        while ((value & -128) != 0) {
            buf.writeByte(value & 127 | 128);
            value >>>= 7;
        }
        buf.writeByte(value);
    }
    
    // Getters
    public long getTimestamp() { return timestamp; }
    public Packet<?> getPacket() { return packet; }
    public SocketAddress getAddress() { return address; }
    
    @SuppressWarnings("unchecked")
    public <T extends Packet<T>> T getPacket(Class<T> packetClass) {
        if (packetClass.isInstance(packet)) {
            return (T) packet;
        }
        return null;
    }
}
