@echo off
REM 独立语音服务器构建脚本 (Windows)

setlocal enabledelayedexpansion

echo 独立语音服务器构建脚本
echo ======================

REM 设置环境变量（避免构建警告）
if "%CURSEFORGE_API_KEY%"=="" set CURSEFORGE_API_KEY=dummy-key-for-local-build
if "%MODRINTH_TOKEN%"=="" set MODRINTH_TOKEN=dummy-token-for-local-build
if "%MOD_UPDATE_API_KEY%"=="" set MOD_UPDATE_API_KEY=dummy-key-for-local-build

REM 检查Java版本
echo [STEP] 检查构建环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Java 未安装
    pause
    exit /b 1
)

REM 检查Gradle
if exist "gradlew.bat" (
    echo [INFO] 使用项目自带的Gradle Wrapper
    set GRADLE_CMD=gradlew.bat
) else (
    gradle --version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Gradle 未找到，请安装Gradle或确保gradlew.bat存在
        pause
        exit /b 1
    ) else (
        echo [INFO] 使用系统安装的Gradle
        set GRADLE_CMD=gradle
    )
)

REM 处理命令行参数
set ACTION=%1
if "%ACTION%"=="" set ACTION=all

if "%ACTION%"=="clean" goto clean
if "%ACTION%"=="voice-server" goto voice_server
if "%ACTION%"=="adapter" goto adapter
if "%ACTION%"=="test" goto test
if "%ACTION%"=="all" goto all
goto usage

:clean
echo [STEP] 清理之前的构建...
%GRADLE_CMD% clean
if errorlevel 1 (
    echo [ERROR] 清理失败
    pause
    exit /b 1
)
echo [INFO] 清理完成
goto end

:voice_server
echo [STEP] 构建独立语音服务器...
%GRADLE_CMD% :standalone-voice-server:shadowJar
if errorlevel 1 (
    echo [ERROR] 独立语音服务器构建失败
    pause
    exit /b 1
)
if exist "standalone-voice-server\build\libs\standalone-voice-server-1.0.0.jar" (
    echo [INFO] 独立语音服务器构建成功 ✓
) else (
    echo [ERROR] 独立语音服务器JAR文件未找到
    pause
    exit /b 1
)
goto end

:adapter
echo [STEP] 构建Minecraft适配器...
%GRADLE_CMD% :minecraft-adapter:shadowJar
if errorlevel 1 (
    echo [ERROR] Minecraft适配器构建失败
    pause
    exit /b 1
)
if exist "minecraft-adapter\build\libs\minecraft-adapter-1.0.0.jar" (
    echo [INFO] Minecraft适配器构建成功 ✓
) else (
    echo [ERROR] Minecraft适配器JAR文件未找到
    pause
    exit /b 1
)
goto end

:test
echo [STEP] 运行测试...
%GRADLE_CMD% :standalone-voice-server:test :minecraft-adapter:test
if errorlevel 1 (
    echo [WARN] 部分测试失败，但构建可以继续
) else (
    echo [INFO] 测试完成
)
goto end

:all
call :clean
call :voice_server
call :adapter
call :test

echo.
echo ==================================
echo 🎉 构建完成！
echo ==================================
echo.
echo 📦 构建产物：
if exist "standalone-voice-server\build\libs\standalone-voice-server-1.0.0.jar" (
    echo 独立语音服务器: standalone-voice-server\build\libs\standalone-voice-server-1.0.0.jar
)
if exist "minecraft-adapter\build\libs\minecraft-adapter-1.0.0.jar" (
    echo Minecraft适配器: minecraft-adapter\build\libs\minecraft-adapter-1.0.0.jar
)
echo.
echo 🚀 下一步：
echo 1. 启动独立语音服务器:
echo    cd standalone-voice-server ^&^& start.bat
echo.
echo 2. 安装Minecraft适配器:
echo    复制 minecraft-adapter\build\libs\minecraft-adapter-1.0.0.jar
echo    到 Minecraft服务器的 plugins 目录
echo.
echo 3. 配置Minecraft服务器使用独立语音服务器
echo ==================================
goto end

:usage
echo 用法: %0 [clean^|voice-server^|adapter^|test^|all]
echo.
echo 选项:
echo   clean        清理构建文件
echo   voice-server 只构建独立语音服务器
echo   adapter      只构建Minecraft适配器
echo   test         运行测试
echo   all          构建所有组件（默认）
goto end

:end
if "%ACTION%"=="all" pause
