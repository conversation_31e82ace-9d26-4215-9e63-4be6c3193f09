package de.maxhenkel.voicechat.standalone.network.packets;

import de.maxhenkel.voicechat.standalone.network.Packet;
import io.netty.buffer.ByteBuf;

import java.util.UUID;

/**
 * 音频播放数据包
 */
public class SoundPacket extends Packet<SoundPacket> {
    
    public static final byte PACKET_ID = 2;
    
    private UUID sender;
    private byte[] data;
    private long sequenceNumber;
    private float distance;
    private String category;
    
    public SoundPacket() {}
    
    public SoundPacket(UUID sender, byte[] data, long sequenceNumber, float distance, String category) {
        this.sender = sender;
        this.data = data;
        this.sequenceNumber = sequenceNumber;
        this.distance = distance;
        this.category = category;
    }
    
    @Override
    public void toBytes(ByteBuf buf) {
        buf.writeLong(sender.getMostSignificantBits());
        buf.writeLong(sender.getLeastSignificantBits());
        buf.writeLong(sequenceNumber);
        buf.writeFloat(distance);
        
        // 写入category
        byte[] categoryBytes = category.getBytes();
        buf.writeInt(categoryBytes.length);
        buf.writeBytes(categoryBytes);
        
        // 写入音频数据
        buf.writeInt(data.length);
        buf.writeBytes(data);
    }
    
    @Override
    public void fromBytes(ByteBuf buf) {
        long mostSig = buf.readLong();
        long leastSig = buf.readLong();
        sender = new UUID(mostSig, leastSig);
        sequenceNumber = buf.readLong();
        distance = buf.readFloat();
        
        // 读取category
        int categoryLength = buf.readInt();
        byte[] categoryBytes = new byte[categoryLength];
        buf.readBytes(categoryBytes);
        category = new String(categoryBytes);
        
        // 读取音频数据
        int dataLength = buf.readInt();
        data = new byte[dataLength];
        buf.readBytes(data);
    }
    
    @Override
    public byte getPacketId() {
        return PACKET_ID;
    }
    
    @Override
    public Class<SoundPacket> getPacketClass() {
        return SoundPacket.class;
    }
    
    // Getters
    public UUID getSender() { return sender; }
    public byte[] getData() { return data; }
    public long getSequenceNumber() { return sequenceNumber; }
    public float getDistance() { return distance; }
    public String getCategory() { return category; }
}
