package de.maxhenkel.voicechat.standalone.network.packets;

import de.maxhenkel.voicechat.standalone.network.Packet;
import io.netty.buffer.ByteBuf;

/**
 * 连接检查确认数据包
 */
public class ConnectionCheckAckPacket extends Packet<ConnectionCheckAckPacket> {

    public static final byte PACKET_ID = (byte) 0xA;

    public ConnectionCheckAckPacket() {
    }

    @Override
    public void fromBytes(ByteBuf buf) {
        // 连接检查确认包通常没有额外数据
    }

    @Override
    public void toBytes(ByteBuf buf) {
        // 连接检查确认包通常没有额外数据
    }

    @Override
    public byte getPacketId() {
        return PACKET_ID;
    }

    @Override
    public Class<ConnectionCheckAckPacket> getPacketClass() {
        return ConnectionCheckAckPacket.class;
    }
}
