package de.maxhenkel.voicechat.standalone.server;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 管理Minecraft Adapter服务器的注册、keepalive和超时清理
 */
public class ServerRegistrationManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ServerRegistrationManager.class);
    
    // 服务器注册信息存储
    private final Map<String, RegisteredServer> registeredServers = new ConcurrentHashMap<>();
    
    // 定时任务执行器
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    // 配置常量
    private static final long KEEPALIVE_TIMEOUT_MS = 30 * 60 * 1000; // 30分钟超时
    private static final long CLEANUP_INTERVAL_MS = 30 * 60 * 1000; // 30分钟清理间隔
    
    /**
     * 注册的服务器信息
     */
    public static class RegisteredServer {
        private final String serverName;
        private final String host;
        private final int port;
        private final long registrationTime;
        private volatile long lastKeepaliveTime;
        
        public RegisteredServer(String serverName, String host, int port) {
            this.serverName = serverName;
            this.host = host;
            this.port = port;
            this.registrationTime = System.currentTimeMillis();
            this.lastKeepaliveTime = System.currentTimeMillis();
        }
        
        public String getServerName() { return serverName; }
        public String getHost() { return host; }
        public int getPort() { return port; }
        public long getRegistrationTime() { return registrationTime; }
        public long getLastKeepaliveTime() { return lastKeepaliveTime; }
        
        public void updateKeepalive() {
            this.lastKeepaliveTime = System.currentTimeMillis();
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() - lastKeepaliveTime > KEEPALIVE_TIMEOUT_MS;
        }
        
        @Override
        public String toString() {
            return String.format("RegisteredServer{name='%s', host='%s', port=%d, lastKeepalive=%d}", 
                    serverName, host, port, lastKeepaliveTime);
        }
    }
    
    /**
     * 启动服务器注册管理器
     */
    public void start() {
        LOGGER.info("Starting server registration manager");
        
        // 启动定时清理任务
        scheduler.scheduleAtFixedRate(this::cleanupExpiredServers, 
                CLEANUP_INTERVAL_MS, CLEANUP_INTERVAL_MS, TimeUnit.MILLISECONDS);
        
        LOGGER.info("Server registration manager started with cleanup interval: {} minutes", 
                CLEANUP_INTERVAL_MS / 60000);
    }
    
    /**
     * 停止服务器注册管理器
     */
    public void stop() {
        LOGGER.info("Stopping server registration manager");
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        registeredServers.clear();
        LOGGER.info("Server registration manager stopped");
    }
    
    /**
     * 注册服务器
     * @param serverName 服务器名称
     * @param host 服务器主机地址
     * @param port 服务器端口
     * @return 如果注册成功返回true，如果服务器名称已存在返回false
     */
    public boolean registerServer(String serverName, String host, int port) {
        if (serverName == null || serverName.trim().isEmpty()) {
            LOGGER.warn("Attempted to register server with empty name");
            return false;
        }
        
        // 检查服务器名称是否已存在
        if (registeredServers.containsKey(serverName)) {
            LOGGER.warn("Server name '{}' is already registered", serverName);
            return false;
        }
        
        RegisteredServer server = new RegisteredServer(serverName, host, port);
        registeredServers.put(serverName, server);
        
        LOGGER.info("Server '{}' registered successfully from {}:{}", serverName, host, port);
        return true;
    }
    
    /**
     * 更新服务器keepalive
     * @param serverName 服务器名称
     * @return 如果服务器存在并更新成功返回true，否则返回false
     */
    public boolean updateKeepalive(String serverName) {
        RegisteredServer server = registeredServers.get(serverName);
        if (server == null) {
            LOGGER.warn("Attempted to update keepalive for unregistered server: {}", serverName);
            return false;
        }
        
        server.updateKeepalive();
        LOGGER.debug("Updated keepalive for server: {}", serverName);
        return true;
    }
    
    /**
     * 取消注册服务器
     * @param serverName 服务器名称
     * @return 如果服务器存在并取消注册成功返回true，否则返回false
     */
    public boolean unregisterServer(String serverName) {
        RegisteredServer server = registeredServers.remove(serverName);
        if (server != null) {
            LOGGER.info("Server '{}' unregistered", serverName);
            return true;
        } else {
            LOGGER.warn("Attempted to unregister non-existent server: {}", serverName);
            return false;
        }
    }
    
    /**
     * 检查服务器名称是否已被注册
     * @param serverName 服务器名称
     * @return 如果已注册返回true，否则返回false
     */
    public boolean isServerRegistered(String serverName) {
        return registeredServers.containsKey(serverName);
    }
    
    /**
     * 获取注册的服务器信息
     * @param serverName 服务器名称
     * @return 服务器信息，如果不存在返回null
     */
    public RegisteredServer getRegisteredServer(String serverName) {
        return registeredServers.get(serverName);
    }
    
    /**
     * 获取所有注册的服务器
     * @return 所有注册的服务器信息
     */
    public Map<String, RegisteredServer> getAllRegisteredServers() {
        return Map.copyOf(registeredServers);
    }
    
    /**
     * 获取活跃的服务器列表（未过期的）
     * @return 活跃的服务器名称集合
     */
    public Set<String> getActiveServerNames() {
        return registeredServers.entrySet().stream()
                .filter(entry -> !entry.getValue().isExpired())
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }
    
    /**
     * 清理过期的服务器
     */
    private void cleanupExpiredServers() {
        long currentTime = System.currentTimeMillis();
        int removedCount = 0;
        
        var iterator = registeredServers.entrySet().iterator();
        while (iterator.hasNext()) {
            var entry = iterator.next();
            RegisteredServer server = entry.getValue();
            
            if (server.isExpired()) {
                iterator.remove();
                removedCount++;
                LOGGER.info("Removed expired server '{}' (last keepalive: {} minutes ago)", 
                        server.getServerName(), 
                        (currentTime - server.getLastKeepaliveTime()) / 60000);
            }
        }
        
        if (removedCount > 0) {
            LOGGER.info("Cleanup completed: removed {} expired servers", removedCount);
        } else {
            LOGGER.debug("Cleanup completed: no expired servers found");
        }
    }
    
    /**
     * 获取统计信息
     * @return 包含统计信息的Map
     */
    public Map<String, Object> getStatistics() {
        long currentTime = System.currentTimeMillis();
        int totalServers = registeredServers.size();
        int activeServers = (int) registeredServers.values().stream()
                .filter(server -> !server.isExpired())
                .count();
        int expiredServers = totalServers - activeServers;
        
        return Map.of(
                "totalServers", totalServers,
                "activeServers", activeServers,
                "expiredServers", expiredServers,
                "keepaliveTimeoutMinutes", KEEPALIVE_TIMEOUT_MS / 60000,
                "cleanupIntervalMinutes", CLEANUP_INTERVAL_MS / 60000
        );
    }
}
