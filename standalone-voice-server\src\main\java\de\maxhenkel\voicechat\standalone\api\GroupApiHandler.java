package de.maxhenkel.voicechat.standalone.api;

import de.maxhenkel.voicechat.standalone.model.GroupType;
import de.maxhenkel.voicechat.standalone.server.GroupManager;
import de.maxhenkel.voicechat.standalone.server.VoiceServer;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 群组API处理器
 */
public class GroupApiHandler {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(GroupApiHandler.class);
    
    private final VoiceServer voiceServer;
    
    public GroupApiHandler(VoiceServer voiceServer) {
        this.voiceServer = voiceServer;
    }
    
    /**
     * 创建群组
     * POST /api/groups
     */
    public void handleCreateGroup(Context ctx) {
        try {
            Map<String, Object> request = ctx.bodyAsClass(Map.class);
            
            String name = (String) request.get("name");
            String password = (String) request.get("password");
            String ownerUuidStr = (String) request.get("ownerUuid");

            if (name == null || ownerUuidStr == null) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Missing required fields: name, ownerUuid"));
                return;
            }

            UUID ownerUuid = UUID.fromString(ownerUuidStr);

            // 解析群组类型
            GroupType groupType = GroupType.NORMAL; // 默认类型
            Object typeObj = request.get("type");
            if (typeObj instanceof Number) {
                groupType = GroupType.fromId(((Number) typeObj).intValue());
            } else if (typeObj instanceof String) {
                try {
                    int typeId = Integer.parseInt((String) typeObj);
                    groupType = GroupType.fromId(typeId);
                } catch (NumberFormatException e) {
                    groupType = GroupType.fromName((String) typeObj);
                }
            }

            // 检查群组名是否已存在
            if (voiceServer.getGroupManager().getGroupByName(name) != null) {
                ctx.status(HttpStatus.CONFLICT)
                   .json(Map.of("error", "Group name already exists"));
                return;
            }

            GroupManager.VoiceGroup group = voiceServer.getGroupManager().createGroup(name, password, ownerUuid, groupType);

            // 设置群组属性
            Boolean persistent = (Boolean) request.get("persistent");
            if (persistent != null) {
                group.setPersistent(persistent);
            }

            Boolean open = (Boolean) request.get("open");
            if (open != null) {
                group.setOpen(open);
            }

            // 自动将创建者加入群组
            group.addMember(ownerUuid);
            voiceServer.getPlayerManager().setPlayerGroup(ownerUuid, group.getId());

            LOGGER.info("Created group '{}' by {}", name, ownerUuid);
            
            ctx.status(HttpStatus.CREATED)
               .json(Map.of(
                   "success", true,
                   "message", "Group created successfully",
                   "group", groupToMap(group)
               ));
               
        } catch (Exception e) {
            LOGGER.error("Error creating group", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }

    /**
     * 获取单个群组信息
     * GET /api/groups/{groupId}
     */
    public void handleGetGroup(Context ctx) {
        try {
            String groupIdStr = ctx.pathParam("groupId");
            UUID groupId = UUID.fromString(groupIdStr);

            GroupManager.VoiceGroup group = voiceServer.getGroupManager().getGroup(groupId);
            if (group == null) {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of("error", "Group not found"));
                return;
            }

            ctx.status(HttpStatus.OK)
               .json(groupToMap(group));

        } catch (IllegalArgumentException e) {
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid group ID format"));
        } catch (Exception e) {
            LOGGER.error("Error getting group", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error: " + e.getMessage()));
        }
    }

    /**
     * 删除群组
     * DELETE /api/groups/{groupId}
     */
    public void handleDeleteGroup(Context ctx) {
        try {
            String groupIdStr = ctx.pathParam("groupId");
            UUID groupId = UUID.fromString(groupIdStr);
            
            boolean deleted = voiceServer.getGroupManager().deleteGroup(groupId);
            
            if (deleted) {
                ctx.status(HttpStatus.OK)
                   .json(Map.of("success", true, "message", "Group deleted successfully"));
            } else {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of("error", "Group not found"));
            }
            
        } catch (Exception e) {
            LOGGER.error("Error deleting group", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 加入群组
     * POST /api/groups/{groupId}/members
     */
    public void handleJoinGroup(Context ctx) {
        try {
            String groupIdStr = ctx.pathParam("groupId");
            UUID groupId = UUID.fromString(groupIdStr);
            
            Map<String, Object> request = ctx.bodyAsClass(Map.class);
            String playerUuidStr = (String) request.get("playerUuid");
            String password = (String) request.get("password");
            
            if (playerUuidStr == null) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Missing required field: playerUuid"));
                return;
            }
            
            UUID playerUuid = UUID.fromString(playerUuidStr);
            
            boolean joined = voiceServer.getGroupManager().joinGroup(groupId, playerUuid, password);
            
            if (joined) {
                // 更新玩家的群组ID
                voiceServer.getPlayerManager().setPlayerGroup(playerUuid, groupId);

                // 通知所有群组成员有新成员加入
                broadcastGroupMemberUpdate(groupId);

                ctx.status(HttpStatus.OK)
                   .json(Map.of("success", true, "message", "Joined group successfully"));
            } else {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Failed to join group (wrong password or group not found)"));
            }
            
        } catch (Exception e) {
            LOGGER.error("Error joining group", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 离开群组
     * DELETE /api/groups/{groupId}/members/{playerId}
     */
    public void handleLeaveGroup(Context ctx) {
        try {
            String groupIdStr = ctx.pathParam("groupId");
            String playerIdStr = ctx.pathParam("playerId");
            
            UUID groupId = UUID.fromString(groupIdStr);
            UUID playerId = UUID.fromString(playerIdStr);
            
            boolean left = voiceServer.getGroupManager().leaveGroup(groupId, playerId);
            
            if (left) {
                // 清除玩家的群组ID
                voiceServer.getPlayerManager().setPlayerGroup(playerId, null);
                
                ctx.status(HttpStatus.OK)
                   .json(Map.of("success", true, "message", "Left group successfully"));
            } else {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of("error", "Group or player not found"));
            }
            
        } catch (Exception e) {
            LOGGER.error("Error leaving group", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }

    /**
     * 玩家离开当前群组（不需要指定群组ID）
     * DELETE /api/players/{playerId}/group
     */
    public void handlePlayerLeaveGroup(Context ctx) {
        try {
            String playerIdStr = ctx.pathParam("playerId");
            UUID playerId = UUID.fromString(playerIdStr);

            // 获取玩家当前所在的群组
            GroupManager.VoiceGroup currentGroup = voiceServer.getGroupManager().getPlayerGroup(playerId);

            if (currentGroup == null) {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of("error", "Player is not in any group"));
                return;
            }

            // 离开群组
            boolean left = voiceServer.getGroupManager().leaveGroup(currentGroup.getId(), playerId);

            if (left) {
                // 清除玩家的群组ID
                voiceServer.getPlayerManager().setPlayerGroup(playerId, null);

                LOGGER.info("Player {} left group '{}'", playerId, currentGroup.getName());

                ctx.status(HttpStatus.OK)
                   .json(Map.of("success", true, "message", "Left group successfully",
                               "groupName", currentGroup.getName()));
            } else {
                ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
                   .json(Map.of("error", "Failed to leave group"));
            }

        } catch (Exception e) {
            LOGGER.error("Error leaving group", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 获取所有群组
     * GET /api/groups
     */
    public void handleGetAllGroups(Context ctx) {
        try {
            Collection<GroupManager.VoiceGroup> groups = voiceServer.getGroupManager().getAllGroups();
            List<Map<String, Object>> groupList = groups.stream()
                    .map(this::groupToMap)
                    .collect(Collectors.toList());
            
            // 直接返回群组列表数组（与适配器期望的格式一致）
            ctx.json(groupList);
            
        } catch (Exception e) {
            LOGGER.error("Error getting all groups", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }

    /**
     * 将群组数据转换为Map
     */
    private Map<String, Object> groupToMap(GroupManager.VoiceGroup group) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", group.getId().toString());
        map.put("name", group.getName());
        map.put("hasPassword", group.hasPassword());
        map.put("owner", group.getOwner().toString());
        map.put("memberCount", group.getMemberCount());
        map.put("maxMembers", group.getMaxMembers());
        map.put("createdTime", group.getCreatedTime());
        map.put("persistent", group.isPersistent());
        map.put("open", group.isOpen());
        map.put("inviteOnly", group.isInviteOnly());
        map.put("type", group.getType().getId());
        map.put("typeName", group.getType().getName());

        // 添加群组成员列表
        java.util.List<Map<String, Object>> members = new java.util.ArrayList<>();
        for (UUID memberId : group.getMembers()) {
            de.maxhenkel.voicechat.standalone.model.PlayerData player = voiceServer.getPlayerManager().getPlayer(memberId);
            if (player != null) {
                Map<String, Object> memberInfo = new HashMap<>();
                memberInfo.put("uuid", memberId.toString());
                memberInfo.put("name", player.getName());
                memberInfo.put("online", player.isOnline());
                memberInfo.put("isModerator", group.isModerator(memberId));
                memberInfo.put("isOwner", group.getOwner().equals(memberId));
                members.add(memberInfo);
            }
        }
        map.put("members", members);

        // 添加管理员列表
        map.put("moderators", group.getModerators().stream()
                .map(UUID::toString)
                .collect(Collectors.toList()));

        // 添加邀请列表
        map.put("invitedPlayers", group.getInvitedPlayers().stream()
                .map(UUID::toString)
                .collect(Collectors.toList()));

        // 添加禁止列表
        map.put("bannedPlayers", group.getBannedPlayers().stream()
                .map(UUID::toString)
                .collect(Collectors.toList()));

        return map;
    }

    /**
     * 邀请玩家加入群组
     * POST /api/groups/{groupId}/invite
     */
    public void handleInvitePlayer(Context ctx) {
        try {
            String groupIdStr = ctx.pathParam("groupId");
            UUID groupId = UUID.fromString(groupIdStr);

            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            String inviterUuidStr = (String) requestData.get("inviterUuid");
            String inviteeUuidStr = (String) requestData.get("inviteeUuid");

            UUID inviterUuid = UUID.fromString(inviterUuidStr);
            UUID inviteeUuid = UUID.fromString(inviteeUuidStr);

            boolean success = voiceServer.getGroupManager().invitePlayer(groupId, inviterUuid, inviteeUuid);

            if (success) {
                ctx.json(Map.of("success", true, "message", "Player invited successfully"));
            } else {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Failed to invite player"));
            }

        } catch (Exception e) {
            LOGGER.error("Error inviting player to group", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }

    /**
     * 踢出玩家
     * POST /api/groups/{groupId}/kick
     */
    public void handleKickPlayer(Context ctx) {
        try {
            String groupIdStr = ctx.pathParam("groupId");
            UUID groupId = UUID.fromString(groupIdStr);

            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            String kickerUuidStr = (String) requestData.get("kickerUuid");
            String kickeeUuidStr = (String) requestData.get("kickeeUuid");

            UUID kickerUuid = UUID.fromString(kickerUuidStr);
            UUID kickeeUuid = UUID.fromString(kickeeUuidStr);

            boolean success = voiceServer.getGroupManager().kickPlayer(groupId, kickerUuid, kickeeUuid);

            if (success) {
                broadcastGroupMemberUpdate(groupId);
                ctx.json(Map.of("success", true, "message", "Player kicked successfully"));
            } else {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Failed to kick player"));
            }

        } catch (Exception e) {
            LOGGER.error("Error kicking player from group", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }

    /**
     * 禁止玩家
     * POST /api/groups/{groupId}/ban
     */
    public void handleBanPlayer(Context ctx) {
        try {
            String groupIdStr = ctx.pathParam("groupId");
            UUID groupId = UUID.fromString(groupIdStr);

            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            String bannerUuidStr = (String) requestData.get("bannerUuid");
            String banneeUuidStr = (String) requestData.get("banneeUuid");

            UUID bannerUuid = UUID.fromString(bannerUuidStr);
            UUID banneeUuid = UUID.fromString(banneeUuidStr);

            boolean success = voiceServer.getGroupManager().banPlayer(groupId, bannerUuid, banneeUuid);

            if (success) {
                broadcastGroupMemberUpdate(groupId);
                ctx.json(Map.of("success", true, "message", "Player banned successfully"));
            } else {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Failed to ban player"));
            }

        } catch (Exception e) {
            LOGGER.error("Error banning player from group", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }

    /**
     * 设置管理员
     * POST /api/groups/{groupId}/moderator
     */
    public void handleSetModerator(Context ctx) {
        try {
            String groupIdStr = ctx.pathParam("groupId");
            UUID groupId = UUID.fromString(groupIdStr);

            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            String setterUuidStr = (String) requestData.get("setterUuid");
            String targetUuidStr = (String) requestData.get("targetUuid");
            Boolean isModerator = (Boolean) requestData.get("isModerator");

            UUID setterUuid = UUID.fromString(setterUuidStr);
            UUID targetUuid = UUID.fromString(targetUuidStr);

            boolean success = voiceServer.getGroupManager().setModerator(groupId, setterUuid, targetUuid, isModerator != null && isModerator);

            if (success) {
                ctx.json(Map.of("success", true, "message", "Moderator status updated successfully"));
            } else {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Failed to update moderator status"));
            }

        } catch (Exception e) {
            LOGGER.error("Error setting moderator status", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }

    /**
     * 转移群组所有权
     * POST /api/groups/{groupId}/transfer
     */
    public void handleTransferOwnership(Context ctx) {
        try {
            String groupIdStr = ctx.pathParam("groupId");
            UUID groupId = UUID.fromString(groupIdStr);

            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            String currentOwnerUuidStr = (String) requestData.get("currentOwnerUuid");
            String newOwnerUuidStr = (String) requestData.get("newOwnerUuid");

            UUID currentOwnerUuid = UUID.fromString(currentOwnerUuidStr);
            UUID newOwnerUuid = UUID.fromString(newOwnerUuidStr);

            boolean success = voiceServer.getGroupManager().transferOwnership(groupId, newOwnerUuid, currentOwnerUuid);

            if (success) {
                ctx.json(Map.of("success", true, "message", "Ownership transferred successfully"));
            } else {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Failed to transfer ownership"));
            }

        } catch (Exception e) {
            LOGGER.error("Error transferring group ownership", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }

    /**
     * 广播群组成员更新（当有成员加入或离开时）
     */
    private void broadcastGroupMemberUpdate(UUID groupId) {
        // 这里可以实现向适配器发送群组成员更新的通知
        // 暂时记录日志，后续可以通过WebSocket或其他方式通知适配器
        GroupManager.VoiceGroup group = voiceServer.getGroupManager().getGroup(groupId);
        if (group != null) {
            LOGGER.info("Group '{}' member list updated, {} members", group.getName(), group.getMembers().size());
        }
    }
}
