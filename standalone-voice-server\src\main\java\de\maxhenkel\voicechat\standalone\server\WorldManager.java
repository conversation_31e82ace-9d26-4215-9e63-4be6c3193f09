package de.maxhenkel.voicechat.standalone.server;

import de.maxhenkel.voicechat.standalone.model.PlayerData;
import de.maxhenkel.voicechat.standalone.model.Permission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 世界和服务器管理器
 * 负责管理多世界语音隔离和服务器间玩家隔离
 */
public class WorldManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(WorldManager.class);
    
    // 世界配置 <WorldName, WorldConfig>
    private final Map<String, WorldConfig> worldConfigs = new ConcurrentHashMap<>();
    
    // 服务器配置 <ServerName, ServerConfig>
    private final Map<String, ServerConfig> serverConfigs = new ConcurrentHashMap<>();
    
    // 跨世界语音规则 <FromWorld, Set<ToWorld>>
    private final Map<String, Set<String>> crossWorldRules = new ConcurrentHashMap<>();
    
    // 跨服务器语音规则 <FromServer, Set<ToServer>>
    private final Map<String, Set<String>> crossServerRules = new ConcurrentHashMap<>();
    
    private final PermissionManager permissionManager;
    
    public WorldManager(PermissionManager permissionManager) {
        this.permissionManager = permissionManager;
        initializeDefaultConfigs();
    }
    
    /**
     * 世界配置类
     */
    public static class WorldConfig {
        private final String worldName;
        private boolean voiceEnabled;
        private double maxVoiceDistance;
        private boolean groupsEnabled;
        private boolean crossWorldAllowed;
        private Set<String> allowedWorlds;
        
        public WorldConfig(String worldName) {
            this.worldName = worldName;
            this.voiceEnabled = true;
            this.maxVoiceDistance = 48.0;
            this.groupsEnabled = true;
            this.crossWorldAllowed = false;
            this.allowedWorlds = new HashSet<>();
        }
        
        // Getters and Setters
        public String getWorldName() { return worldName; }
        public boolean isVoiceEnabled() { return voiceEnabled; }
        public void setVoiceEnabled(boolean voiceEnabled) { this.voiceEnabled = voiceEnabled; }
        public double getMaxVoiceDistance() { return maxVoiceDistance; }
        public void setMaxVoiceDistance(double maxVoiceDistance) { this.maxVoiceDistance = maxVoiceDistance; }
        public boolean isGroupsEnabled() { return groupsEnabled; }
        public void setGroupsEnabled(boolean groupsEnabled) { this.groupsEnabled = groupsEnabled; }
        public boolean isCrossWorldAllowed() { return crossWorldAllowed; }
        public void setCrossWorldAllowed(boolean crossWorldAllowed) { this.crossWorldAllowed = crossWorldAllowed; }
        public Set<String> getAllowedWorlds() { return new HashSet<>(allowedWorlds); }
        public void setAllowedWorlds(Set<String> allowedWorlds) { this.allowedWorlds = new HashSet<>(allowedWorlds); }
        public void addAllowedWorld(String worldName) { this.allowedWorlds.add(worldName); }
        public void removeAllowedWorld(String worldName) { this.allowedWorlds.remove(worldName); }
    }
    
    /**
     * 服务器配置类
     */
    public static class ServerConfig {
        private final String serverName;
        private boolean voiceEnabled;
        private boolean crossServerAllowed;
        private Set<String> allowedServers;
        private Map<String, WorldConfig> worldConfigs;
        
        public ServerConfig(String serverName) {
            this.serverName = serverName;
            this.voiceEnabled = true;
            this.crossServerAllowed = false;
            this.allowedServers = new HashSet<>();
            this.worldConfigs = new HashMap<>();
        }
        
        // Getters and Setters
        public String getServerName() { return serverName; }
        public boolean isVoiceEnabled() { return voiceEnabled; }
        public void setVoiceEnabled(boolean voiceEnabled) { this.voiceEnabled = voiceEnabled; }
        public boolean isCrossServerAllowed() { return crossServerAllowed; }
        public void setCrossServerAllowed(boolean crossServerAllowed) { this.crossServerAllowed = crossServerAllowed; }
        public Set<String> getAllowedServers() { return new HashSet<>(allowedServers); }
        public void setAllowedServers(Set<String> allowedServers) { this.allowedServers = new HashSet<>(allowedServers); }
        public void addAllowedServer(String serverName) { this.allowedServers.add(serverName); }
        public void removeAllowedServer(String serverName) { this.allowedServers.remove(serverName); }
        public Map<String, WorldConfig> getWorldConfigs() { return new HashMap<>(worldConfigs); }
        public void addWorldConfig(WorldConfig worldConfig) { this.worldConfigs.put(worldConfig.getWorldName(), worldConfig); }
        public WorldConfig getWorldConfig(String worldName) { return worldConfigs.get(worldName); }
    }
    
    /**
     * 初始化默认配置
     */
    private void initializeDefaultConfigs() {
        // 创建默认世界配置
        WorldConfig defaultWorld = new WorldConfig("world");
        worldConfigs.put("world", defaultWorld);
        
        WorldConfig netherWorld = new WorldConfig("world_nether");
        netherWorld.setMaxVoiceDistance(24.0); // 地狱中语音距离更短
        worldConfigs.put("world_nether", netherWorld);
        
        WorldConfig endWorld = new WorldConfig("world_the_end");
        endWorld.setMaxVoiceDistance(64.0); // 末地中语音距离更远
        worldConfigs.put("world_the_end", endWorld);
        
        // 创建默认服务器配置
        ServerConfig defaultServer = new ServerConfig("default");
        defaultServer.addWorldConfig(defaultWorld);
        defaultServer.addWorldConfig(netherWorld);
        defaultServer.addWorldConfig(endWorld);
        serverConfigs.put("default", defaultServer);
        
        LOGGER.info("Initialized default world and server configurations");
    }
    
    /**
     * 检查两个玩家是否可以进行语音通信
     */
    public boolean canCommunicate(PlayerData sender, PlayerData receiver) {
        // 基本检查
        if (sender == null || receiver == null) {
            return false;
        }
        
        // 检查是否在同一服务器
        if (!sender.getServerName().equals(receiver.getServerName())) {
            return canCommunicateAcrossServers(sender, receiver);
        }
        
        // 检查是否在同一世界
        // 检查世界名称是否为null，如果为null则假设在同一世界
        String senderWorld = sender.getWorldName();
        String receiverWorld = receiver.getWorldName();

        if (senderWorld == null || receiverWorld == null) {
            // 如果任一玩家的世界名称为null，假设他们在同一世界（向后兼容）
            return true;
        }

        if (!senderWorld.equals(receiverWorld)) {
            return canCommunicateAcrossWorlds(sender, receiver);
        }
        
        // 同一世界内的通信检查
        return canCommunicateInSameWorld(sender, receiver);
    }
    
    /**
     * 检查是否可以跨服务器通信
     */
    private boolean canCommunicateAcrossServers(PlayerData sender, PlayerData receiver) {
        // 检查发送者是否有跨服务器权限
        if (!permissionManager.hasPermission(sender, Permission.CROSS_SERVER)) {
            return false;
        }
        
        // 检查接收者是否有跨服务器权限
        if (!permissionManager.hasPermission(receiver, Permission.CROSS_SERVER)) {
            return false;
        }
        
        // 检查服务器配置
        ServerConfig senderServerConfig = serverConfigs.get(sender.getServerName());
        if (senderServerConfig == null || !senderServerConfig.isCrossServerAllowed()) {
            return false;
        }
        
        ServerConfig receiverServerConfig = serverConfigs.get(receiver.getServerName());
        if (receiverServerConfig == null || !receiverServerConfig.isCrossServerAllowed()) {
            return false;
        }
        
        // 检查跨服务器规则
        Set<String> allowedServers = crossServerRules.get(sender.getServerName());
        if (allowedServers != null && !allowedServers.contains(receiver.getServerName())) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查是否可以跨世界通信
     */
    private boolean canCommunicateAcrossWorlds(PlayerData sender, PlayerData receiver) {
        // 检查发送者是否有跨世界权限
        if (!permissionManager.hasPermission(sender, Permission.CROSS_WORLD)) {
            return false;
        }

        // 检查接收者是否有跨世界权限
        if (!permissionManager.hasPermission(receiver, Permission.CROSS_WORLD)) {
            return false;
        }

        // 获取世界名称
        String senderWorld = sender.getWorldName();
        String receiverWorld = receiver.getWorldName();

        // 检查世界配置
        if (senderWorld != null) {
            WorldConfig senderWorldConfig = worldConfigs.get(senderWorld);
            if (senderWorldConfig == null || !senderWorldConfig.isCrossWorldAllowed()) {
                return false;
            }
        }

        if (receiverWorld != null) {
            WorldConfig receiverWorldConfig = worldConfigs.get(receiverWorld);
            if (receiverWorldConfig == null || !receiverWorldConfig.isCrossWorldAllowed()) {
                return false;
            }
        }

        // 检查跨世界规则
        if (senderWorld != null && receiverWorld != null) {
            Set<String> allowedWorlds = crossWorldRules.get(senderWorld);
            if (allowedWorlds != null && !allowedWorlds.contains(receiverWorld)) {
                return false;
            }
        }

        return true;
    }
    
    /**
     * 检查同一世界内是否可以通信
     */
    private boolean canCommunicateInSameWorld(PlayerData sender, PlayerData receiver) {
        String senderWorld = sender.getWorldName();
        if (senderWorld == null) {
            return true; // 如果世界名称为null，默认允许
        }

        WorldConfig worldConfig = worldConfigs.get(senderWorld);
        if (worldConfig == null) {
            return true; // 如果没有配置，默认允许
        }

        return worldConfig.isVoiceEnabled();
    }
    
    /**
     * 获取世界的最大语音距离
     */
    public double getMaxVoiceDistance(String worldName) {
        WorldConfig worldConfig = worldConfigs.get(worldName);
        return worldConfig != null ? worldConfig.getMaxVoiceDistance() : 48.0;
    }
    
    /**
     * 检查世界是否启用群组功能
     */
    public boolean isGroupsEnabled(String worldName) {
        WorldConfig worldConfig = worldConfigs.get(worldName);
        return worldConfig == null || worldConfig.isGroupsEnabled();
    }
    
    /**
     * 添加跨世界规则
     */
    public void addCrossWorldRule(String fromWorld, String toWorld) {
        crossWorldRules.computeIfAbsent(fromWorld, k -> ConcurrentHashMap.newKeySet()).add(toWorld);
        LOGGER.info("Added cross-world rule: {} -> {}", fromWorld, toWorld);
    }
    
    /**
     * 移除跨世界规则
     */
    public void removeCrossWorldRule(String fromWorld, String toWorld) {
        Set<String> allowedWorlds = crossWorldRules.get(fromWorld);
        if (allowedWorlds != null) {
            allowedWorlds.remove(toWorld);
            if (allowedWorlds.isEmpty()) {
                crossWorldRules.remove(fromWorld);
            }
        }
        LOGGER.info("Removed cross-world rule: {} -> {}", fromWorld, toWorld);
    }
    
    /**
     * 添加跨服务器规则
     */
    public void addCrossServerRule(String fromServer, String toServer) {
        crossServerRules.computeIfAbsent(fromServer, k -> ConcurrentHashMap.newKeySet()).add(toServer);
        LOGGER.info("Added cross-server rule: {} -> {}", fromServer, toServer);
    }
    
    /**
     * 移除跨服务器规则
     */
    public void removeCrossServerRule(String fromServer, String toServer) {
        Set<String> allowedServers = crossServerRules.get(fromServer);
        if (allowedServers != null) {
            allowedServers.remove(toServer);
            if (allowedServers.isEmpty()) {
                crossServerRules.remove(fromServer);
            }
        }
        LOGGER.info("Removed cross-server rule: {} -> {}", fromServer, toServer);
    }
    
    /**
     * 获取世界配置
     */
    public WorldConfig getWorldConfig(String worldName) {
        return worldConfigs.get(worldName);
    }
    
    /**
     * 设置世界配置
     */
    public void setWorldConfig(WorldConfig worldConfig) {
        worldConfigs.put(worldConfig.getWorldName(), worldConfig);
        LOGGER.info("Updated world config for: {}", worldConfig.getWorldName());
    }
    
    /**
     * 获取服务器配置
     */
    public ServerConfig getServerConfig(String serverName) {
        return serverConfigs.get(serverName);
    }
    
    /**
     * 设置服务器配置
     */
    public void setServerConfig(ServerConfig serverConfig) {
        serverConfigs.put(serverConfig.getServerName(), serverConfig);
        LOGGER.info("Updated server config for: {}", serverConfig.getServerName());
    }
    
    /**
     * 获取统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalWorlds", worldConfigs.size());
        stats.put("totalServers", serverConfigs.size());
        stats.put("crossWorldRules", crossWorldRules.size());
        stats.put("crossServerRules", crossServerRules.size());
        
        Map<String, Object> worldStats = new HashMap<>();
        for (WorldConfig config : worldConfigs.values()) {
            Map<String, Object> worldInfo = new HashMap<>();
            worldInfo.put("voiceEnabled", config.isVoiceEnabled());
            worldInfo.put("maxDistance", config.getMaxVoiceDistance());
            worldInfo.put("groupsEnabled", config.isGroupsEnabled());
            worldInfo.put("crossWorldAllowed", config.isCrossWorldAllowed());
            worldStats.put(config.getWorldName(), worldInfo);
        }
        stats.put("worlds", worldStats);
        
        return stats;
    }

    /**
     * 从Adapter更新世界配置
     */
    public void updateWorldConfigFromAdapter(String worldName, Map<String, Object> configData) {
        WorldConfig config = worldConfigs.computeIfAbsent(worldName, k -> new WorldConfig(worldName));

        // 更新配置值
        if (configData.containsKey("voiceEnabled")) {
            config.setVoiceEnabled((Boolean) configData.get("voiceEnabled"));
        }
        if (configData.containsKey("maxVoiceDistance")) {
            Number distance = (Number) configData.get("maxVoiceDistance");
            config.setMaxVoiceDistance(distance.doubleValue());
        }
        if (configData.containsKey("groupsEnabled")) {
            config.setGroupsEnabled((Boolean) configData.get("groupsEnabled"));
        }
        if (configData.containsKey("crossWorldAllowed")) {
            config.setCrossWorldAllowed((Boolean) configData.get("crossWorldAllowed"));
        }

        worldConfigs.put(worldName, config);
        LOGGER.info("Updated world config for {} from adapter: voiceEnabled={}, maxDistance={}",
                   worldName, config.isVoiceEnabled(), config.getMaxVoiceDistance());
    }

    /**
     * 获取所有世界配置（用于API）
     */
    public Map<String, WorldConfig> getAllWorldConfigs() {
        return new HashMap<>(worldConfigs);
    }
}
