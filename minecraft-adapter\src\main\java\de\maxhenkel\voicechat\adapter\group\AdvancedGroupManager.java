package de.maxhenkel.voicechat.adapter.group;

import de.maxhenkel.voicechat.adapter.network.VoiceServerClient;
import org.bukkit.entity.Player;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.UUID;

/**
 * 高级群组管理器
 * 提供群组邀请、踢人、禁止等高级功能
 */
public class AdvancedGroupManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(AdvancedGroupManager.class);
    
    private final VoiceServerClient voiceServerClient;
    
    public AdvancedGroupManager(VoiceServerClient voiceServerClient) {
        this.voiceServerClient = voiceServerClient;
    }
    
    /**
     * 邀请玩家加入群组
     */
    public boolean invitePlayer(Player inviter, Player invitee, UUID groupId) {
        if (inviter == null || invitee == null || groupId == null) {
            return false;
        }
        
        try {
            boolean success = voiceServerClient.invitePlayerToGroup(groupId, inviter.getUniqueId(), invitee.getUniqueId());
            
            if (success) {
                // 发送消息通知
                inviter.sendMessage("§a已邀请 " + invitee.getName() + " 加入群组");
                invitee.sendMessage("§a" + inviter.getName() + " 邀请你加入语音群组");
                
                LOGGER.info("Player {} invited {} to group {}", inviter.getName(), invitee.getName(), groupId);
            } else {
                inviter.sendMessage("§c邀请失败，可能是权限不足或群组已满");
            }
            
            return success;
            
        } catch (Exception e) {
            LOGGER.error("Failed to invite player {} to group {}", invitee.getName(), groupId, e);
            inviter.sendMessage("§c邀请失败：" + e.getMessage());
            return false;
        }
    }
    
    /**
     * 踢出玩家
     */
    public boolean kickPlayer(Player kicker, Player kickee, UUID groupId) {
        if (kicker == null || kickee == null || groupId == null) {
            return false;
        }
        
        try {
            boolean success = voiceServerClient.kickPlayerFromGroup(groupId, kicker.getUniqueId(), kickee.getUniqueId());
            
            if (success) {
                // 发送消息通知
                kicker.sendMessage("§a已将 " + kickee.getName() + " 踢出群组");
                kickee.sendMessage("§c你被 " + kicker.getName() + " 踢出了语音群组");
                
                LOGGER.info("Player {} kicked {} from group {}", kicker.getName(), kickee.getName(), groupId);
            } else {
                kicker.sendMessage("§c踢出失败，可能是权限不足");
            }
            
            return success;
            
        } catch (Exception e) {
            LOGGER.error("Failed to kick player {} from group {}", kickee.getName(), groupId, e);
            kicker.sendMessage("§c踢出失败：" + e.getMessage());
            return false;
        }
    }
    
    /**
     * 禁止玩家
     */
    public boolean banPlayer(Player banner, Player bannee, UUID groupId) {
        if (banner == null || bannee == null || groupId == null) {
            return false;
        }
        
        try {
            boolean success = voiceServerClient.banPlayerFromGroup(groupId, banner.getUniqueId(), bannee.getUniqueId());
            
            if (success) {
                // 发送消息通知
                banner.sendMessage("§a已禁止 " + bannee.getName() + " 加入群组");
                bannee.sendMessage("§c你被 " + banner.getName() + " 禁止加入该语音群组");
                
                LOGGER.info("Player {} banned {} from group {}", banner.getName(), bannee.getName(), groupId);
            } else {
                banner.sendMessage("§c禁止失败，可能是权限不足");
            }
            
            return success;
            
        } catch (Exception e) {
            LOGGER.error("Failed to ban player {} from group {}", bannee.getName(), groupId, e);
            banner.sendMessage("§c禁止失败：" + e.getMessage());
            return false;
        }
    }
    
    /**
     * 设置群组管理员
     */
    public boolean setModerator(Player setter, Player target, UUID groupId, boolean isModerator) {
        if (setter == null || target == null || groupId == null) {
            return false;
        }
        
        try {
            boolean success = voiceServerClient.setGroupModerator(groupId, setter.getUniqueId(), target.getUniqueId(), isModerator);
            
            if (success) {
                String action = isModerator ? "设置为" : "取消";
                String status = isModerator ? "管理员" : "普通成员";
                
                // 发送消息通知
                setter.sendMessage("§a已将 " + target.getName() + " " + action + "群组管理员");
                target.sendMessage("§a你被 " + setter.getName() + " " + action + "群组管理员");
                
                LOGGER.info("Player {} set {} as moderator in group {}: {}", setter.getName(), target.getName(), groupId, isModerator);
            } else {
                setter.sendMessage("§c操作失败，可能是权限不足");
            }
            
            return success;
            
        } catch (Exception e) {
            LOGGER.error("Failed to set moderator status for player {} in group {}", target.getName(), groupId, e);
            setter.sendMessage("§c操作失败：" + e.getMessage());
            return false;
        }
    }
    
    /**
     * 转移群组所有权
     */
    public boolean transferOwnership(Player currentOwner, Player newOwner, UUID groupId) {
        if (currentOwner == null || newOwner == null || groupId == null) {
            return false;
        }
        
        try {
            boolean success = voiceServerClient.transferGroupOwnership(groupId, currentOwner.getUniqueId(), newOwner.getUniqueId());
            
            if (success) {
                // 发送消息通知
                currentOwner.sendMessage("§a已将群组所有权转移给 " + newOwner.getName());
                newOwner.sendMessage("§a" + currentOwner.getName() + " 将群组所有权转移给了你");
                
                LOGGER.info("Group {} ownership transferred from {} to {}", groupId, currentOwner.getName(), newOwner.getName());
            } else {
                currentOwner.sendMessage("§c转移失败，可能是权限不足或目标玩家不在群组中");
            }
            
            return success;
            
        } catch (Exception e) {
            LOGGER.error("Failed to transfer ownership of group {} from {} to {}", groupId, currentOwner.getName(), newOwner.getName(), e);
            currentOwner.sendMessage("§c转移失败：" + e.getMessage());
            return false;
        }
    }
    
    /**
     * 通过玩家名称邀请玩家
     */
    public boolean invitePlayerByName(Player inviter, String inviteeName, UUID groupId) {
        Player invitee = org.bukkit.Bukkit.getPlayer(inviteeName);
        if (invitee == null) {
            inviter.sendMessage("§c玩家 " + inviteeName + " 不在线");
            return false;
        }
        
        return invitePlayer(inviter, invitee, groupId);
    }
    
    /**
     * 通过玩家名称踢出玩家
     */
    public boolean kickPlayerByName(Player kicker, String kickeeName, UUID groupId) {
        Player kickee = org.bukkit.Bukkit.getPlayer(kickeeName);
        if (kickee == null) {
            kicker.sendMessage("§c玩家 " + kickeeName + " 不在线");
            return false;
        }
        
        return kickPlayer(kicker, kickee, groupId);
    }
    
    /**
     * 通过玩家名称禁止玩家
     */
    public boolean banPlayerByName(Player banner, String banneeName, UUID groupId) {
        Player bannee = org.bukkit.Bukkit.getPlayer(banneeName);
        if (bannee == null) {
            banner.sendMessage("§c玩家 " + banneeName + " 不在线");
            return false;
        }
        
        return banPlayer(banner, bannee, groupId);
    }
    
    /**
     * 通过玩家名称设置管理员
     */
    public boolean setModeratorByName(Player setter, String targetName, UUID groupId, boolean isModerator) {
        Player target = org.bukkit.Bukkit.getPlayer(targetName);
        if (target == null) {
            setter.sendMessage("§c玩家 " + targetName + " 不在线");
            return false;
        }
        
        return setModerator(setter, target, groupId, isModerator);
    }
    
    /**
     * 通过玩家名称转移所有权
     */
    public boolean transferOwnershipByName(Player currentOwner, String newOwnerName, UUID groupId) {
        Player newOwner = org.bukkit.Bukkit.getPlayer(newOwnerName);
        if (newOwner == null) {
            currentOwner.sendMessage("§c玩家 " + newOwnerName + " 不在线");
            return false;
        }
        
        return transferOwnership(currentOwner, newOwner, groupId);
    }
    
    /**
     * 检查玩家是否有群组管理权限
     */
    public boolean hasGroupManagePermission(Player player) {
        return player.hasPermission("voicechat.groups.manage") || player.hasPermission("voicechat.admin") || player.isOp();
    }
    
    /**
     * 检查玩家是否有群组创建权限
     */
    public boolean hasGroupCreatePermission(Player player) {
        return player.hasPermission("voicechat.groups.create") || player.hasPermission("voicechat.groups") || player.hasPermission("voicechat.admin") || player.isOp();
    }
    
    /**
     * 检查玩家是否有群组加入权限
     */
    public boolean hasGroupJoinPermission(Player player) {
        return player.hasPermission("voicechat.groups.join") || player.hasPermission("voicechat.groups") || player.hasPermission("voicechat.admin") || player.isOp();
    }
}
