# 独立语音服务器 (Standalone Voice Server)

这是从 Simple Voice Chat 项目中独立出来的语音服务器，可以独立于 Minecraft 服务器运行，支持多个 Minecraft 服务器共享同一个语音服务。

## 特性

- **独立运行**: 不依赖 Minecraft 服务器，可以独立部署
- **多服务器支持**: 支持多个 Minecraft 服务器连接到同一个语音服务器
- **HTTP API**: 提供 RESTful API 与 Minecraft 服务器通信
- **实时语音**: 支持近距离语音聊天和群组聊天
- **高性能**: 使用 Netty 处理 UDP 网络通信
- **可配置**: 丰富的配置选项

## 系统要求

- Java 17 或更高版本
- 至少 512MB 内存
- 开放的 UDP 端口（默认 24454）
- 开放的 HTTP 端口（默认 8080）

## 快速开始

### 1. 构建项目

```bash
./gradlew shadowJar
```

### 2. 配置服务器

编辑 `voice-server.yml` 配置文件：

```yaml
server:
  host: "0.0.0.0"
  port: 24454

api:
  host: "0.0.0.0"
  port: 8080
  auth_token: "your-secret-token"

voice:
  max_distance: 48.0
  groups_enabled: true
```

### 3. 启动服务器

**Linux/macOS:**
```bash
chmod +x start.sh
./start.sh
```

**Windows:**
```cmd
start.bat
```

**手动启动:**
```bash
java -jar standalone-voice-server-1.0.0.jar voice-server.yml
```

## 配置说明

### 服务器配置 (server)

- `host`: 绑定地址，`0.0.0.0` 表示绑定所有网络接口
- `port`: UDP 端口，用于语音数据传输
- `bind_address`: 特定绑定地址，留空使用 host

### API 配置 (api)

- `host`: API 服务器地址
- `port`: API 服务器端口
- `auth_token`: 认证令牌，Minecraft 服务器需要使用此令牌访问 API

### 语音配置 (voice)

- `codec`: 音频编解码器 (VOIP, AUDIO, RESTRICTED_LOWDELAY)
- `mtu_size`: 最大传输单元大小（字节）
- `keep_alive`: 心跳间隔（毫秒）
- `max_distance`: 最大语音距离
- `groups_enabled`: 是否启用群组聊天
- `allow_recording`: 是否允许录音

### 安全配置 (security)

- `encryption_enabled`: 是否启用加密
- `auth_timeout`: 认证超时时间（毫秒）

## API 接口

### 玩家管理

- `POST /api/players/login` - 玩家登录
- `POST /api/players/logout` - 玩家登出
- `PUT /api/players/{uuid}/position` - 更新玩家位置
- `PUT /api/players/{uuid}/permissions` - 更新玩家权限
- `GET /api/players/{uuid}/state` - 获取玩家状态
- `POST /api/players/{uuid}/secret` - 生成认证密钥

### 群组管理

- `POST /api/groups` - 创建群组
- `DELETE /api/groups/{groupId}` - 删除群组
- `POST /api/groups/{groupId}/members` - 加入群组
- `DELETE /api/groups/{groupId}/members/{playerId}` - 离开群组

### 系统状态

- `GET /health` - 健康检查
- `GET /api/status` - 服务器状态
- `GET /api/config` - 获取配置

## 与 Minecraft 服务器集成

### 1. 安装适配器插件

在 Minecraft 服务器上安装对应的适配器插件（需要单独开发）。

### 2. 配置 Minecraft 服务器

```yaml
voicechat:
  mode: "standalone"
  standalone:
    voice_server_host: "voice.example.com"
    voice_server_port: 24454
    api_endpoint: "http://voice.example.com:8080"
    auth_token: "your-secret-token"
```

### 3. 客户端配置

客户端会自动从 Minecraft 服务器获取语音服务器地址并连接。

## 部署架构

### 单服务器部署

```
┌─────────────────────────────────────┐
│  物理服务器                          │
│  ┌─────────────────┐                │
│  │ Minecraft Server│                │
│  │ Port: 25565     │                │
│  └─────────────────┘                │
│  ┌─────────────────┐                │
│  │ Voice Server    │                │
│  │ UDP: 24454      │                │
│  │ HTTP: 8080      │                │
│  └─────────────────┘                │
└─────────────────────────────────────┘
```

### 分布式部署

```
┌─────────────────┐    ┌─────────────────┐
│ Minecraft       │    │ Minecraft       │
│ Server 1        │    │ Server 2        │
│ Port: 25565     │    │ Port: 25565     │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────────────────┼───────────────────────┐
                                 │                       │
                    ┌─────────────────┐                 │
                    │ Voice Server    │                 │
                    │ UDP: 24454      │                 │
                    │ HTTP: 8080      │                 │
                    └─────────────────┘                 │
```

## 监控和日志

### 日志文件

- `logs/voice-server.log` - 主日志文件
- `logs/voice-server-error.log` - 错误日志文件

### 监控端点

- `GET /health` - 健康检查
- `GET /api/status` - 详细状态信息

## 故障排除

### 常见问题

1. **端口被占用**
   - 检查 UDP 24454 和 HTTP 8080 端口是否被其他程序占用
   - 修改配置文件中的端口号

2. **连接超时**
   - 检查防火墙设置
   - 确认网络连通性

3. **认证失败**
   - 检查 auth_token 是否正确
   - 确认 Minecraft 服务器配置

### 调试模式

修改 `logback.xml` 中的日志级别为 DEBUG：

```xml
<logger name="de.maxhenkel.voicechat.standalone" level="DEBUG" />
```

## 性能优化

### JVM 参数

```bash
-Xmx2G -Xms1G
-XX:+UseG1GC
-XX:MaxGCPauseMillis=100
-XX:+UnlockExperimentalVMOptions
```

### 网络优化

- 使用高性能网络接口
- 调整 MTU 大小
- 优化心跳间隔

## 开发

### 构建

```bash
./gradlew build
```

### 测试

```bash
./gradlew test
```

### 打包

```bash
./gradlew shadowJar
```

## 许可证

本项目基于原 Simple Voice Chat 项目的许可证。

## 贡献

欢迎提交 Issue 和 Pull Request！
