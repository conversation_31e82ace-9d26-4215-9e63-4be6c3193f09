package de.maxhenkel.voicechat.adapter.util;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

/**
 * 简化的FriendlyByteBuf实现，用于解析和构建Voice Chat数据包
 */
public class FriendlyByteBuf implements AutoCloseable {

    private final DataInputStream input;
    private final ByteArrayInputStream byteStream;
    private final ByteArrayOutputStream outputStream;
    private final DataOutputStream output;
    
    public FriendlyByteBuf(byte[] data) {
        this.byteStream = new ByteArrayInputStream(data);
        this.input = new DataInputStream(byteStream);
        this.outputStream = null;
        this.output = null;
    }

    public FriendlyByteBuf() {
        this.byteStream = null;
        this.input = null;
        this.outputStream = new ByteArrayOutputStream();
        this.output = new DataOutputStream(outputStream);
    }
    
    /**
     * 读取VarInt长度前缀的UTF-8字符串
     */
    public String readUtf(int maxLength) throws IOException {
        int length = readVarInt();
        if (length > maxLength * 4) {
            throw new IOException("String too big (was " + length + " bytes encoded, max " + maxLength + ")");
        }
        
        byte[] bytes = new byte[length];
        input.readFully(bytes);
        String result = new String(bytes, StandardCharsets.UTF_8);
        
        if (result.length() > maxLength) {
            throw new IOException("String too big (was " + result.length() + " characters, max " + maxLength + ")");
        }
        
        return result;
    }

    /**
     * 读取UTF-8字符串（默认最大长度32767）
     */
    public String readUtf() throws IOException {
        return readUtf(32767);
    }
    
    /**
     * 读取布尔值
     */
    public boolean readBoolean() throws IOException {
        return input.readBoolean();
    }
    
    /**
     * 读取短整型
     */
    public short readShort() throws IOException {
        return input.readShort();
    }
    
    /**
     * 读取长整型
     */
    public long readLong() throws IOException {
        return input.readLong();
    }
    
    /**
     * 读取UUID
     */
    public UUID readUUID() throws IOException {
        long mostSig = input.readLong();
        long leastSig = input.readLong();
        return new UUID(mostSig, leastSig);
    }
    
    /**
     * 读取VarInt
     */
    public int readVarInt() throws IOException {
        int i = 0;
        int j = 0;
        
        byte b;
        do {
            b = input.readByte();
            i |= (b & 127) << j++ * 7;
            if (j > 5) {
                throw new RuntimeException("VarInt too big");
            }
        } while ((b & 128) == 128);
        
        return i;
    }
    
    /**
     * 检查是否还有可读数据
     */
    public int available() throws IOException {
        return input.available();
    }
    
    /**
     * 写入VarInt长度前缀的UTF-8字符串（默认最大长度32767）
     */
    public void writeUtf(String str) throws IOException {
        writeUtf(str, 32767);
    }

    /**
     * 写入VarInt长度前缀的UTF-8字符串
     */
    public void writeUtf(String str, int maxLength) throws IOException {
        if (output == null) {
            throw new IllegalStateException("Cannot write to read-only buffer");
        }

        byte[] bytes = str.getBytes(StandardCharsets.UTF_8);
        if (bytes.length > maxLength * 4) {
            throw new IOException("String too big (was " + bytes.length + " bytes encoded, max " + maxLength + ")");
        }

        writeVarInt(bytes.length);
        output.write(bytes);
    }

    /**
     * 写入int值
     */
    public void writeInt(int value) throws IOException {
        if (output == null) {
            throw new IllegalStateException("Cannot write to read-only buffer");
        }
        output.writeInt(value);
    }

    /**
     * 读取int值
     */
    public int readInt() throws IOException {
        if (input == null) {
            throw new IllegalStateException("Cannot read from write-only buffer");
        }
        return input.readInt();
    }



    /**
     * 写入布尔值
     */
    public void writeBoolean(boolean value) throws IOException {
        if (output == null) {
            throw new IllegalStateException("Cannot write to read-only buffer");
        }
        output.writeBoolean(value);
    }

    /**
     * 写入UUID
     */
    public void writeUUID(UUID uuid) throws IOException {
        if (output == null) {
            throw new IllegalStateException("Cannot write to read-only buffer");
        }
        output.writeLong(uuid.getMostSignificantBits());
        output.writeLong(uuid.getLeastSignificantBits());
    }

    /**
     * 写入短整型
     */
    public void writeShort(short value) throws IOException {
        if (output == null) {
            throw new IllegalStateException("Cannot write to read-only buffer");
        }
        output.writeShort(value);
    }

    /**
     * 写入VarInt
     */
    public void writeVarInt(int value) throws IOException {
        if (output == null) {
            throw new IllegalStateException("Cannot write to read-only buffer");
        }

        while ((value & -128) != 0) {
            output.writeByte(value & 127 | 128);
            value >>>= 7;
        }
        output.writeByte(value);
    }

    /**
     * 获取写入的字节数组
     */
    public byte[] toByteArray() throws IOException {
        if (outputStream == null) {
            throw new IllegalStateException("Cannot get bytes from read-only buffer");
        }
        output.flush();
        return outputStream.toByteArray();
    }



    /**
     * 关闭流
     */
    public void close() throws IOException {
        if (input != null) {
            input.close();
        }
        if (byteStream != null) {
            byteStream.close();
        }
        if (output != null) {
            output.close();
        }
        if (outputStream != null) {
            outputStream.close();
        }
    }
}
