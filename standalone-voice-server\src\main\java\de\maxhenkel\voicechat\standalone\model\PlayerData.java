package de.maxhenkel.voicechat.standalone.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 玩家数据模型
 */
public class PlayerData {
    
    @JsonProperty("uuid")
    private UUID uuid;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("position")
    private Position position;
    
    @JsonProperty("permissions")
    private Set<Permission> permissions = ConcurrentHashMap.newKeySet();
    
    @JsonProperty("online")
    private boolean online;
    
    @JsonProperty("last_update")
    private long lastUpdate;
    
    @JsonProperty("server_name")
    private String serverName;

    @JsonProperty("world_name")
    private String worldName;

    @JsonProperty("group_id")
    private UUID groupId;
    
    @JsonProperty("voice_disabled")
    private boolean voiceDisabled;
    
    @JsonProperty("voice_connected")
    private boolean voiceConnected;

    @JsonProperty("game_mode")
    private int gameMode; // 0=生存, 1=创造, 2=冒险, 3=旁观

    public PlayerData() {
        this.lastUpdate = System.currentTimeMillis();
        this.gameMode = 0; // 默认生存模式
    }
    
    public PlayerData(UUID uuid, String name, String serverName) {
        this();
        this.uuid = uuid;
        this.name = name;
        this.serverName = serverName;
        this.online = true;
    }
    
    // Getters and Setters
    public UUID getUuid() { return uuid; }
    public void setUuid(UUID uuid) { this.uuid = uuid; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public Position getPosition() { return position; }
    public void setPosition(Position position) { 
        this.position = position;
        this.lastUpdate = System.currentTimeMillis();
    }
    
    public Set<Permission> getPermissions() { return permissions; }
    public void setPermissions(Set<Permission> permissions) { this.permissions = permissions; }
    
    public boolean isOnline() { return online; }
    public void setOnline(boolean online) { 
        this.online = online;
        this.lastUpdate = System.currentTimeMillis();
    }
    
    public long getLastUpdate() { return lastUpdate; }
    public void setLastUpdate(long lastUpdate) { this.lastUpdate = lastUpdate; }
    
    public String getServerName() { return serverName; }
    public void setServerName(String serverName) { this.serverName = serverName; }

    public String getWorldName() { return worldName; }
    public void setWorldName(String worldName) { this.worldName = worldName; }

    public UUID getGroupId() { return groupId; }
    public void setGroupId(UUID groupId) { this.groupId = groupId; }
    
    public boolean isVoiceDisabled() { return voiceDisabled; }
    public void setVoiceDisabled(boolean voiceDisabled) { this.voiceDisabled = voiceDisabled; }
    
    public boolean isVoiceConnected() { return voiceConnected; }
    public void setVoiceConnected(boolean voiceConnected) { this.voiceConnected = voiceConnected; }
    
    // Utility methods
    public boolean hasPermission(Permission permission) {
        return permissions.contains(permission);
    }
    
    public void addPermission(Permission permission) {
        permissions.add(permission);
    }
    
    public void removePermission(Permission permission) {
        permissions.remove(permission);
    }
    
    public boolean hasGroup() {
        return groupId != null;
    }
    
    public boolean isInSameWorld(PlayerData other) {
        if (position == null || other.position == null) {
            return false;
        }
        return position.getWorldId().equals(other.position.getWorldId());
    }
    
    public double getDistanceTo(PlayerData other) {
        if (!isInSameWorld(other)) {
            return Double.MAX_VALUE;
        }
        return position.getDistanceTo(other.position);
    }
    
    public boolean isWithinVoiceRange(PlayerData other, double maxDistance) {
        return getDistanceTo(other) <= maxDistance;
    }

    public int getGameMode() { return gameMode; }
    public void setGameMode(int gameMode) { this.gameMode = gameMode; }

    /**
     * 检查是否是观察者模式
     */
    public boolean isSpectator() {
        return gameMode == 3;
    }

    /**
     * 检查是否是创造模式
     */
    public boolean isCreative() {
        return gameMode == 1;
    }

    @Override
    public String toString() {
        return "PlayerData{" +
                "uuid=" + uuid +
                ", name='" + name + '\'' +
                ", online=" + online +
                ", serverName='" + serverName + '\'' +
                ", voiceConnected=" + voiceConnected +
                '}';
    }
}
