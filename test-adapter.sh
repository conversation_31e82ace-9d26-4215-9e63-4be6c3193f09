#!/bin/bash

# 适配器测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查独立语音服务器
check_voice_server() {
    log_step "检查独立语音服务器..."
    
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        log_info "独立语音服务器运行正常 ✓"
        return 0
    else
        log_error "独立语音服务器未运行"
        return 1
    fi
}

# 检查适配器JAR文件
check_adapter_jar() {
    log_step "检查适配器JAR文件..."
    
    JAR_FILE="minecraft-adapter/build/libs/minecraft-adapter-1.0.0.jar"
    if [ -f "$JAR_FILE" ]; then
        SIZE=$(ls -lh "$JAR_FILE" | awk '{print $5}')
        log_info "适配器JAR文件存在: $SIZE ✓"
        return 0
    else
        log_error "适配器JAR文件不存在: $JAR_FILE"
        return 1
    fi
}

# 测试适配器配置
test_adapter_config() {
    log_step "测试适配器配置..."
    
    # 创建测试配置
    cat > test-adapter-config.yml << 'EOF'
# 语音服务器配置
voice-server:
  host: "localhost"
  port: 24454
  api-endpoint: "http://localhost:8080"
  auth-token: "change-this-secret-token"

# 同步配置
sync:
  position-interval: 1000
  permission-interval: 5000
  sync-on-move: true
  min-move-distance: 1.0

# 服务器标识
server-name: "test-server"
EOF
    
    log_info "测试配置文件已创建: test-adapter-config.yml ✓"
}

# 显示部署说明
show_deployment_instructions() {
    log_step "部署说明"
    
    echo "=================================="
    echo "🎉 适配器修复完成！"
    echo "=================================="
    echo ""
    echo "📦 修复内容："
    echo "1. ✅ 修复了数据包格式兼容性问题"
    echo "2. ✅ 使用正确的Minecraft网络协议"
    echo "3. ✅ 添加了连接状态检查"
    echo "4. ✅ 改进了错误处理和日志记录"
    echo ""
    echo "🚀 部署步骤："
    echo "1. 停止Paper服务器"
    echo "2. 复制适配器JAR到plugins目录:"
    echo "   cp minecraft-adapter/build/libs/minecraft-adapter-1.0.0.jar /path/to/paper/plugins/"
    echo ""
    echo "3. 配置适配器 (plugins/VoiceChatAdapter/config.yml):"
    echo "   voice-server:"
    echo "     host: \"localhost\""
    echo "     port: 24454"
    echo "     api-endpoint: \"http://localhost:8080\""
    echo "     auth-token: \"change-this-secret-token\""
    echo ""
    echo "4. 启动独立语音服务器:"
    echo "   cd standalone-voice-server/build/libs"
    echo "   java -jar standalone-voice-server-1.0.0.jar voice-server.yml"
    echo ""
    echo "5. 启动Paper服务器"
    echo ""
    echo "🔍 预期结果："
    echo "- 客户端不再出现数据包大小错误"
    echo "- 玩家可以正常连接语音聊天"
    echo "- 服务器日志显示成功发送SecretPacket"
    echo ""
    echo "📋 主要修复："
    echo "- 使用VarInt编码字符串长度（Minecraft标准）"
    echo "- 完全兼容原版客户端的数据包格式"
    echo "- 通过voiceHost字段传递独立服务器地址"
    echo "- 不添加任何额外字段，保持向后兼容"
    echo "=================================="
}

# 主函数
main() {
    echo "适配器修复验证"
    echo "=============="
    
    check_adapter_jar
    test_adapter_config
    
    if check_voice_server; then
        log_info "环境检查通过，可以部署适配器"
    else
        log_warn "独立语音服务器未运行，请先启动语音服务器"
    fi
    
    show_deployment_instructions
}

# 执行主函数
main
