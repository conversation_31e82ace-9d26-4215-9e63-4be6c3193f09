package de.maxhenkel.voicechat.standalone.audio;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Opus编解码器管理器
 * 负责管理Opus编解码器的创建、配置和生命周期
 */
public class OpusManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(OpusManager.class);
    
    // 音频参数常量
    public static final int SAMPLE_RATE = 48000; // 48kHz采样率
    public static final int CHANNELS = 1; // 单声道
    public static final int FRAME_SIZE_MS = 20; // 20ms帧大小
    public static final int FRAME_SIZE = (SAMPLE_RATE / 1000) * FRAME_SIZE_MS; // 960 samples
    
    // 编解码器缓存 <CodecType, OpusCodecConfig>
    private final Map<OpusCodec, OpusCodecConfig> codecConfigs = new ConcurrentHashMap<>();
    
    // 当前使用的编解码器类型
    private OpusCodec currentCodec = OpusCodec.VOIP;
    
    public OpusManager() {
        initializeCodecConfigs();
    }
    
    /**
     * 初始化编解码器配置
     */
    private void initializeCodecConfigs() {
        for (OpusCodec codec : OpusCodec.values()) {
            OpusCodecConfig config = new OpusCodecConfig(codec);
            codecConfigs.put(codec, config);
        }
        LOGGER.info("Initialized Opus codec configurations for {} codecs", codecConfigs.size());
    }
    
    /**
     * 设置当前使用的编解码器
     */
    public void setCurrentCodec(OpusCodec codec) {
        if (codec == null) {
            codec = OpusCodec.VOIP;
        }
        
        this.currentCodec = codec;
        LOGGER.info("Set current Opus codec to: {} ({})", codec.getName(), codec.getDescription());
    }
    
    /**
     * 获取当前使用的编解码器
     */
    public OpusCodec getCurrentCodec() {
        return currentCodec;
    }
    
    /**
     * 获取编解码器配置
     */
    public OpusCodecConfig getCodecConfig(OpusCodec codec) {
        return codecConfigs.get(codec != null ? codec : OpusCodec.VOIP);
    }
    
    /**
     * 获取当前编解码器配置
     */
    public OpusCodecConfig getCurrentCodecConfig() {
        return getCodecConfig(currentCodec);
    }
    
    /**
     * 验证音频数据包（宽松验证，与原项目行为保持一致）
     */
    public boolean validateAudioPacket(byte[] audioData) {
        if (audioData == null || audioData.length == 0) {
            return false;
        }

        // 只拒绝明显异常的数据包（比如超过MTU大小的数据包）
        if (audioData.length > 1500) { // 标准以太网MTU
            LOGGER.warn("Audio packet size {} exceeds MTU - rejecting", audioData.length);
            return false;
        }

        return true;
    }
    
    /**
     * 处理音频数据包
     */
    public byte[] processAudioPacket(byte[] audioData) {
        if (!validateAudioPacket(audioData)) {
            return null;
        }
        
        // 这里可以添加音频处理逻辑，比如：
        // - 音量调节
        // - 噪声抑制
        // - 回声消除
        // - 音频格式转换
        
        // 目前直接返回原始数据
        return audioData;
    }
    
    /**
     * 获取编解码器统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        
        stats.put("currentCodec", currentCodec.getName());
        stats.put("sampleRate", SAMPLE_RATE);
        stats.put("channels", CHANNELS);
        stats.put("frameSize", FRAME_SIZE);
        stats.put("frameSizeMs", FRAME_SIZE_MS);
        
        // 当前编解码器配置
        OpusCodecConfig currentConfig = getCurrentCodecConfig();
        Map<String, Object> codecStats = new ConcurrentHashMap<>();
        codecStats.put("bitrate", currentConfig.getBitrate());
        codecStats.put("complexity", currentConfig.getComplexity());
        codecStats.put("dtxEnabled", currentConfig.isDtxEnabled());
        codecStats.put("fecEnabled", currentConfig.isFecEnabled());
        codecStats.put("maxPacketSize", currentConfig.getMaxPacketSize());
        
        stats.put("codecConfig", codecStats);
        
        // 所有可用编解码器
        Map<String, String> availableCodecs = new ConcurrentHashMap<>();
        for (OpusCodec codec : OpusCodec.values()) {
            availableCodecs.put(codec.getName(), codec.getDescription());
        }
        stats.put("availableCodecs", availableCodecs);
        
        return stats;
    }
    
    /**
     * Opus编解码器配置类
     */
    public static class OpusCodecConfig {
        private final OpusCodec codec;
        private int bitrate;
        private int complexity;
        private boolean dtxEnabled;
        private boolean fecEnabled;
        private int maxPacketSize;
        
        public OpusCodecConfig(OpusCodec codec) {
            this.codec = codec;
            this.bitrate = codec.getRecommendedBitrate();
            this.complexity = codec.getComplexity();
            this.dtxEnabled = codec.isDtxEnabled();
            this.fecEnabled = codec.isInbandFecEnabled();
            this.maxPacketSize = calculateMaxPacketSize();
        }
        
        private int calculateMaxPacketSize() {
            // 根据比特率和帧大小计算最大数据包大小
            // Opus数据包大小可能会因为音频复杂度而变化，需要更宽松的限制
            // 使用更大的安全系数，并设置合理的最小值
            int theoreticalSize = (int) ((bitrate / 8.0) * (FRAME_SIZE_MS / 1000.0) * 3.0);

            // 设置合理的最小和最大限制
            int minSize = 200;  // 最小200字节
            int maxSize = 1500; // 最大1500字节（接近MTU大小）

            return Math.max(minSize, Math.min(maxSize, theoreticalSize));
        }
        
        // Getters and Setters
        public OpusCodec getCodec() { return codec; }
        public int getBitrate() { return bitrate; }
        public void setBitrate(int bitrate) { 
            this.bitrate = bitrate;
            this.maxPacketSize = calculateMaxPacketSize();
        }
        public int getComplexity() { return complexity; }
        public void setComplexity(int complexity) { this.complexity = complexity; }
        public boolean isDtxEnabled() { return dtxEnabled; }
        public void setDtxEnabled(boolean dtxEnabled) { this.dtxEnabled = dtxEnabled; }
        public boolean isFecEnabled() { return fecEnabled; }
        public void setFecEnabled(boolean fecEnabled) { this.fecEnabled = fecEnabled; }
        public int getMaxPacketSize() { return maxPacketSize; }
        
        public Map<String, Object> toMap() {
            Map<String, Object> map = new ConcurrentHashMap<>();
            map.put("codec", codec.getName());
            map.put("bitrate", bitrate);
            map.put("complexity", complexity);
            map.put("dtxEnabled", dtxEnabled);
            map.put("fecEnabled", fecEnabled);
            map.put("maxPacketSize", maxPacketSize);
            return map;
        }
    }
    
    /**
     * 从配置字符串创建OpusManager
     */
    public static OpusManager fromConfig(String codecName) {
        OpusManager manager = new OpusManager();
        OpusCodec codec = OpusCodec.fromString(codecName);
        manager.setCurrentCodec(codec);
        return manager;
    }
    
    /**
     * 获取推荐的MTU大小
     */
    public int getRecommendedMtuSize() {
        OpusCodecConfig config = getCurrentCodecConfig();
        // MTU应该能容纳最大的音频包加上一些协议开销
        return config.getMaxPacketSize() + 100; // 100字节协议开销
    }
    
    /**
     * 检查编解码器是否支持指定的比特率
     */
    public boolean isBitrateSupported(int bitrate) {
        // Opus支持6-510 kbps的比特率
        return bitrate >= 6000 && bitrate <= 510000;
    }
    
    /**
     * 调整比特率到支持的范围
     */
    public int adjustBitrate(int bitrate) {
        if (bitrate < 6000) return 6000;
        if (bitrate > 510000) return 510000;
        return bitrate;
    }
}
