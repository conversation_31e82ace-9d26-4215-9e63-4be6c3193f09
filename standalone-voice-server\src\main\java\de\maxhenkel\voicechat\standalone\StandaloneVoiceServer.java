package de.maxhenkel.voicechat.standalone;

import de.maxhenkel.voicechat.standalone.config.ServerConfig;
import de.maxhenkel.voicechat.standalone.server.VoiceServer;
import de.maxhenkel.voicechat.standalone.api.ApiServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.concurrent.CountDownLatch;

/**
 * 独立语音服务器主类
 */
public class StandaloneVoiceServer {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(StandaloneVoiceServer.class);
    
    private ServerConfig config;
    private VoiceServer voiceServer;
    private ApiServer apiServer;
    private final CountDownLatch shutdownLatch = new CountDownLatch(1);
    
    public static void main(String[] args) {
        StandaloneVoiceServer server = new StandaloneVoiceServer();
        server.start(args);
    }
    
    public void start(String[] args) {
        LOGGER.info("Starting Standalone Voice Chat Server...");
        
        try {
            // 加载配置
            loadConfig(args);
            
            // 启动语音服务器
            startVoiceServer();
            
            // 启动API服务器
            startApiServer();
            
            // 注册关闭钩子
            registerShutdownHook();
            
            LOGGER.info("Standalone Voice Chat Server started successfully!");
            LOGGER.info("Voice Server: {}:{}", config.getServer().getHost(), config.getServer().getPort());
            LOGGER.info("API Server: {}:{}", config.getApi().getHost(), config.getApi().getPort());
            
            // 等待关闭信号
            shutdownLatch.await();
            
        } catch (Exception e) {
            LOGGER.error("Failed to start server", e);
            System.exit(1);
        }
    }
    
    private void loadConfig(String[] args) throws Exception {
        String configPath = "voice-server.yml";
        if (args.length > 0) {
            configPath = args[0];
        }
        
        File configFile = new File(configPath);
        if (!configFile.exists()) {
            LOGGER.info("Config file not found, creating default config: {}", configPath);
            ServerConfig.createDefaultConfig(configFile);
        }
        
        config = ServerConfig.load(configFile);
        LOGGER.info("Loaded config from: {}", configPath);
    }
    
    private void startVoiceServer() throws Exception {
        voiceServer = new VoiceServer(config);
        voiceServer.start();
        LOGGER.info("Voice server started on {}:{}", config.getServer().getHost(), config.getServer().getPort());
    }
    
    private void startApiServer() throws Exception {
        apiServer = new ApiServer(config, voiceServer);
        apiServer.start();
        LOGGER.info("API server started on {}:{}", config.getApi().getHost(), config.getApi().getPort());
    }
    
    private void registerShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            LOGGER.info("Shutting down Standalone Voice Chat Server...");
            
            try {
                if (apiServer != null) {
                    apiServer.stop();
                }
                if (voiceServer != null) {
                    voiceServer.stop();
                }
                LOGGER.info("Server shutdown complete");
            } catch (Exception e) {
                LOGGER.error("Error during shutdown", e);
            } finally {
                shutdownLatch.countDown();
            }
        }));
    }
}
