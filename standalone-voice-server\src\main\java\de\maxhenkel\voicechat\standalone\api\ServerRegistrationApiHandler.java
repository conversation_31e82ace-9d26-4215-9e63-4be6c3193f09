package de.maxhenkel.voicechat.standalone.api;

import de.maxhenkel.voicechat.standalone.server.ServerRegistrationManager;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 处理服务器注册相关的API请求
 */
public class ServerRegistrationApiHandler {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ServerRegistrationApiHandler.class);
    
    private final ServerRegistrationManager serverRegistrationManager;
    
    public ServerRegistrationApiHandler(ServerRegistrationManager serverRegistrationManager) {
        this.serverRegistrationManager = serverRegistrationManager;
    }
    
    /**
     * 注册服务器
     * POST /api/servers/register
     * 
     * 请求体:
     * {
     *   "serverName": "my-server",
     *   "host": "*************",
     *   "port": 25565
     * }
     */
    public void handleRegisterServer(Context ctx) {
        try {
            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            
            String serverName = (String) requestData.get("serverName");
            String host = (String) requestData.get("host");
            Object portObj = requestData.get("port");
            
            // 验证必需参数
            if (serverName == null || serverName.trim().isEmpty()) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Server name is required"));
                return;
            }
            
            if (host == null || host.trim().isEmpty()) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Host is required"));
                return;
            }
            
            int port;
            try {
                port = portObj instanceof Integer ? (Integer) portObj : Integer.parseInt(portObj.toString());
            } catch (Exception e) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Invalid port number"));
                return;
            }
            
            // 尝试注册服务器
            boolean success = serverRegistrationManager.registerServer(serverName, host, port);
            
            if (success) {
                ctx.status(HttpStatus.CREATED)
                   .json(Map.of(
                       "success", true,
                       "message", "Server registered successfully",
                       "serverName", serverName
                   ));
                LOGGER.info("Server '{}' registered via API from {}:{}", serverName, host, port);
            } else {
                ctx.status(HttpStatus.CONFLICT)
                   .json(Map.of(
                       "error", "Server name already exists",
                       "message", "A server with name '" + serverName + "' is already registered"
                   ));
            }
            
        } catch (Exception e) {
            LOGGER.error("Error registering server", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 发送keepalive
     * POST /api/servers/{serverName}/keepalive
     */
    public void handleKeepalive(Context ctx) {
        try {
            String serverName = ctx.pathParam("serverName");
            
            if (serverName == null || serverName.trim().isEmpty()) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Server name is required"));
                return;
            }
            
            boolean success = serverRegistrationManager.updateKeepalive(serverName);
            
            if (success) {
                ctx.json(Map.of(
                    "success", true,
                    "message", "Keepalive updated successfully",
                    "serverName", serverName,
                    "timestamp", System.currentTimeMillis()
                ));
                LOGGER.debug("Keepalive received for server: {}", serverName);
            } else {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of(
                       "error", "Server not found",
                       "message", "Server '" + serverName + "' is not registered"
                   ));
            }
            
        } catch (Exception e) {
            LOGGER.error("Error processing keepalive", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 取消注册服务器
     * DELETE /api/servers/{serverName}
     */
    public void handleUnregisterServer(Context ctx) {
        try {
            String serverName = ctx.pathParam("serverName");
            
            if (serverName == null || serverName.trim().isEmpty()) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Server name is required"));
                return;
            }
            
            boolean success = serverRegistrationManager.unregisterServer(serverName);
            
            if (success) {
                ctx.json(Map.of(
                    "success", true,
                    "message", "Server unregistered successfully",
                    "serverName", serverName
                ));
                LOGGER.info("Server '{}' unregistered via API", serverName);
            } else {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of(
                       "error", "Server not found",
                       "message", "Server '" + serverName + "' is not registered"
                   ));
            }
            
        } catch (Exception e) {
            LOGGER.error("Error unregistering server", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 检查服务器名称是否可用
     * GET /api/servers/{serverName}/available
     */
    public void handleCheckServerNameAvailable(Context ctx) {
        try {
            String serverName = ctx.pathParam("serverName");
            
            if (serverName == null || serverName.trim().isEmpty()) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Server name is required"));
                return;
            }
            
            boolean isRegistered = serverRegistrationManager.isServerRegistered(serverName);
            
            ctx.json(Map.of(
                "serverName", serverName,
                "available", !isRegistered,
                "registered", isRegistered
            ));
            
        } catch (Exception e) {
            LOGGER.error("Error checking server name availability", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 获取服务器信息
     * GET /api/servers/{serverName}
     */
    public void handleGetServerInfo(Context ctx) {
        try {
            String serverName = ctx.pathParam("serverName");
            
            if (serverName == null || serverName.trim().isEmpty()) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Server name is required"));
                return;
            }
            
            ServerRegistrationManager.RegisteredServer server = 
                    serverRegistrationManager.getRegisteredServer(serverName);
            
            if (server != null) {
                ctx.json(Map.of(
                    "serverName", server.getServerName(),
                    "host", server.getHost(),
                    "port", server.getPort(),
                    "registrationTime", server.getRegistrationTime(),
                    "lastKeepaliveTime", server.getLastKeepaliveTime(),
                    "expired", server.isExpired()
                ));
            } else {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of(
                       "error", "Server not found",
                       "message", "Server '" + serverName + "' is not registered"
                   ));
            }
            
        } catch (Exception e) {
            LOGGER.error("Error getting server info", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 获取所有注册的服务器列表
     * GET /api/servers
     */
    public void handleGetAllServers(Context ctx) {
        try {
            Map<String, ServerRegistrationManager.RegisteredServer> allServers = 
                    serverRegistrationManager.getAllRegisteredServers();
            
            Map<String, Object> serversData = allServers.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> {
                                ServerRegistrationManager.RegisteredServer server = entry.getValue();
                                return Map.of(
                                        "serverName", server.getServerName(),
                                        "host", server.getHost(),
                                        "port", server.getPort(),
                                        "registrationTime", server.getRegistrationTime(),
                                        "lastKeepaliveTime", server.getLastKeepaliveTime(),
                                        "expired", server.isExpired()
                                );
                            }
                    ));
            
            ctx.json(Map.of(
                "servers", serversData,
                "totalCount", allServers.size(),
                "activeCount", serverRegistrationManager.getActiveServerNames().size()
            ));
            
        } catch (Exception e) {
            LOGGER.error("Error getting all servers", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error: " + e.getMessage()));
        }
    }
    
    /**
     * 获取服务器注册统计信息
     * GET /api/servers/statistics
     */
    public void handleGetStatistics(Context ctx) {
        try {
            Map<String, Object> statistics = serverRegistrationManager.getStatistics();
            ctx.json(statistics);
            
        } catch (Exception e) {
            LOGGER.error("Error getting server registration statistics", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error: " + e.getMessage()));
        }
    }
}
