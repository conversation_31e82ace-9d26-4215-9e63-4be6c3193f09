package de.maxhenkel.voicechat.standalone.api;

import de.maxhenkel.voicechat.standalone.audio.OpusCodec;
import de.maxhenkel.voicechat.standalone.audio.OpusManager;
import de.maxhenkel.voicechat.standalone.server.VoiceServer;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * Opus编解码器API处理器
 */
public class OpusApiHandler {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(OpusApiHandler.class);
    
    private final VoiceServer voiceServer;
    private final OpusManager opusManager;
    
    public OpusApiHandler(VoiceServer voiceServer) {
        this.voiceServer = voiceServer;
        this.opusManager = voiceServer.getOpusManager();
    }
    
    /**
     * 获取当前Opus配置
     * GET /api/opus/config
     */
    public void handleGetOpusConfig(Context ctx) {
        try {
            Map<String, Object> config = new HashMap<>();
            
            OpusCodec currentCodec = opusManager.getCurrentCodec();
            OpusManager.OpusCodecConfig codecConfig = opusManager.getCurrentCodecConfig();
            
            config.put("currentCodec", currentCodec.getName());
            config.put("codecDescription", currentCodec.getDescription());
            config.put("bitrate", codecConfig.getBitrate());
            config.put("complexity", codecConfig.getComplexity());
            config.put("dtxEnabled", codecConfig.isDtxEnabled());
            config.put("fecEnabled", codecConfig.isFecEnabled());
            config.put("maxPacketSize", codecConfig.getMaxPacketSize());
            config.put("recommendedMtuSize", opusManager.getRecommendedMtuSize());
            
            ctx.json(config);
            
        } catch (Exception e) {
            LOGGER.error("Error getting Opus config", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 更新Opus配置
     * PUT /api/opus/config
     */
    public void handleUpdateOpusConfig(Context ctx) {
        try {
            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            
            // 更新编解码器类型
            if (requestData.containsKey("codec")) {
                String codecName = (String) requestData.get("codec");
                OpusCodec codec = OpusCodec.fromString(codecName);
                opusManager.setCurrentCodec(codec);
                
                // 同时更新服务器配置
                voiceServer.getConfig().getVoice().setOpusCodec(codec);
            }
            
            // 更新编解码器参数
            OpusManager.OpusCodecConfig codecConfig = opusManager.getCurrentCodecConfig();
            
            if (requestData.containsKey("bitrate")) {
                Number bitrate = (Number) requestData.get("bitrate");
                int adjustedBitrate = opusManager.adjustBitrate(bitrate.intValue());
                codecConfig.setBitrate(adjustedBitrate);
                voiceServer.getConfig().getVoice().setBitrate(adjustedBitrate);
            }
            
            if (requestData.containsKey("complexity")) {
                Number complexity = (Number) requestData.get("complexity");
                int complexityValue = Math.max(0, Math.min(10, complexity.intValue()));
                codecConfig.setComplexity(complexityValue);
                voiceServer.getConfig().getVoice().setComplexity(complexityValue);
            }
            
            if (requestData.containsKey("dtxEnabled")) {
                Boolean dtxEnabled = (Boolean) requestData.get("dtxEnabled");
                codecConfig.setDtxEnabled(dtxEnabled);
                voiceServer.getConfig().getVoice().setDtxEnabled(dtxEnabled);
            }
            
            if (requestData.containsKey("fecEnabled")) {
                Boolean fecEnabled = (Boolean) requestData.get("fecEnabled");
                codecConfig.setFecEnabled(fecEnabled);
                voiceServer.getConfig().getVoice().setFecEnabled(fecEnabled);
            }
            
            ctx.json(Map.of("success", true, "message", "Opus configuration updated successfully"));
            
        } catch (Exception e) {
            LOGGER.error("Error updating Opus config", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 获取所有可用的编解码器
     * GET /api/opus/codecs
     */
    public void handleGetAvailableCodecs(Context ctx) {
        try {
            List<Map<String, Object>> codecs = new ArrayList<>();
            
            for (OpusCodec codec : OpusCodec.values()) {
                Map<String, Object> codecInfo = new HashMap<>();
                codecInfo.put("name", codec.getName());
                codecInfo.put("description", codec.getDescription());
                codecInfo.put("recommendedBitrate", codec.getRecommendedBitrate());
                codecInfo.put("complexity", codec.getComplexity());
                codecInfo.put("frameSize", codec.getFrameSize());
                codecInfo.put("dtxEnabled", codec.isDtxEnabled());
                codecInfo.put("fecEnabled", codec.isInbandFecEnabled());
                codecs.add(codecInfo);
            }
            
            ctx.json(Map.of("codecs", codecs));
            
        } catch (Exception e) {
            LOGGER.error("Error getting available codecs", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 获取Opus统计信息
     * GET /api/opus/statistics
     */
    public void handleGetOpusStatistics(Context ctx) {
        try {
            Map<String, Object> statistics = opusManager.getStatistics();
            ctx.json(statistics);
            
        } catch (Exception e) {
            LOGGER.error("Error getting Opus statistics", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 验证比特率
     * GET /api/opus/validate-bitrate/{bitrate}
     */
    public void handleValidateBitrate(Context ctx) {
        try {
            String bitrateStr = ctx.pathParam("bitrate");
            int bitrate = Integer.parseInt(bitrateStr);
            
            boolean supported = opusManager.isBitrateSupported(bitrate);
            int adjustedBitrate = opusManager.adjustBitrate(bitrate);
            
            Map<String, Object> result = new HashMap<>();
            result.put("originalBitrate", bitrate);
            result.put("supported", supported);
            result.put("adjustedBitrate", adjustedBitrate);
            result.put("needsAdjustment", bitrate != adjustedBitrate);
            
            ctx.json(result);
            
        } catch (NumberFormatException e) {
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid bitrate format"));
        } catch (Exception e) {
            LOGGER.error("Error validating bitrate", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 重置编解码器配置为默认值
     * POST /api/opus/reset
     */
    public void handleResetOpusConfig(Context ctx) {
        try {
            OpusCodec currentCodec = opusManager.getCurrentCodec();
            
            // 重置为当前编解码器的默认配置
            OpusManager.OpusCodecConfig codecConfig = opusManager.getCodecConfig(currentCodec);
            codecConfig.setBitrate(currentCodec.getRecommendedBitrate());
            codecConfig.setComplexity(currentCodec.getComplexity());
            codecConfig.setDtxEnabled(currentCodec.isDtxEnabled());
            codecConfig.setFecEnabled(currentCodec.isInbandFecEnabled());
            
            // 同步到服务器配置
            voiceServer.getConfig().getVoice().setBitrate(currentCodec.getRecommendedBitrate());
            voiceServer.getConfig().getVoice().setComplexity(currentCodec.getComplexity());
            voiceServer.getConfig().getVoice().setDtxEnabled(currentCodec.isDtxEnabled());
            voiceServer.getConfig().getVoice().setFecEnabled(currentCodec.isInbandFecEnabled());
            
            ctx.json(Map.of("success", true, "message", "Opus configuration reset to defaults"));
            
        } catch (Exception e) {
            LOGGER.error("Error resetting Opus config", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 获取编解码器性能建议
     * GET /api/opus/recommendations
     */
    public void handleGetRecommendations(Context ctx) {
        try {
            List<Map<String, Object>> recommendations = new ArrayList<>();
            
            // 为不同使用场景提供建议
            Map<String, Object> voipRec = new HashMap<>();
            voipRec.put("scenario", "Voice Chat");
            voipRec.put("codec", "VOIP");
            voipRec.put("bitrate", 24000);
            voipRec.put("complexity", 5);
            voipRec.put("description", "Optimized for voice communication with good quality and low latency");
            recommendations.add(voipRec);
            
            Map<String, Object> audioRec = new HashMap<>();
            audioRec.put("scenario", "High Quality Audio");
            audioRec.put("codec", "AUDIO");
            audioRec.put("bitrate", 64000);
            audioRec.put("complexity", 8);
            audioRec.put("description", "Best quality for music and audio content");
            recommendations.add(audioRec);
            
            Map<String, Object> lowLatencyRec = new HashMap<>();
            lowLatencyRec.put("scenario", "Low Latency");
            lowLatencyRec.put("codec", "RESTRICTED_LOWDELAY");
            lowLatencyRec.put("bitrate", 16000);
            lowLatencyRec.put("complexity", 3);
            lowLatencyRec.put("description", "Minimal latency for real-time applications");
            recommendations.add(lowLatencyRec);
            
            ctx.json(Map.of("recommendations", recommendations));
            
        } catch (Exception e) {
            LOGGER.error("Error getting recommendations", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
}
