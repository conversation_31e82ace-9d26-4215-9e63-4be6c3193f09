package de.maxhenkel.voicechat.standalone.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 位置信息模型
 */
public class Position {
    
    @JsonProperty("world_id")
    private String worldId;
    
    @JsonProperty("x")
    private double x;
    
    @JsonProperty("y")
    private double y;
    
    @JsonProperty("z")
    private double z;
    
    @JsonProperty("timestamp")
    private long timestamp;
    
    public Position() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public Position(String worldId, double x, double y, double z) {
        this();
        this.worldId = worldId;
        this.x = x;
        this.y = y;
        this.z = z;
    }
    
    // Getters and Setters
    public String getWorldId() { return worldId; }
    public void setWorldId(String worldId) { this.worldId = worldId; }
    
    public double getX() { return x; }
    public void setX(double x) { this.x = x; }
    
    public double getY() { return y; }
    public void setY(double y) { this.y = y; }
    
    public double getZ() { return z; }
    public void setZ(double z) { this.z = z; }
    
    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    
    /**
     * 计算到另一个位置的距离
     */
    public double getDistanceTo(Position other) {
        if (!worldId.equals(other.worldId)) {
            return Double.MAX_VALUE;
        }
        
        double dx = x - other.x;
        double dy = y - other.y;
        double dz = z - other.z;
        
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    /**
     * 检查是否在指定范围内
     */
    public boolean isWithinRange(Position other, double range) {
        return getDistanceTo(other) <= range;
    }
    
    /**
     * 更新位置并刷新时间戳
     */
    public void update(double x, double y, double z) {
        this.x = x;
        this.y = y;
        this.z = z;
        this.timestamp = System.currentTimeMillis();
    }
    
    @Override
    public String toString() {
        return "Position{" +
                "worldId='" + worldId + '\'' +
                ", x=" + x +
                ", y=" + y +
                ", z=" + z +
                ", timestamp=" + timestamp +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        Position position = (Position) o;
        
        if (Double.compare(position.x, x) != 0) return false;
        if (Double.compare(position.y, y) != 0) return false;
        if (Double.compare(position.z, z) != 0) return false;
        return worldId != null ? worldId.equals(position.worldId) : position.worldId == null;
    }
    
    @Override
    public int hashCode() {
        int result;
        long temp;
        result = worldId != null ? worldId.hashCode() : 0;
        temp = Double.doubleToLongBits(x);
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        temp = Double.doubleToLongBits(y);
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        temp = Double.doubleToLongBits(z);
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        return result;
    }
}
