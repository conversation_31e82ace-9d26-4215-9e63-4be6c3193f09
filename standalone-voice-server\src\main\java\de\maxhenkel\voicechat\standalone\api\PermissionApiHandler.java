package de.maxhenkel.voicechat.standalone.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.maxhenkel.voicechat.standalone.model.Permission;
import de.maxhenkel.voicechat.standalone.server.PermissionManager;
import de.maxhenkel.voicechat.standalone.server.VoiceServer;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 权限管理API处理器
 */
public class PermissionApiHandler {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionApiHandler.class);
    
    private final VoiceServer voiceServer;
    private final PermissionManager permissionManager;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    public PermissionApiHandler(VoiceServer voiceServer) {
        this.voiceServer = voiceServer;
        this.permissionManager = voiceServer.getPermissionManager();
    }
    
    /**
     * 获取玩家权限
     * GET /api/permissions/players/{uuid}
     */
    public void handleGetPlayerPermissions(Context ctx) {
        try {
            String uuidStr = ctx.pathParam("uuid");
            UUID playerUuid = UUID.fromString(uuidStr);
            
            Map<Permission, Boolean> permissions = permissionManager.getPlayerPermissions(playerUuid);
            Set<String> groups = permissionManager.getPlayerGroups(playerUuid);
            
            Map<String, Object> response = new HashMap<>();
            response.put("playerUuid", playerUuid.toString());
            response.put("permissions", permissions.entrySet().stream()
                    .collect(Collectors.toMap(
                            entry -> entry.getKey().getPermissionNode(),
                            Map.Entry::getValue
                    )));
            response.put("groups", groups);
            
            ctx.json(response);
            
        } catch (IllegalArgumentException e) {
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid UUID format"));
        } catch (Exception e) {
            LOGGER.error("Error getting player permissions", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 设置玩家权限
     * PUT /api/permissions/players/{uuid}
     */
    public void handleSetPlayerPermissions(Context ctx) {
        try {
            String uuidStr = ctx.pathParam("uuid");
            UUID playerUuid = UUID.fromString(uuidStr);
            
            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            Map<String, Boolean> permissions = (Map<String, Boolean>) requestData.get("permissions");
            
            if (permissions != null) {
                for (Map.Entry<String, Boolean> entry : permissions.entrySet()) {
                    Permission permission = Permission.fromNode(entry.getKey());
                    if (permission != null) {
                        permissionManager.setPlayerPermission(playerUuid, permission, entry.getValue());
                    } else {
                        LOGGER.warn("Unknown permission node: {}", entry.getKey());
                    }
                }
            }
            
            ctx.json(Map.of("success", true, "message", "Permissions updated successfully"));
            
        } catch (IllegalArgumentException e) {
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid UUID format"));
        } catch (Exception e) {
            LOGGER.error("Error setting player permissions", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 检查玩家权限
     * GET /api/permissions/players/{uuid}/check/{permission}
     */
    public void handleCheckPlayerPermission(Context ctx) {
        try {
            String uuidStr = ctx.pathParam("uuid");
            String permissionNode = ctx.pathParam("permission");
            
            UUID playerUuid = UUID.fromString(uuidStr);
            Permission permission = Permission.fromNode(permissionNode);
            
            if (permission == null) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Unknown permission: " + permissionNode));
                return;
            }
            
            boolean hasPermission = permissionManager.hasPermission(playerUuid, permission);
            
            ctx.json(Map.of(
                "playerUuid", playerUuid.toString(),
                "permission", permissionNode,
                "granted", hasPermission
            ));
            
        } catch (IllegalArgumentException e) {
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid UUID format"));
        } catch (Exception e) {
            LOGGER.error("Error checking player permission", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 将玩家添加到权限组
     * POST /api/permissions/players/{uuid}/groups
     */
    public void handleAddPlayerToGroup(Context ctx) {
        try {
            String uuidStr = ctx.pathParam("uuid");
            UUID playerUuid = UUID.fromString(uuidStr);
            
            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            String groupName = (String) requestData.get("groupName");
            
            if (groupName == null || groupName.trim().isEmpty()) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Group name is required"));
                return;
            }
            
            permissionManager.addPlayerToGroup(playerUuid, groupName);
            
            ctx.json(Map.of("success", true, "message", "Player added to group successfully"));
            
        } catch (IllegalArgumentException e) {
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid UUID format"));
        } catch (Exception e) {
            LOGGER.error("Error adding player to group", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 从权限组移除玩家
     * DELETE /api/permissions/players/{uuid}/groups/{groupName}
     */
    public void handleRemovePlayerFromGroup(Context ctx) {
        try {
            String uuidStr = ctx.pathParam("uuid");
            String groupName = ctx.pathParam("groupName");
            
            UUID playerUuid = UUID.fromString(uuidStr);
            
            permissionManager.removePlayerFromGroup(playerUuid, groupName);
            
            ctx.json(Map.of("success", true, "message", "Player removed from group successfully"));
            
        } catch (IllegalArgumentException e) {
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid UUID format"));
        } catch (Exception e) {
            LOGGER.error("Error removing player from group", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 获取所有权限组
     * GET /api/permissions/groups
     */
    public void handleGetAllGroups(Context ctx) {
        try {
            Map<String, Set<Permission>> groups = permissionManager.getAllPermissionGroups();
            
            Map<String, Object> response = new HashMap<>();
            response.put("groups", groups.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().stream()
                                    .map(Permission::getPermissionNode)
                                    .collect(Collectors.toSet())
                    )));
            
            ctx.json(response);
            
        } catch (Exception e) {
            LOGGER.error("Error getting permission groups", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 创建权限组
     * POST /api/permissions/groups
     */
    public void handleCreateGroup(Context ctx) {
        try {
            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            String groupName = (String) requestData.get("groupName");
            List<String> permissionNodes = (List<String>) requestData.get("permissions");
            
            if (groupName == null || groupName.trim().isEmpty()) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Group name is required"));
                return;
            }
            
            Set<Permission> permissions = new HashSet<>();
            if (permissionNodes != null) {
                for (String node : permissionNodes) {
                    Permission permission = Permission.fromNode(node);
                    if (permission != null) {
                        permissions.add(permission);
                    } else {
                        LOGGER.warn("Unknown permission node: {}", node);
                    }
                }
            }
            
            permissionManager.createPermissionGroup(groupName, permissions);
            
            ctx.json(Map.of("success", true, "message", "Permission group created successfully"));
            
        } catch (Exception e) {
            LOGGER.error("Error creating permission group", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 删除权限组
     * DELETE /api/permissions/groups/{groupName}
     */
    public void handleDeleteGroup(Context ctx) {
        try {
            String groupName = ctx.pathParam("groupName");
            
            permissionManager.deletePermissionGroup(groupName);
            
            ctx.json(Map.of("success", true, "message", "Permission group deleted successfully"));
            
        } catch (Exception e) {
            LOGGER.error("Error deleting permission group", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 获取权限统计信息
     * GET /api/permissions/statistics
     */
    public void handleGetStatistics(Context ctx) {
        try {
            Map<String, Object> statistics = permissionManager.getStatistics();
            ctx.json(statistics);
            
        } catch (Exception e) {
            LOGGER.error("Error getting permission statistics", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 获取所有可用权限
     * GET /api/permissions/available
     */
    public void handleGetAvailablePermissions(Context ctx) {
        try {
            List<Map<String, Object>> permissions = Arrays.stream(Permission.values())
                    .map(permission -> {
                        Map<String, Object> permInfo = new HashMap<>();
                        permInfo.put("node", permission.getPermissionNode());
                        permInfo.put("name", permission.name());
                        permInfo.put("isGroupPermission", permission.isGroupPermission());
                        permInfo.put("isAdminPermission", permission.isAdminPermission());
                        permInfo.put("isCrossBoundaryPermission", permission.isCrossBoundaryPermission());
                        return permInfo;
                    })
                    .collect(Collectors.toList());
            
            ctx.json(Map.of("permissions", permissions));
            
        } catch (Exception e) {
            LOGGER.error("Error getting available permissions", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
}
