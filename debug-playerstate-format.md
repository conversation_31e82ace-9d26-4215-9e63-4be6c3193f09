# PlayerState 数据包格式调试

## 错误分析

客户端错误：
```
io.netty.handler.codec.DecoderException: Not enough bytes in buffer, expected 109, but got 11
```

这表明客户端期望109字节，但只收到了11字节。

## 数据包格式对比

### 原版 PlayerState 格式 (common/PlayerState.java)
```java
public void toBytes(FriendlyByteBuf buf) {
    buf.writeBoolean(disabled);      // 1 byte
    buf.writeBoolean(disconnected);  // 1 byte  
    buf.writeUUID(uuid);             // 16 bytes (2 longs)
    buf.writeUtf(name);              // VarInt + string bytes
    buf.writeBoolean(hasGroup());    // 1 byte
    if (hasGroup()) {
        buf.writeUUID(group);        // 16 bytes (2 longs)
    }
}
```

### 我们的 PlayerState 格式 (adapter/PlayerState.java)
```java
public void toBytes(FriendlyByteBuf buf) throws IOException {
    buf.writeBoolean(disabled);      // 1 byte
    buf.writeBoolean(disconnected);  // 1 byte
    buf.writeUUID(uuid);             // 16 bytes (2 longs)
    buf.writeUtf(name);              // VarInt + string bytes
    buf.writeBoolean(hasGroup());    // 1 byte
    if (hasGroup()) {
        buf.writeUUID(group);        // 16 bytes (2 longs)
    }
}
```

## 可能的问题

1. **VarInt 编码问题**：我们的 FriendlyByteBuf 实现可能与原版不兼容
2. **字符串编码问题**：writeUtf 方法可能有差异
3. **UUID 编码问题**：UUID 的字节序可能不同
4. **数据包头部问题**：可能缺少某些头部信息

## 调试步骤

### 1. 检查单个 PlayerState 大小
一个典型的 PlayerState 应该包含：
- disabled: 1 byte
- disconnected: 1 byte  
- uuid: 16 bytes
- name (假设 "TestPlayer"): 1 byte (VarInt) + 10 bytes = 11 bytes
- hasGroup: 1 byte
- group (如果有): 16 bytes

**最小大小**: 1+1+16+11+1 = 30 bytes
**带群组**: 30+16 = 46 bytes

### 2. 检查 PlayerStatesPacket 大小
对于包含3个玩家的数据包：
- count: 4 bytes (int)
- 3个 PlayerState: 3 × 46 = 138 bytes

**总大小**: 4 + 138 = 142 bytes

但客户端期望109字节，这表明格式不匹配。

## 解决方案

1. **使用原版的 FriendlyByteBuf**：直接使用 Bukkit 的 FriendlyByteBuf 实现
2. **检查字节序**：确保 UUID 和其他多字节数据使用正确的字节序
3. **添加调试日志**：记录发送的数据包大小和内容
4. **对比原版实现**：确保我们的序列化格式完全匹配原版

## 临时解决方案

暂时禁用 PlayerState 网络通信，直到格式问题解决：

```java
// 在 PlayerStateNetworkManager 中添加开关
private static final boolean ENABLE_NETWORK = false;

public void sendPlayerState(Player player, PlayerState state) {
    if (!ENABLE_NETWORK) {
        LOGGER.debug("PlayerState network communication disabled");
        return;
    }
    // ... 原有代码
}
```
