package de.maxhenkel.voicechat.adapter.config;

import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;

/**
 * 适配器配置类
 */
public class AdapterConfig {
    
    private final VoiceServerConfig voiceServer;
    private final SyncConfig sync;
    private final String serverName;
    
    public AdapterConfig(VoiceServerConfig voiceServer, SyncConfig sync, String serverName) {
        this.voiceServer = voiceServer;
        this.sync = sync;
        this.serverName = serverName;
    }
    
    /**
     * 语音服务器配置
     */
    public static class VoiceServerConfig {
        private final String host;
        private final int port;
        private final String apiEndpoint;
        private final String authToken;
        
        public VoiceServerConfig(String host, int port, String apiEndpoint, String authToken) {
            this.host = host;
            this.port = port;
            this.apiEndpoint = apiEndpoint;
            this.authToken = authToken;
        }
        
        public String getHost() { return host; }
        public int getPort() { return port; }
        public String getApiEndpoint() { return apiEndpoint; }
        public String getAuthToken() { return authToken; }
    }
    
    /**
     * 同步配置
     */
    public static class SyncConfig {
        private final int positionSyncInterval;
        private final int permissionSyncInterval;
        private final boolean syncOnMove;
        private final double minMoveDistance;
        
        public SyncConfig(int positionSyncInterval, int permissionSyncInterval, 
                         boolean syncOnMove, double minMoveDistance) {
            this.positionSyncInterval = positionSyncInterval;
            this.permissionSyncInterval = permissionSyncInterval;
            this.syncOnMove = syncOnMove;
            this.minMoveDistance = minMoveDistance;
        }
        
        public int getPositionSyncInterval() { return positionSyncInterval; }
        public int getPermissionSyncInterval() { return permissionSyncInterval; }
        public boolean isSyncOnMove() { return syncOnMove; }
        public double getMinMoveDistance() { return minMoveDistance; }
    }
    
    /**
     * 从文件加载配置
     */
    public static AdapterConfig load(File file) throws Exception {
        YamlConfiguration yaml = YamlConfiguration.loadConfiguration(file);
        
        // 语音服务器配置
        String host = yaml.getString("voice-server.host", "localhost");
        int port = yaml.getInt("voice-server.port", 24454);
        String apiEndpoint = yaml.getString("voice-server.api-endpoint", "http://localhost:8080");
        String authToken = yaml.getString("voice-server.auth-token", "change-this-secret-token");
        
        VoiceServerConfig voiceServerConfig = new VoiceServerConfig(host, port, apiEndpoint, authToken);
        
        // 同步配置
        int positionSyncInterval = yaml.getInt("sync.position-interval", 1000);
        int permissionSyncInterval = yaml.getInt("sync.permission-interval", 5000);
        boolean syncOnMove = yaml.getBoolean("sync.sync-on-move", true);
        double minMoveDistance = yaml.getDouble("sync.min-move-distance", 1.0);
        
        SyncConfig syncConfig = new SyncConfig(positionSyncInterval, permissionSyncInterval, 
                                              syncOnMove, minMoveDistance);
        
        // 服务器名称
        String serverName = yaml.getString("server-name", "default");
        
        return new AdapterConfig(voiceServerConfig, syncConfig, serverName);
    }
    
    // Getters
    public VoiceServerConfig getVoiceServer() { return voiceServer; }
    public SyncConfig getSync() { return sync; }
    public String getServerName() { return serverName; }
}
