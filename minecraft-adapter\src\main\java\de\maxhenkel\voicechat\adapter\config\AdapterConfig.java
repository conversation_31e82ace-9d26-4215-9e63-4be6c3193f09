package de.maxhenkel.voicechat.adapter.config;

import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;

/**
 * 适配器配置类
 */
public class AdapterConfig {
    
    private final VoiceServerConfig voiceServer;
    private final SyncConfig sync;
    private final String serverName;
    private final CrossServerConfig crossServer;
    private final CrossWorldConfig crossWorld;
    private final PermissionConfig permissions;
    
    public AdapterConfig(VoiceServerConfig voiceServer, SyncConfig sync, String serverName,
                        CrossServerConfig crossServer, CrossWorldConfig crossWorld, PermissionConfig permissions) {
        this.voiceServer = voiceServer;
        this.sync = sync;
        this.serverName = serverName;
        this.crossServer = crossServer;
        this.crossWorld = crossWorld;
        this.permissions = permissions;
    }
    
    /**
     * 语音服务器配置
     */
    public static class VoiceServerConfig {
        private final String host;
        private final int port;
        private final String apiEndpoint;
        private final String authToken;
        
        public VoiceServerConfig(String host, int port, String apiEndpoint, String authToken) {
            this.host = host;
            this.port = port;
            this.apiEndpoint = apiEndpoint;
            this.authToken = authToken;
        }
        
        public String getHost() { return host; }
        public int getPort() { return port; }
        public String getApiEndpoint() { return apiEndpoint; }
        public String getAuthToken() { return authToken; }
    }
    
    /**
     * 同步配置
     */
    public static class SyncConfig {
        private final int positionSyncInterval;
        private final int permissionSyncInterval;
        private final boolean syncOnMove;
        private final double minMoveDistance;
        
        public SyncConfig(int positionSyncInterval, int permissionSyncInterval, 
                         boolean syncOnMove, double minMoveDistance) {
            this.positionSyncInterval = positionSyncInterval;
            this.permissionSyncInterval = permissionSyncInterval;
            this.syncOnMove = syncOnMove;
            this.minMoveDistance = minMoveDistance;
        }
        
        public int getPositionSyncInterval() { return positionSyncInterval; }
        public int getPermissionSyncInterval() { return permissionSyncInterval; }
        public boolean isSyncOnMove() { return syncOnMove; }
        public double getMinMoveDistance() { return minMoveDistance; }
    }
    
    /**
     * 从文件加载配置
     */
    public static AdapterConfig load(File file) throws Exception {
        YamlConfiguration yaml = YamlConfiguration.loadConfiguration(file);
        
        // 语音服务器配置
        String host = yaml.getString("voice-server.host", "localhost");
        int port = yaml.getInt("voice-server.port", 24454);
        String apiEndpoint = yaml.getString("voice-server.api-endpoint", "http://localhost:8080");
        String authToken = yaml.getString("voice-server.auth-token", "change-this-secret-token");
        
        VoiceServerConfig voiceServerConfig = new VoiceServerConfig(host, port, apiEndpoint, authToken);
        
        // 同步配置
        int positionSyncInterval = yaml.getInt("sync.position-interval", 1000);
        int permissionSyncInterval = yaml.getInt("sync.permission-interval", 5000);
        boolean syncOnMove = yaml.getBoolean("sync.sync-on-move", true);
        double minMoveDistance = yaml.getDouble("sync.min-move-distance", 1.0);
        
        SyncConfig syncConfig = new SyncConfig(positionSyncInterval, permissionSyncInterval, 
                                              syncOnMove, minMoveDistance);
        
        // 服务器名称
        String serverName = yaml.getString("server-name", "default");

        // 跨服务器配置
        boolean crossServerEnabled = yaml.getBoolean("cross-server.enabled", true);
        java.util.List<String> allowedServers = yaml.getStringList("cross-server.allowed-servers");
        CrossServerConfig crossServerConfig = new CrossServerConfig(crossServerEnabled, allowedServers);

        // 跨世界配置
        boolean crossWorldEnabled = yaml.getBoolean("cross-world.enabled", true);
        java.util.List<String> allowedWorlds = yaml.getStringList("cross-world.allowed-worlds");
        CrossWorldConfig crossWorldConfig = new CrossWorldConfig(crossWorldEnabled, allowedWorlds);

        // 权限配置
        PermissionConfig permissionConfig = loadPermissionConfig(yaml);

        return new AdapterConfig(voiceServerConfig, syncConfig, serverName,
                               crossServerConfig, crossWorldConfig, permissionConfig);
    }
    
    /**
     * 加载权限配置
     */
    private static PermissionConfig loadPermissionConfig(YamlConfiguration yaml) {
        boolean listen = yaml.getBoolean("permissions.defaults.listen", true);
        boolean speak = yaml.getBoolean("permissions.defaults.speak", true);
        boolean groups = yaml.getBoolean("permissions.defaults.groups", true);
        boolean createGroup = yaml.getBoolean("permissions.defaults.create-group", true);
        boolean joinGroup = yaml.getBoolean("permissions.defaults.join-group", true);
        boolean crossWorld = yaml.getBoolean("permissions.defaults.cross-world", true);
        boolean crossServer = yaml.getBoolean("permissions.defaults.cross-server", true);
        boolean record = yaml.getBoolean("permissions.defaults.record", false);
        boolean admin = yaml.getBoolean("permissions.defaults.admin", false);

        return new PermissionConfig(listen, speak, groups, createGroup, joinGroup,
                                  crossWorld, crossServer, record, admin);
    }

    // Getters
    public VoiceServerConfig getVoiceServer() { return voiceServer; }
    public SyncConfig getSync() { return sync; }
    public String getServerName() { return serverName; }
    public CrossServerConfig getCrossServer() { return crossServer; }
    public CrossWorldConfig getCrossWorld() { return crossWorld; }
    public PermissionConfig getPermissions() { return permissions; }

    /**
     * 跨服务器配置
     */
    public static class CrossServerConfig {
        private final boolean enabled;
        private final java.util.List<String> allowedServers;

        public CrossServerConfig(boolean enabled, java.util.List<String> allowedServers) {
            this.enabled = enabled;
            this.allowedServers = allowedServers != null ? allowedServers : new java.util.ArrayList<>();
        }

        public boolean isEnabled() { return enabled; }
        public java.util.List<String> getAllowedServers() { return allowedServers; }
        public boolean isServerAllowed(String serverName) {
            return allowedServers.isEmpty() || allowedServers.contains(serverName);
        }
    }

    /**
     * 跨世界配置
     */
    public static class CrossWorldConfig {
        private final boolean enabled;
        private final java.util.List<String> allowedWorlds;

        public CrossWorldConfig(boolean enabled, java.util.List<String> allowedWorlds) {
            this.enabled = enabled;
            this.allowedWorlds = allowedWorlds != null ? allowedWorlds : new java.util.ArrayList<>();
        }

        public boolean isEnabled() { return enabled; }
        public java.util.List<String> getAllowedWorlds() { return allowedWorlds; }
        public boolean isWorldAllowed(String worldName) {
            return allowedWorlds.isEmpty() || allowedWorlds.contains(worldName);
        }
    }

    /**
     * 权限配置
     */
    public static class PermissionConfig {
        private final boolean listen;
        private final boolean speak;
        private final boolean groups;
        private final boolean createGroup;
        private final boolean joinGroup;
        private final boolean crossWorld;
        private final boolean crossServer;
        private final boolean record;
        private final boolean admin;

        public PermissionConfig(boolean listen, boolean speak, boolean groups, boolean createGroup,
                              boolean joinGroup, boolean crossWorld, boolean crossServer,
                              boolean record, boolean admin) {
            this.listen = listen;
            this.speak = speak;
            this.groups = groups;
            this.createGroup = createGroup;
            this.joinGroup = joinGroup;
            this.crossWorld = crossWorld;
            this.crossServer = crossServer;
            this.record = record;
            this.admin = admin;
        }

        public boolean isListen() { return listen; }
        public boolean isSpeak() { return speak; }
        public boolean isGroups() { return groups; }
        public boolean isCreateGroup() { return createGroup; }
        public boolean isJoinGroup() { return joinGroup; }
        public boolean isCrossWorld() { return crossWorld; }
        public boolean isCrossServer() { return crossServer; }
        public boolean isRecord() { return record; }
        public boolean isAdmin() { return admin; }
    }
}
