# 独立语音服务器部署指南

本指南将帮助您部署和配置独立的语音聊天服务器。

## 系统要求

### 硬件要求
- **CPU**: 2核心或更多
- **内存**: 最少512MB，推荐1GB+
- **存储**: 至少1GB可用空间
- **网络**: 稳定的网络连接，支持UDP和TCP

### 软件要求
- **Java**: 17或更高版本
- **操作系统**: Linux, Windows, macOS
- **防火墙**: 开放UDP 24454和TCP 8080端口

## 快速开始

### 1. 下载和构建

```bash
# 克隆项目
git clone <repository-url>
cd simple-voice-chat

# 使用部署脚本
chmod +x deploy.sh
./deploy.sh config build deploy
```

### 2. 配置服务器

编辑 `deployment/voice-server/voice-server.yml`:

```yaml
server:
  host: "0.0.0.0"
  port: 24454

api:
  host: "0.0.0.0"
  port: 8080
  auth_token: "your-generated-token"

voice:
  max_distance: 48.0
  groups_enabled: true
```

### 3. 启动服务器

```bash
cd deployment/voice-server
./start.sh
```

## 详细部署步骤

### 步骤1: 环境准备

#### 安装Java 17+

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install openjdk-17-jre-headless
```

**CentOS/RHEL:**
```bash
sudo yum install java-17-openjdk-headless
```

**Windows:**
下载并安装 [OpenJDK 17](https://adoptium.net/)

#### 配置防火墙

**Linux (ufw):**
```bash
sudo ufw allow 24454/udp
sudo ufw allow 8080/tcp
```

**Linux (firewalld):**
```bash
sudo firewall-cmd --permanent --add-port=24454/udp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

**Windows:**
在Windows防火墙中开放端口24454(UDP)和8080(TCP)

### 步骤2: 下载和构建

```bash
# 下载源码
git clone <repository-url>
cd simple-voice-chat

# 构建项目
./deploy.sh build
```

### 步骤3: 配置

#### 生成配置文件
```bash
./deploy.sh config
```

这将生成随机的认证令牌并更新配置文件。

#### 手动配置 (可选)

编辑 `standalone-voice-server/voice-server.yml`:

```yaml
# 服务器配置
server:
  host: "0.0.0.0"          # 绑定所有网络接口
  port: 24454              # UDP端口
  bind_address: ""         # 特定绑定地址

# API配置
api:
  host: "0.0.0.0"          # API服务器地址
  port: 8080               # API端口
  auth_token: "your-secret-token"  # 认证令牌

# 注意：独立服务器不需要配置Minecraft服务器信息
# 每个Minecraft服务器通过适配器插件连接到独立服务器

# 语音配置
voice:
  codec: "VOIP"            # 音频编解码器
  mtu_size: 1024           # MTU大小
  keep_alive: 1000         # 心跳间隔
  max_distance: 48.0       # 最大语音距离
  groups_enabled: true     # 启用群组聊天
  allow_recording: true    # 允许录音

# 安全配置
security:
  encryption_enabled: true # 启用加密
  auth_timeout: 30000      # 认证超时
```

### 步骤4: 部署

#### 本地部署
```bash
./deploy.sh deploy
```

#### Docker部署
```bash
./deploy.sh docker
```

### 步骤5: 启动服务器

#### 本地启动
```bash
cd deployment/voice-server

# Linux/macOS
./start.sh

# Windows
start.bat
```

#### Docker启动
```bash
docker-compose up -d
```

### 步骤6: 验证部署

#### 检查服务状态
```bash
# 健康检查
curl http://localhost:8080/health

# 服务器状态
curl -H "Authorization: Bearer your-auth-token" \
     http://localhost:8080/api/status
```

#### 查看日志
```bash
# 本地部署
tail -f deployment/voice-server/logs/voice-server.log

# Docker部署
docker-compose logs -f voice-server
```

## Minecraft服务器集成

### 1. 安装适配器插件

将 `minecraft-adapter-1.0.0.jar` 复制到Minecraft服务器的 `plugins` 目录。

### 2. 配置适配器

编辑 `plugins/VoiceChatAdapter/config.yml`:

```yaml
voice-server:
  host: "voice.example.com"
  port: 24454
  api-endpoint: "http://voice.example.com:8080"
  auth-token: "your-secret-token"

sync:
  position-interval: 1000
  permission-interval: 5000
  sync-on-move: true
  min-move-distance: 1.0

server-name: "survival"
```

### 3. 重启Minecraft服务器

重启Minecraft服务器以加载适配器插件。

## 监控和维护

### 日志管理

日志文件位置：
- 主日志: `logs/voice-server.log`
- 错误日志: `logs/voice-server-error.log`

日志轮转配置在 `logback.xml` 中：
- 每日轮转
- 最大文件大小: 10MB
- 保留30天

### 性能监控

#### 使用Prometheus和Grafana

```bash
# 启动监控服务
docker-compose up -d prometheus grafana

# 访问Grafana
# URL: http://localhost:3000
# 用户名: admin
# 密码: admin
```

#### 手动监控

```bash
# 检查服务器状态
curl -H "Authorization: Bearer your-auth-token" \
     http://localhost:8080/api/status

# 检查系统资源
top
netstat -tulpn | grep :24454
```

### 备份和恢复

#### 备份配置
```bash
# 备份配置文件
tar -czf voice-server-config-$(date +%Y%m%d).tar.gz \
    deployment/voice-server/voice-server.yml \
    deployment/voice-server/logback.xml
```

#### 备份数据
```bash
# 备份日志和数据
tar -czf voice-server-data-$(date +%Y%m%d).tar.gz \
    deployment/voice-server/logs/ \
    deployment/voice-server/data/
```

## 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
netstat -tulpn | grep :24454
netstat -tulpn | grep :8080

# 杀死占用进程
sudo kill -9 <PID>
```

#### 2. 连接超时
- 检查防火墙设置
- 验证网络连通性
- 检查服务器日志

#### 3. 认证失败
- 验证auth_token配置
- 检查Minecraft适配器配置
- 查看API请求日志

#### 4. 内存不足
```bash
# 增加JVM内存
export JAVA_OPTS="-Xmx2G -Xms1G"
./start.sh
```

### 调试模式

启用调试日志：

编辑 `logback.xml`:
```xml
<logger name="de.maxhenkel.voicechat.standalone" level="DEBUG" />
```

### 性能优化

#### JVM调优
```bash
export JAVA_OPTS="-Xmx2G -Xms1G \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=100 \
  -XX:+UnlockExperimentalVMOptions"
```

#### 网络优化
- 调整MTU大小
- 优化心跳间隔
- 使用高性能网络接口

## 安全建议

1. **更改默认认证令牌**
2. **使用HTTPS** (通过反向代理)
3. **限制API访问** (IP白名单)
4. **定期更新** 软件版本
5. **监控异常** 连接和请求

## 扩展和定制

### 添加自定义功能

1. 修改源码
2. 重新构建
3. 部署更新

### 集成其他系统

- 使用HTTP API集成
- 实现Webhook通知
- 添加数据库支持

## 支持和社区

- GitHub Issues: <repository-url>/issues
- 文档: <documentation-url>
- 社区论坛: <forum-url>
