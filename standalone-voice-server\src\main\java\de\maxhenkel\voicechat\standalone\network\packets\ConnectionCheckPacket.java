package de.maxhenkel.voicechat.standalone.network.packets;

import de.maxhenkel.voicechat.standalone.network.Packet;
import io.netty.buffer.ByteBuf;

/**
 * 连接检查数据包
 */
public class ConnectionCheckPacket extends Packet<ConnectionCheckPacket> {

    public static final byte PACKET_ID = (byte) 0x9;

    public ConnectionCheckPacket() {
    }

    @Override
    public void fromBytes(ByteBuf buf) {
        // 连接检查包通常没有额外数据
    }

    @Override
    public void toBytes(ByteBuf buf) {
        // 连接检查包通常没有额外数据
    }

    @Override
    public byte getPacketId() {
        return PACKET_ID;
    }

    @Override
    public Class<ConnectionCheckPacket> getPacketClass() {
        return ConnectionCheckPacket.class;
    }
}
