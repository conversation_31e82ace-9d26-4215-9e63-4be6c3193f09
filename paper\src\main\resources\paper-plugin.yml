name: voicechat
main: de.maxhenkel.voicechat.VoicechatPaperPlugin
bootstrapper: de.maxhenkel.voicechat.VoicechatBootstrap
version: ${mod_version}
description: A working voice chat in Minecraft!
authors:
  - <PERSON>
website: https://modrepo.de/minecraft/voicechat/overview
api-version: ${minecraft_version}
prefix: voicechat
load: STARTUP
permissions:
  voicechat.listen:
    description: Hear voice chat
    default: true
  voicechat.speak:
    description: Transmit voice chat
    default: true
  voicechat.groups:
    description: Use voice chat groups
    default: true
  voicechat.admin:
    description: Use admin commands
    default: op
dependencies:
  - name: ViaVersion
    required: false
  - name: PlaceholderAPI
    required: false
load-before:
  - name: ViaVersion
    boostrap: false
  - name: PlaceholderAPI
    boostrap: false