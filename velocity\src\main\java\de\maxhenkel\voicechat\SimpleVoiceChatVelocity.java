package de.maxhenkel.voicechat;

import com.google.inject.Inject;
import com.velocitypowered.api.event.Subscribe;
import com.velocitypowered.api.event.connection.DisconnectEvent;
import com.velocitypowered.api.event.connection.PluginMessageEvent;
import com.velocitypowered.api.event.player.ServerConnectedEvent;
import com.velocitypowered.api.event.proxy.ProxyInitializeEvent;
import com.velocitypowered.api.event.proxy.ProxyReloadEvent;
import com.velocitypowered.api.event.proxy.ProxyShutdownEvent;
import com.velocitypowered.api.plugin.Plugin;
import com.velocitypowered.api.plugin.annotation.DataDirectory;
import com.velocitypowered.api.proxy.Player;
import com.velocitypowered.api.proxy.ProxyServer;
import com.velocitypowered.api.proxy.ServerConnection;
import com.velocitypowered.api.proxy.messages.MinecraftChannelIdentifier;
import de.maxhenkel.voicechat.logging.JavaLoggingLogger;
import de.maxhenkel.voicechat.sniffer.IncompatibleVoiceChatException;
import de.maxhenkel.voicechat.velocity.StandaloneVoiceProxyConfig;

import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.nio.file.Path;
import java.util.Optional;
import java.util.UUID;
import java.util.logging.Logger;

@Plugin(
        id = SimpleVoiceChatVelocity.MOD_ID,
        name = "Simple Voice Chat",
        version = VoiceProxy.MOD_VERSION,
        authors = {"NilaTheDragon", "Max Henkel"},
        url = "https://github.com/henkelmax/simple-voice-chat",
        description = "Run multiple servers with Simple Voice Chat behind a single public port"
)
public class SimpleVoiceChatVelocity extends VoiceProxy {

    @DataDirectory
    @Inject
    private Path dataDirectory;
    @Inject
    private ProxyServer proxyServer;

    private StandaloneVoiceProxyConfig standaloneConfig;

    @Inject
    public SimpleVoiceChatVelocity(Logger logger) {
        super(new JavaLoggingLogger(logger));
    }

    @Override
    public InetSocketAddress getDefaultBackendSocket(UUID playerUUID) {
        // 如果启用了独立语音服务器模式，返回独立语音服务器地址
        if (standaloneConfig != null && standaloneConfig.isStandaloneMode()) {
            return new InetSocketAddress(standaloneConfig.getVoiceServerHost(), standaloneConfig.getVoiceServerPort());
        }

        // 原有的代理模式逻辑
        Optional<Player> player = this.proxyServer.getPlayer(playerUUID);
        if (player.isEmpty()) {
            return null;
        }

        Optional<ServerConnection> server = player.get().getCurrentServer();
        if (server.isEmpty()) {
            return null;
        }

        InetSocketAddress serverSocket = server.get().getServerInfo().getAddress();
        if (serverSocket.isUnresolved()) {
            serverSocket = new InetSocketAddress(serverSocket.getHostString(), serverSocket.getPort());
        }
        return serverSocket;
    }

    @Override
    public InetSocketAddress getDefaultBindSocket() {
        return this.proxyServer.getBoundAddress();
    }

    @Override
    public Path getDataDirectory() {
        return this.dataDirectory;
    }

    @Subscribe
    public void onProxyInitialization(ProxyInitializeEvent event) {
        // 加载独立语音服务器配置
        loadStandaloneConfig();

        this.proxyServer.getChannelRegistrar().register(
                MinecraftChannelIdentifier.from(REQUEST_SECRET_CHANNEL),
                MinecraftChannelIdentifier.from(REQUEST_SECRET_CHANNEL_1_12),
                MinecraftChannelIdentifier.from(SECRET_CHANNEL),
                MinecraftChannelIdentifier.from(SECRET_CHANNEL_1_12)
        );

        // 根据配置决定是否启动代理服务器
        if (standaloneConfig == null || !standaloneConfig.isStandaloneMode()) {
            this.reloadVoiceProxyServer();
            getLogger().info("Voice chat proxy mode enabled");
        } else {
            getLogger().info("Standalone voice server mode enabled - connecting to {}:{}",
                standaloneConfig.getVoiceServerHost(), standaloneConfig.getVoiceServerPort());
        }
    }

    @Subscribe
    public void onProxyReload(ProxyReloadEvent event) {
        // 重新加载配置
        loadStandaloneConfig();

        // 根据配置决定是否重启代理服务器
        if (standaloneConfig == null || !standaloneConfig.isStandaloneMode()) {
            this.reloadVoiceProxyServer();
            getLogger().info("Voice chat proxy mode reloaded");
        } else {
            // 如果之前是代理模式，现在切换到独立模式，需要停止代理服务器
            if (voiceProxyServer != null) {
                voiceProxyServer.interrupt();
                voiceProxyServer = null;
            }
            getLogger().info("Standalone voice server mode reloaded - connecting to {}:{}",
                standaloneConfig.getVoiceServerHost(), standaloneConfig.getVoiceServerPort());
        }
    }

    @Subscribe
    public void onProxyShutdown(ProxyShutdownEvent event) {
        if (this.voiceProxyServer != null) this.voiceProxyServer.interrupt();
    }

    /**
     * This handler detects whether a player has switched servers and if so,
     * disconnects the current VoiceProxyBridge and resets the sniffer.
     */
    @Subscribe
    public void onServerConnected(ServerConnectedEvent event) {
        if (event.getPreviousServer().isEmpty()) {
            return;
        }
        this.onPlayerServerDisconnected(event.getPlayer().getUniqueId());
    }

    /**
     * This handler detects when a player has disconnected from the velocity proxy and if so,
     * disconnects the current VoiceProxyBridge and resets the sniffer.
     */
    @Subscribe
    public void onPlayerDisconnected(DisconnectEvent event) {
        this.onPlayerServerDisconnected(event.getPlayer().getUniqueId());
    }

    /**
     * This handler is used to intercept plugin messages between the client and backend server
     * so the proxy is able to sniff the SecretPacket.
     */
    @Subscribe
    public void onPluginMessage(PluginMessageEvent event) {
        Player p = null;
        if (event.getSource() instanceof Player) {
            p = (Player) event.getSource();
        }
        if (event.getTarget() instanceof Player) {
            p = (Player) event.getTarget();
        }
        if (p == null) {
            return;
        }

        // 如果是独立语音服务器模式，需要修改SecretPacket中的服务器信息
        if (standaloneConfig != null && standaloneConfig.isStandaloneMode()) {
            try {
                ByteBuffer replacement = handleStandalonePluginMessage(event.getIdentifier().getId(), ByteBuffer.wrap(event.getData()), p.getUniqueId());
                if (replacement != null) {
                    event.setResult(PluginMessageEvent.ForwardResult.handled());
                    event.getTarget().sendPluginMessage(event.getIdentifier(), replacement.array());
                }
            } catch (IncompatibleVoiceChatException e) {
                event.setResult(PluginMessageEvent.ForwardResult.handled());
                getLogger().info("Player {} has an incompatible voice chat version: {}", p.getUsername(), e.getMessage());
            }
            return;
        }

        // 原有的代理模式逻辑
        try {
            ByteBuffer replacement = voiceProxySniffer.onPluginMessage(event.getIdentifier().getId(), ByteBuffer.wrap(event.getData()), p.getUniqueId());
            if (replacement == null) {
                return;
            }
            event.setResult(PluginMessageEvent.ForwardResult.handled());
            event.getTarget().sendPluginMessage(event.getIdentifier(), replacement.array());
        } catch (IncompatibleVoiceChatException e) {
            event.setResult(PluginMessageEvent.ForwardResult.handled());
            getLogger().info("Player {} has an incompatible voice chat version: {}", p.getUsername(), e.getMessage());
        }
    }

    /**
     * 加载独立语音服务器配置
     */
    private void loadStandaloneConfig() {
        try {
            standaloneConfig = StandaloneVoiceProxyConfig.load(getDataDirectory());
            if (standaloneConfig.isStandaloneMode()) {
                getLogger().info("Loaded standalone voice server config: {}:{}",
                    standaloneConfig.getVoiceServerHost(), standaloneConfig.getVoiceServerPort());
            }
        } catch (Exception e) {
            getLogger().warn("Failed to load standalone config, using proxy mode: {}", e.getMessage());
            standaloneConfig = null;
        }
    }

    /**
     * 处理独立语音服务器模式下的插件消息
     */
    private ByteBuffer handleStandalonePluginMessage(String channel, ByteBuffer message, UUID playerUUID) throws IncompatibleVoiceChatException {
        if (channel.equals(SECRET_CHANNEL) || channel.equals(SECRET_CHANNEL_1_12)) {
            // 修改SecretPacket，将语音服务器地址改为独立语音服务器地址
            return StandaloneVoiceProxyConfig.patchSecretPacket(message, standaloneConfig);
        }
        return null;
    }
}
