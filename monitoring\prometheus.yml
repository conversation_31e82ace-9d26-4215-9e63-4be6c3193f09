# Prometheus 配置文件

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 语音服务器监控
  - job_name: 'voice-server'
    static_configs:
      - targets: ['voice-server:8080']
    metrics_path: '/api/metrics'
    scrape_interval: 10s

  # Minecraft服务器监控 (如果有JMX导出器)
  - job_name: 'minecraft-server'
    static_configs:
      - targets: ['minecraft-server:9999']
    scrape_interval: 30s
