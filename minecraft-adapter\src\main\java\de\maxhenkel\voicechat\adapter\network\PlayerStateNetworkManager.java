package de.maxhenkel.voicechat.adapter.network;

import de.maxhenkel.voicechat.adapter.VoiceChatAdapterPlugin;
import de.maxhenkel.voicechat.adapter.model.PlayerState;
import org.bukkit.entity.Player;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.UUID;

/**
 * PlayerState网络通信管理器
 * 负责向客户端发送PlayerState相关的插件消息
 */
public class PlayerStateNetworkManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerStateNetworkManager.class);

    // 启用网络通信 - 这对群组成员显示至关重要
    private static final boolean ENABLE_NETWORK = true;

    private final VoiceChatAdapterPlugin plugin;
    
    public PlayerStateNetworkManager(VoiceChatAdapterPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 注册插件消息通道
     */
    public void register() {
        // 注册输出通道
        plugin.getServer().getMessenger().registerOutgoingPluginChannel(plugin, PlayerStatePacket.CHANNEL);
        plugin.getServer().getMessenger().registerOutgoingPluginChannel(plugin, PlayerStatesPacket.CHANNEL);
        
        LOGGER.info("Registered PlayerState plugin message channels");
    }
    
    /**
     * 注销插件消息通道
     */
    public void unregister() {
        plugin.getServer().getMessenger().unregisterOutgoingPluginChannel(plugin, PlayerStatePacket.CHANNEL);
        plugin.getServer().getMessenger().unregisterOutgoingPluginChannel(plugin, PlayerStatesPacket.CHANNEL);
        
        LOGGER.info("Unregistered PlayerState plugin message channels");
    }
    
    /**
     * 向单个玩家发送PlayerState更新
     */
    public void sendPlayerState(Player player, PlayerState state) {
        if (!ENABLE_NETWORK) {
            LOGGER.debug("PlayerState network communication disabled - skipping send to {}", player.getName());
            return;
        }

        try {
            PlayerStatePacket packet = new PlayerStatePacket(state);
            byte[] data = packet.toByteArray();

            LOGGER.debug("Sending PlayerState packet to {}: {} bytes", player.getName(), data.length);

            plugin.getServer().getScheduler().runTask(plugin, () -> {
                try {
                    player.sendPluginMessage(plugin, PlayerStatePacket.CHANNEL, data);
                    LOGGER.debug("Sent PlayerState to {}: {}", player.getName(), state);
                } catch (Exception e) {
                    LOGGER.warn("Failed to send PlayerState to {}: {}", player.getName(), e.getMessage());
                }
            });

        } catch (Exception e) {
            LOGGER.error("Failed to create PlayerState packet for {}: {}", player.getName(), e.getMessage());
        }
    }
    
    /**
     * 向单个玩家发送所有PlayerState
     */
    public void sendAllPlayerStates(Player player, Map<UUID, PlayerState> states) {
        if (!ENABLE_NETWORK) {
            LOGGER.debug("PlayerState network communication disabled - skipping send {} states to {}", states.size(), player.getName());
            return;
        }

        try {
            PlayerStatesPacket packet = new PlayerStatesPacket(states);
            byte[] data = packet.toByteArray();

            LOGGER.debug("Sending PlayerStates packet to {}: {} bytes for {} states", player.getName(), data.length, states.size());

            plugin.getServer().getScheduler().runTask(plugin, () -> {
                try {
                    // 检查玩家是否支持该通道
                    if (!player.getListeningPluginChannels().contains(PlayerStatesPacket.CHANNEL)) {
                        LOGGER.warn("Player {} does not support channel {}, supported channels: {}",
                                   player.getName(), PlayerStatesPacket.CHANNEL, player.getListeningPluginChannels());
                    }

                    player.sendPluginMessage(plugin, PlayerStatesPacket.CHANNEL, data);
                    LOGGER.debug("Sent {} PlayerStates to {}", states.size(), player.getName());
                } catch (Exception e) {
                    LOGGER.error("Failed to send PlayerStates to {}: {}", player.getName(), e.getMessage(), e);
                }
            });

        } catch (Exception e) {
            LOGGER.error("Failed to create PlayerStates packet for {}: {}", player.getName(), e.getMessage());
        }
    }
    
    /**
     * 向所有在线玩家广播PlayerState更新
     */
    public void broadcastPlayerState(PlayerState state) {
        for (Player player : plugin.getServer().getOnlinePlayers()) {
            sendPlayerState(player, state);
        }
    }
    
    /**
     * 向所有在线玩家广播所有PlayerState
     */
    public void broadcastAllPlayerStates(Map<UUID, PlayerState> states) {
        for (Player player : plugin.getServer().getOnlinePlayers()) {
            sendAllPlayerStates(player, states);
        }
    }
}
