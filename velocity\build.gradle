plugins {
    id 'java'
}

apply plugin: 'com.gradleup.shadow'
apply plugin: 'com.modrinth.minotaur'
apply plugin: 'io.papermc.hangar-publish-plugin'
apply plugin: 'xyz.jpenilla.run-velocity'

apply from: "https://raw.githubusercontent.com/henkelmax/mod-gradle-scripts/${mod_gradle_script_version}/mod.gradle"

repositories {
    mavenCentral()
    maven { url = 'https://repo.papermc.io/repository/maven-public/' }
}

dependencies {
    compileOnly "com.velocitypowered:velocity-api:${velocity_version}"
    annotationProcessor "com.velocitypowered:velocity-api:${velocity_version}"
}

tasks.register('generateJava', Copy) {
    from project(':common-proxy').file('src/template/java')
    into "${layout.buildDirectory.asFile.get()}/generated/java"
    expand 'mod_version': mod_version,
            'compatibility_version': voicechat_compatibility_version
}

sourceSets.main.java {
    srcDir "${layout.buildDirectory.asFile.get()}/generated/java"
}

tasks {
    runVelocity {
        velocityVersion('3.3.0-SNAPSHOT')
    }
}

compileJava.dependsOn generateJava
build.dependsOn shadowJar
runVelocity.dependsOn(build)
