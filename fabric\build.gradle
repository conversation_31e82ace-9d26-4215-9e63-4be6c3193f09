apply plugin: 'fabric-loom'
apply plugin: 'com.gradleup.shadow'
apply plugin: 'com.matthewprenger.cursegradle'
apply plugin: 'com.modrinth.minotaur'
apply plugin: 'mod-update'

apply from: "https://raw.githubusercontent.com/henkelmax/mod-gradle-scripts/${mod_gradle_script_version}/mod.gradle"

processResources {
    filesMatching('fabric.mod.json') {
        expand 'java_version': java_version,
                'mod_version': mod_version,
                'minecraft_version': minecraft_version,
                'minecraft_dependency': minecraft_dependency,
                'fabric_loader_dependency': fabric_loader_dependency,
                'modmenu_version': modmenu_version,
                'cloth_config_version': cloth_config_version,
                'viaversion_version': viaversion_version,
                'fabric_api_dependency_breaks': fabric_api_dependency_breaks
    }
}

repositories {
    maven { url = 'https://maven.shedaniel.me/' }
    maven { url = 'https://maven.terraformersmc.com/releases' }
    maven { url = 'https://repo.viaversion.com/' }
}

dependencies {
    implementation 'org.jetbrains:annotations:23.0.0'

    modImplementation "com.terraformersmc:modmenu:${modmenu_version}"
    modApi("me.shedaniel.cloth:cloth-config-fabric:${cloth_config_version}") {
        exclude(group: 'net.fabricmc.fabric-api')
    }

    modImplementation "me.lucko:fabric-permissions-api:${fabric_permission_api_version}"
    modCompileOnly "com.viaversion:viaversion-common:${viaversion_version}"

    implementation "org.concentus:Concentus:${concentus_version}"
    shadow "org.concentus:Concentus:${concentus_version}"

    implementation "de.maxhenkel.opus4j:opus4j:${opus4j_version}"
    shadow "de.maxhenkel.opus4j:opus4j:${opus4j_version}"

    implementation "de.maxhenkel.rnnoise4j:rnnoise4j:${rnnoise4j_version}"
    shadow "de.maxhenkel.rnnoise4j:rnnoise4j:${rnnoise4j_version}"

    implementation "de.maxhenkel.lame4j:lame4j:${lame4j_version}"
    shadow "de.maxhenkel.lame4j:lame4j:${lame4j_version}"
}

tasks.register('generateJava', Copy) {
    from project(':common').file('src/template/java')
    into "${layout.buildDirectory.asFile.get()}/generated/java"
    expand 'compatibility_version': voicechat_compatibility_version,
            'minecraft_version': minecraft_version
}
sourceSets.main.java {
    srcDir "${layout.buildDirectory.asFile.get()}/generated/java"
}
compileJava.dependsOn generateJava

shadowJar {
    relocate 'org.concentus', 'de.maxhenkel.voicechat.concentus'
    exclude 'natives/*-x86/*'
}
