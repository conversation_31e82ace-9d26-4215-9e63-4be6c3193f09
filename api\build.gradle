plugins {
    id 'java'
    id 'maven-publish'
}

sourceCompatibility = JavaLanguageVersion.of(8)
targetCompatibility = JavaLanguageVersion.of(8)

archivesBaseName = archives_base_name
version = version
group = maven_group

repositories {
    mavenCentral()
}

dependencies {
    implementation 'com.google.code.findbugs:jsr305:3.0.2'
}

tasks.register('packageJavadoc', Jar) {
    dependsOn 'javadoc'
    from javadoc.destinationDir
    archiveClassifier = 'javadoc'
}

tasks.register('packageSources', Jar) {
    dependsOn 'classes'
    from sourceSets.main.allSource
    archiveClassifier = 'sources'
}

javadoc {
    title "Simple Voice Chat API Version ${version}"
    options.windowTitle = "Simple Voice Chat API"
}

artifacts {
    archives packageJavadoc
    archives packageSources
}

publishing {
    publications {
        voiceChatApi(MavenPublication) {
            artifactId archives_base_name
            artifact(jar.archivePath) {
                builtBy build
                classifier null
            }
            artifact packageJavadoc
            artifact packageSources
        }
    }
    repositories {
        maven {
            name = 'henkelmax.public'
            url = 'https://maven.maxhenkel.de/repository/public'
            credentials {
                username System.getenv('MAVEN_USERNAME')
                password System.getenv('MAVEN_PASSWORD')
            }
        }
    }
}