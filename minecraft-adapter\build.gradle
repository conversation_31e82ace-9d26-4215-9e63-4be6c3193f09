plugins {
    id 'java'
    id 'com.gradleup.shadow' version '8.3.6'
}

group = 'de.maxhenkel.voicechat'
version = '1.0.0'

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

repositories {
    mavenCentral()
    maven {
        name = 'PaperMC'
        url = 'https://repo.papermc.io/repository/maven-public/'
    }
    maven {
        name = 'SpigotMC'
        url = 'https://hub.spigotmc.org/nexus/content/repositories/snapshots/'
    }
}

dependencies {
    // Bukkit/Spigot/Paper API
    compileOnly 'org.spigotmc:spigot-api:1.20.1-R0.1-SNAPSHOT'
    
    // HTTP Client
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
    
    // JSON处理
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    
    // 配置文件处理
    implementation 'org.yaml:snakeyaml:2.1'
    
    // 日志
    implementation 'org.slf4j:slf4j-api:2.0.7'
    
    // 测试
    testImplementation 'org.junit.jupiter:junit-jupiter:5.10.0'
    testImplementation 'org.mockito:mockito-core:5.5.0'
}

shadowJar {
    archiveClassifier.set('')
    mergeServiceFiles()
    
    // 重定位依赖以避免冲突
    relocate 'okhttp3', 'de.maxhenkel.voicechat.adapter.libs.okhttp3'
    relocate 'com.fasterxml.jackson', 'de.maxhenkel.voicechat.adapter.libs.jackson'
    relocate 'org.yaml.snakeyaml', 'de.maxhenkel.voicechat.adapter.libs.snakeyaml'
}

test {
    useJUnitPlatform()
}

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}
