package de.maxhenkel.voicechat.standalone.network.packets;

import de.maxhenkel.voicechat.standalone.network.Packet;
import io.netty.buffer.ByteBuf;

import java.util.UUID;

/**
 * 麦克风音频数据包
 */
public class MicPacket extends Packet<MicPacket> {
    
    public static final byte PACKET_ID = 1;
    
    private UUID sender;
    private byte[] data;
    private boolean whispering;
    private long sequenceNumber;
    
    public MicPacket() {}
    
    public MicPacket(UUID sender, byte[] data, boolean whispering, long sequenceNumber) {
        this.sender = sender;
        this.data = data;
        this.whispering = whispering;
        this.sequenceNumber = sequenceNumber;
    }
    
    @Override
    public void toBytes(ByteBuf buf) {
        buf.writeLong(sender.getMostSignificantBits());
        buf.writeLong(sender.getLeastSignificantBits());
        buf.writeBoolean(whispering);
        buf.writeLong(sequenceNumber);
        buf.writeInt(data.length);
        buf.writeBytes(data);
    }
    
    @Override
    public void fromBytes(ByteBuf buf) {
        // 按照原项目的顺序读取数据
        // 1. 读取音频数据（使用VarInt长度前缀）
        data = readByteArray(buf);

        // 2. 读取序列号
        sequenceNumber = buf.readLong();

        // 3. 读取是否为耳语
        whispering = buf.readBoolean();

        // sender在UDP数据包中已经提供，不需要从数据包内容中读取
        // 这将在NetworkMessage解析时设置
    }

    /**
     * 读取字节数组（与FriendlyByteBuf.readByteArray()保持一致）
     */
    private static byte[] readByteArray(ByteBuf buf) {
        int length = readVarInt(buf);
        byte[] data = new byte[length];
        buf.readBytes(data);
        return data;
    }

    /**
     * 读取VarInt（与FriendlyByteBuf.readVarInt()保持一致）
     */
    private static int readVarInt(ByteBuf buf) {
        int i = 0;
        int j = 0;

        byte b;
        do {
            b = buf.readByte();
            i |= (b & 127) << j++ * 7;
            if (j > 5) {
                throw new RuntimeException("VarInt too big");
            }
        } while ((b & 128) == 128);

        return i;
    }
    
    @Override
    public byte getPacketId() {
        return PACKET_ID;
    }
    
    @Override
    public Class<MicPacket> getPacketClass() {
        return MicPacket.class;
    }
    
    // Getters and Setters
    public UUID getSender() { return sender; }
    public void setSender(UUID sender) { this.sender = sender; }
    public byte[] getData() { return data; }
    public boolean isWhispering() { return whispering; }
    public long getSequenceNumber() { return sequenceNumber; }
}
