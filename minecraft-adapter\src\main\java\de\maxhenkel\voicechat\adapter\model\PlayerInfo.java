package de.maxhenkel.voicechat.adapter.model;

import java.util.List;
import java.util.UUID;

/**
 * 玩家信息模型
 */
public class PlayerInfo {
    
    private final UUID uuid;
    private final String name;
    private Position position;
    private List<String> permissions;
    private int gameMode; // 0=生存, 1=创造, 2=冒险, 3=旁观
    
    public PlayerInfo(UUID uuid, String name) {
        this.uuid = uuid;
        this.name = name;
    }
    
    // Getters and Setters
    public UUID getUuid() { return uuid; }
    public String getName() { return name; }
    
    public Position getPosition() { return position; }
    public void setPosition(Position position) { this.position = position; }
    
    public List<String> getPermissions() { return permissions; }
    public void setPermissions(List<String> permissions) { this.permissions = permissions; }

    public int getGameMode() { return gameMode; }
    public void setGameMode(int gameMode) { this.gameMode = gameMode; }

    @Override
    public String toString() {
        return "PlayerInfo{" +
                "uuid=" + uuid +
                ", name='" + name + '\'' +
                ", position=" + position +
                ", permissions=" + permissions +
                ", gameMode=" + gameMode +
                '}';
    }
}
