package de.maxhenkel.voicechat.net;

import de.maxhenkel.voicechat.Voicechat;
import de.maxhenkel.voicechat.config.ServerConfig;
import de.maxhenkel.voicechat.plugins.PluginManager;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.protocol.common.custom.CustomPacketPayload;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;

import java.util.UUID;

public class SecretPacket implements Packet<SecretPacket> {

    public static final CustomPacketPayload.Type<SecretPacket> SECRET = new CustomPacketPayload.Type<>(ResourceLocation.fromNamespaceAndPath(Voicechat.MODID, "secret"));

    private UUID secret;
    private int serverPort;
    private UUID playerUUID;
    private ServerConfig.Codec codec;
    private int mtuSize;
    private double voiceChatDistance;
    private int keepAlive;
    private boolean groupsEnabled;
    private String voiceHost;
    private boolean allowRecording;

    // 新增：独立语音服务器支持
    private String standaloneVoiceHost;
    private int standaloneVoicePort;
    private boolean useStandaloneServer;

    public SecretPacket() {

    }

    public SecretPacket(ServerPlayer player, UUID secret, int port, ServerConfig serverConfig) {
        this.secret = secret;
        this.serverPort = port;
        this.playerUUID = player.getUUID();
        this.codec = serverConfig.voiceChatCodec.get();
        this.mtuSize = serverConfig.voiceChatMtuSize.get();
        this.voiceChatDistance = serverConfig.voiceChatDistance.get();
        this.keepAlive = serverConfig.keepAlive.get();
        this.groupsEnabled = serverConfig.groupsEnabled.get();
        this.voiceHost = PluginManager.instance().getVoiceHost(serverConfig.voiceHost.get());
        this.allowRecording = serverConfig.allowRecording.get();

        // 默认不使用独立服务器（向后兼容）
        this.useStandaloneServer = false;
        this.standaloneVoiceHost = null;
        this.standaloneVoicePort = 0;
    }

    /**
     * 构造函数：支持独立语音服务器
     */
    public SecretPacket(ServerPlayer player, UUID secret, int port, ServerConfig serverConfig,
                       String standaloneVoiceHost, int standaloneVoicePort) {
        this(player, secret, port, serverConfig);

        // 设置独立服务器信息
        this.useStandaloneServer = standaloneVoiceHost != null && !standaloneVoiceHost.isEmpty();
        this.standaloneVoiceHost = standaloneVoiceHost;
        this.standaloneVoicePort = standaloneVoicePort;
    }

    public UUID getSecret() {
        return secret;
    }

    public int getServerPort() {
        return serverPort;
    }

    public UUID getPlayerUUID() {
        return playerUUID;
    }

    public ServerConfig.Codec getCodec() {
        return codec;
    }

    public int getMtuSize() {
        return mtuSize;
    }

    public double getVoiceChatDistance() {
        return voiceChatDistance;
    }

    public int getKeepAlive() {
        return keepAlive;
    }

    public boolean groupsEnabled() {
        return groupsEnabled;
    }

    public String getVoiceHost() {
        return voiceHost;
    }

    public boolean allowRecording() {
        return allowRecording;
    }

    /**
     * 是否使用独立语音服务器
     */
    public boolean useStandaloneServer() {
        return useStandaloneServer;
    }

    /**
     * 获取独立语音服务器地址
     */
    public String getStandaloneVoiceHost() {
        return standaloneVoiceHost;
    }

    /**
     * 获取独立语音服务器端口
     */
    public int getStandaloneVoicePort() {
        return standaloneVoicePort;
    }

    @Override
    public SecretPacket fromBytes(FriendlyByteBuf buf) {
        secret = buf.readUUID();
        serverPort = buf.readInt();
        playerUUID = buf.readUUID();
        codec = ServerConfig.Codec.values()[buf.readByte()];
        mtuSize = buf.readInt();
        voiceChatDistance = buf.readDouble();
        keepAlive = buf.readInt();
        groupsEnabled = buf.readBoolean();
        voiceHost = buf.readUtf(32767);
        allowRecording = buf.readBoolean();

        // 检查是否有额外的独立服务器数据（向后兼容）
        if (buf.isReadable()) {
            try {
                useStandaloneServer = buf.readBoolean();
                if (useStandaloneServer) {
                    standaloneVoiceHost = buf.readUtf(32767);
                    standaloneVoicePort = buf.readInt();
                } else {
                    standaloneVoiceHost = null;
                    standaloneVoicePort = 0;
                }
            } catch (Exception e) {
                // 如果读取失败，说明是旧版本，使用默认值
                useStandaloneServer = false;
                standaloneVoiceHost = null;
                standaloneVoicePort = 0;
            }
        } else {
            // 旧版本数据包，使用默认值
            useStandaloneServer = false;
            standaloneVoiceHost = null;
            standaloneVoicePort = 0;
        }

        return this;
    }

    @Override
    public void toBytes(FriendlyByteBuf buf) {
        buf.writeUUID(secret);
        buf.writeInt(serverPort);
        buf.writeUUID(playerUUID);
        buf.writeByte(codec.ordinal());
        buf.writeInt(mtuSize);
        buf.writeDouble(voiceChatDistance);
        buf.writeInt(keepAlive);
        buf.writeBoolean(groupsEnabled);
        buf.writeUtf(voiceHost);
        buf.writeBoolean(allowRecording);

        // 只有在使用独立服务器时才写入额外字段
        // 这样可以保持与旧客户端的兼容性
        if (useStandaloneServer && standaloneVoiceHost != null && !standaloneVoiceHost.isEmpty()) {
            buf.writeBoolean(true);  // useStandaloneServer
            buf.writeUtf(standaloneVoiceHost);
            buf.writeInt(standaloneVoicePort);
        }
        // 如果不使用独立服务器，就不写入任何额外字段，保持原始格式
    }

    @Override
    public Type<SecretPacket> type() {
        return SECRET;
    }

}
