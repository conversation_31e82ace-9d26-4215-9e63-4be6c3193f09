package de.maxhenkel.voicechat.standalone.model;

/**
 * 群组类型枚举
 */
public enum GroupType {
    
    /**
     * 普通群组 (0)
     * - 群组内成员只能听到群组内的声音
     * - 但可以听到附近非群组玩家的声音
     * - 非群组玩家听不到群组内的声音
     */
    NORMAL(0, "normal"),
    
    /**
     * 开放群组 (1)
     * - 群组内成员可以听到群组内和附近非群组玩家的声音
     * - 附近非群组玩家也可以听到群组内的声音
     */
    OPEN(1, "open"),
    
    /**
     * 隔离群组 (2)
     * - 群组内成员只能听到群组内的声音
     * - 完全与外界隔离
     */
    ISOLATED(2, "isolated");
    
    private final int id;
    private final String name;
    
    GroupType(int id, String name) {
        this.id = id;
        this.name = name;
    }
    
    public int getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 从ID获取群组类型
     */
    public static GroupType fromId(int id) {
        for (GroupType type : values()) {
            if (type.getId() == id) {
                return type;
            }
        }
        return NORMAL; // 默认返回普通群组
    }
    
    /**
     * 从名称获取群组类型
     */
    public static GroupType fromName(String name) {
        for (GroupType type : values()) {
            if (type.getName().equalsIgnoreCase(name)) {
                return type;
            }
        }
        return NORMAL; // 默认返回普通群组
    }
    
    /**
     * 检查是否为开放群组
     */
    public boolean isOpen() {
        return this == OPEN;
    }
    
    /**
     * 检查是否为隔离群组
     */
    public boolean isIsolated() {
        return this == ISOLATED;
    }
    
    /**
     * 检查是否为普通群组
     */
    public boolean isNormal() {
        return this == NORMAL;
    }
    
    @Override
    public String toString() {
        return name + "(" + id + ")";
    }
}
