# Docker Compose 配置文件
# 用于快速部署独立语音服务器和Minecraft服务器

version: '3.8'

services:
  # 独立语音服务器
  voice-server:
    build:
      context: ./standalone-voice-server
      dockerfile: Dockerfile
    container_name: voice-server
    ports:
      - "24454:24454/udp"  # 语音UDP端口
      - "8080:8080"        # API HTTP端口
    volumes:
      - ./voice-server-data:/app/data
      - ./voice-server-logs:/app/logs
    environment:
      - JAVA_OPTS=-Xmx1G -Xms512M
    restart: unless-stopped
    networks:
      - voicechat-network

  # Minecraft服务器 (示例)
  minecraft-server:
    image: itzg/minecraft-server:java17
    container_name: minecraft-server
    ports:
      - "25565:25565"
    volumes:
      - ./minecraft-data:/data
      - ./minecraft-adapter/build/libs/minecraft-adapter-1.0.0.jar:/data/plugins/VoiceChatAdapter.jar
    environment:
      - EULA=TRUE
      - TYPE=PAPER
      - VERSION=1.20.1
      - MEMORY=2G
      - PLUGINS_SYNC_UPDATE=false
    depends_on:
      - voice-server
    restart: unless-stopped
    networks:
      - voicechat-network

  # 可选：监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - voicechat-network

  # 可选：Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    networks:
      - voicechat-network

networks:
  voicechat-network:
    driver: bridge

volumes:
  grafana-storage:
