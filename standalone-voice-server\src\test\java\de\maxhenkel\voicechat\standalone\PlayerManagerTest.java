package de.maxhenkel.voicechat.standalone;

import de.maxhenkel.voicechat.standalone.model.PlayerData;
import de.maxhenkel.voicechat.standalone.model.Position;
import de.maxhenkel.voicechat.standalone.model.Permission;
import de.maxhenkel.voicechat.standalone.server.PlayerManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 玩家管理器测试
 */
public class PlayerManagerTest {
    
    private PlayerManager playerManager;
    private UUID testPlayerUuid;
    private PlayerData testPlayer;
    
    @BeforeEach
    void setUp() {
        playerManager = new PlayerManager();
        testPlayerUuid = UUID.randomUUID();
        testPlayer = new PlayerData(testPlayerUuid, "TestPlayer", "testserver");
    }
    
    @Test
    void testAddPlayer() {
        playerManager.addPlayer(testPlayer);
        
        PlayerData retrieved = playerManager.getPlayer(testPlayerUuid);
        assertNotNull(retrieved);
        assertEquals("TestPlayer", retrieved.getName());
        assertEquals("testserver", retrieved.getServerName());
        assertTrue(retrieved.isOnline());
    }
    
    @Test
    void testRemovePlayer() {
        playerManager.addPlayer(testPlayer);
        playerManager.removePlayer(testPlayerUuid);
        
        PlayerData retrieved = playerManager.getPlayer(testPlayerUuid);
        assertNull(retrieved);
    }
    
    @Test
    void testUpdatePlayerPosition() {
        playerManager.addPlayer(testPlayer);
        
        Position newPosition = new Position("world", 10.0, 64.0, 20.0);
        playerManager.updatePlayerPosition(testPlayerUuid, newPosition);
        
        PlayerData retrieved = playerManager.getPlayer(testPlayerUuid);
        assertNotNull(retrieved.getPosition());
        assertEquals("world", retrieved.getPosition().getWorldId());
        assertEquals(10.0, retrieved.getPosition().getX());
        assertEquals(64.0, retrieved.getPosition().getY());
        assertEquals(20.0, retrieved.getPosition().getZ());
    }
    
    @Test
    void testUpdatePlayerPermissions() {
        playerManager.addPlayer(testPlayer);
        
        Set<Permission> permissions = Set.of(Permission.SPEAK, Permission.GROUPS);
        playerManager.updatePlayerPermissions(testPlayerUuid, permissions);
        
        PlayerData retrieved = playerManager.getPlayer(testPlayerUuid);
        assertTrue(retrieved.hasPermission(Permission.SPEAK));
        assertTrue(retrieved.hasPermission(Permission.GROUPS));
        assertFalse(retrieved.hasPermission(Permission.ADMIN));
    }
    
    @Test
    void testSetPlayerOnlineStatus() {
        playerManager.addPlayer(testPlayer);
        
        playerManager.setPlayerOnline(testPlayerUuid, false);
        
        PlayerData retrieved = playerManager.getPlayer(testPlayerUuid);
        assertFalse(retrieved.isOnline());
        assertFalse(retrieved.isVoiceConnected()); // 离线时语音连接也应该断开
    }
    
    @Test
    void testGetPlayersInRange() {
        // 添加多个玩家
        PlayerData player1 = new PlayerData(UUID.randomUUID(), "Player1", "testserver");
        player1.setPosition(new Position("world", 0.0, 64.0, 0.0));
        
        PlayerData player2 = new PlayerData(UUID.randomUUID(), "Player2", "testserver");
        player2.setPosition(new Position("world", 10.0, 64.0, 0.0));
        
        PlayerData player3 = new PlayerData(UUID.randomUUID(), "Player3", "testserver");
        player3.setPosition(new Position("world", 100.0, 64.0, 0.0));
        
        playerManager.addPlayer(player1);
        playerManager.addPlayer(player2);
        playerManager.addPlayer(player3);
        
        // 测试范围查询
        Position center = new Position("world", 0.0, 64.0, 0.0);
        var playersInRange = playerManager.getPlayersInRange(center, 20.0, "world");
        
        assertEquals(2, playersInRange.size()); // player1 和 player2 在范围内
        assertTrue(playersInRange.stream().anyMatch(p -> p.getName().equals("Player1")));
        assertTrue(playersInRange.stream().anyMatch(p -> p.getName().equals("Player2")));
        assertFalse(playersInRange.stream().anyMatch(p -> p.getName().equals("Player3")));
    }
    
    @Test
    void testGetStatistics() {
        // 添加一些测试数据
        PlayerData player1 = new PlayerData(UUID.randomUUID(), "Player1", "server1");
        PlayerData player2 = new PlayerData(UUID.randomUUID(), "Player2", "server1");
        player2.setVoiceConnected(true);
        
        PlayerData player3 = new PlayerData(UUID.randomUUID(), "Player3", "server2");
        player3.setOnline(false);
        
        playerManager.addPlayer(player1);
        playerManager.addPlayer(player2);
        playerManager.addPlayer(player3);
        
        var stats = playerManager.getStatistics();
        
        assertEquals(3, stats.get("totalPlayers"));
        assertEquals(2, stats.get("onlinePlayers"));
        assertEquals(1, stats.get("voiceConnectedPlayers"));
    }
}
