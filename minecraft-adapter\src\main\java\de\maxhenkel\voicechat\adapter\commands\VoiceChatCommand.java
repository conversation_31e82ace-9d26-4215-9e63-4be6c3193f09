package de.maxhenkel.voicechat.adapter.commands;

import de.maxhenkel.voicechat.adapter.VoiceChatAdapterPlugin;
import de.maxhenkel.voicechat.adapter.network.VoiceServerClient;
import de.maxhenkel.voicechat.adapter.group.AdvancedGroupManager;
import de.maxhenkel.voicechat.adapter.permission.PermissionSyncManager;
import de.maxhenkel.voicechat.adapter.broadcast.VoiceBroadcastManager;
import org.bukkit.ChatColor;
import org.bukkit.OfflinePlayer;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 语音聊天命令处理器
 */
public class VoiceChatCommand implements CommandExecutor, TabCompleter {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(VoiceChatCommand.class);
    
    private final VoiceChatAdapterPlugin plugin;
    private final VoiceServerClient voiceServerClient;
    
    public VoiceChatCommand(VoiceChatAdapterPlugin plugin, VoiceServerClient voiceServerClient) {
        this.plugin = plugin;
        this.voiceServerClient = voiceServerClient;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "status":
                handleStatusCommand(sender);
                break;
            case "reload":
                handleReloadCommand(sender);
                break;
            case "secret":
                handleSecretCommand(sender, args);
                break;
            case "help":
                sendHelp(sender);
                break;
            case "syncgroups":
                handleSyncGroupsCommand(sender);
                break;
            case "invite":
                handleInviteCommand(sender, args);
                break;
            case "kick":
                handleKickCommand(sender, args);
                break;
            case "ban":
                handleBanCommand(sender, args);
                break;
            case "mod":
            case "moderator":
                handleModeratorCommand(sender, args);
                break;
            case "transfer":
                handleTransferCommand(sender, args);
                break;
            case "syncperms":
                handleSyncPermissionsCommand(sender, args);
                break;
            case "opus":
                handleOpusCommand(sender, args);
                break;
            case "admin":
                handleAdminCommand(sender, Arrays.copyOfRange(args, 1, args.length));
                break;
            default:
                sender.sendMessage(ChatColor.RED + "Unknown subcommand: " + subCommand);
                sendHelp(sender);
                break;
        }
        
        return true;
    }
    
    /**
     * 处理状态命令
     */
    private void handleStatusCommand(CommandSender sender) {
        if (!sender.hasPermission("voicechat.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return;
        }
        
        sender.sendMessage(ChatColor.YELLOW + "Fetching voice server status...");
        
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    Map<String, Object> status = voiceServerClient.getServerStatus();
                    
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.GREEN + "=== Voice Server Status ===");
                            
                            Map<String, Object> voiceServer = (Map<String, Object>) status.get("voiceServer");
                            if (voiceServer != null) {
                                Map<String, Object> players = (Map<String, Object>) voiceServer.get("players");
                                Map<String, Object> groups = (Map<String, Object>) voiceServer.get("groups");
                                
                                if (players != null) {
                                    sender.sendMessage(ChatColor.AQUA + "Players:");
                                    sender.sendMessage("  Total: " + players.get("totalPlayers"));
                                    sender.sendMessage("  Online: " + players.get("onlinePlayers"));
                                    sender.sendMessage("  Voice Connected: " + players.get("voiceConnectedPlayers"));
                                }
                                
                                if (groups != null) {
                                    sender.sendMessage(ChatColor.AQUA + "Groups:");
                                    sender.sendMessage("  Total: " + groups.get("totalGroups"));
                                    sender.sendMessage("  Members: " + groups.get("totalMembers"));
                                }
                            }
                            
                        }
                    }.runTask(plugin);
                    
                } catch (Exception e) {
                    LOGGER.error("Failed to get voice server status", e);
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.RED + "Failed to get voice server status: " + e.getMessage());
                        }
                    }.runTask(plugin);
                }
            }
        }.runTaskAsynchronously(plugin);
    }
    
    /**
     * 处理重载命令
     */
    private void handleReloadCommand(CommandSender sender) {
        if (!sender.hasPermission("voicechat.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return;
        }
        
        sender.sendMessage(ChatColor.YELLOW + "Reloading voice chat adapter configuration...");
        
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    plugin.reloadConfiguration();
                    
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.GREEN + "Configuration reloaded successfully!");
                        }
                    }.runTask(plugin);
                    
                } catch (Exception e) {
                    LOGGER.error("Failed to reload configuration", e);
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.RED + "Failed to reload configuration: " + e.getMessage());
                        }
                    }.runTask(plugin);
                }
            }
        }.runTaskAsynchronously(plugin);
    }
    
    /**
     * 处理密钥生成命令
     */
    private void handleSecretCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("voicechat.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return;
        }
        
        Player targetPlayer;
        if (args.length > 1) {
            targetPlayer = plugin.getServer().getPlayer(args[1]);
            if (targetPlayer == null) {
                sender.sendMessage(ChatColor.RED + "Player not found: " + args[1]);
                return;
            }
        } else if (sender instanceof Player) {
            targetPlayer = (Player) sender;
        } else {
            sender.sendMessage(ChatColor.RED + "You must specify a player name when using this command from console.");
            return;
        }
        
        sender.sendMessage(ChatColor.YELLOW + "Generating voice chat secret for " + targetPlayer.getName() + "...");
        
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    String secret = voiceServerClient.generatePlayerSecret(targetPlayer.getUniqueId());
                    
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.GREEN + "Voice chat secret for " + targetPlayer.getName() + ": " + secret);
                            if (!sender.equals(targetPlayer)) {
                                targetPlayer.sendMessage(ChatColor.GREEN + "Your voice chat secret: " + secret);
                            }
                        }
                    }.runTask(plugin);
                    
                } catch (Exception e) {
                    LOGGER.error("Failed to generate secret for {}", targetPlayer.getName(), e);
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.RED + "Failed to generate secret: " + e.getMessage());
                        }
                    }.runTask(plugin);
                }
            }
        }.runTaskAsynchronously(plugin);
    }

    /**
     * 处理群组同步命令
     */
    private void handleSyncGroupsCommand(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players.");
            return;
        }

        Player player = (Player) sender;

        try {
            plugin.getGroupMessageHandler().syncGroupsForPlayer(player);
            sender.sendMessage(ChatColor.GREEN + "Group synchronization started. Check console for details.");
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Failed to sync groups: " + e.getMessage());
            LOGGER.error("Failed to sync groups for player {}", player.getName(), e);
        }
    }

    /**
     * 发送帮助信息
     */
    private void sendHelp(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== Voice Chat Adapter Commands ===");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat status" + ChatColor.WHITE + " - Show voice server status");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat reload" + ChatColor.WHITE + " - Reload configuration");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat secret [player]" + ChatColor.WHITE + " - Generate voice chat secret");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat syncgroups" + ChatColor.WHITE + " - Manually sync voice chat groups");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat syncperms [player]" + ChatColor.WHITE + " - Sync player permissions");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat invite <player>" + ChatColor.WHITE + " - Invite player to group");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat kick <player>" + ChatColor.WHITE + " - Kick player from group");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat ban <player>" + ChatColor.WHITE + " - Ban player from group");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat mod <player> <add|remove>" + ChatColor.WHITE + " - Manage group moderators");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat transfer <player>" + ChatColor.WHITE + " - Transfer group ownership");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat opus <info|set|reset|codecs>" + ChatColor.WHITE + " - Manage Opus codec settings");
        sender.sendMessage(ChatColor.YELLOW + "/voicechat help" + ChatColor.WHITE + " - Show this help message");

        // 管理员命令
        if (sender.hasPermission("voicechat.admin") || sender.isOp()) {
            sender.sendMessage("");
            sender.sendMessage(ChatColor.RED + "Admin Commands:");
            sender.sendMessage(ChatColor.YELLOW + "/voicechat admin force-join <player> <group>" + ChatColor.WHITE + " - Force player to join group");
            sender.sendMessage(ChatColor.YELLOW + "/voicechat admin force-leave <player> <group>" + ChatColor.WHITE + " - Force player to leave group");
            sender.sendMessage(ChatColor.YELLOW + "/voicechat admin force-delete <group>" + ChatColor.WHITE + " - Force delete group");
            sender.sendMessage(ChatColor.YELLOW + "/voicechat admin global-ban <player> [reason]" + ChatColor.WHITE + " - Globally ban player from all groups");
            sender.sendMessage(ChatColor.YELLOW + "/voicechat admin global-unban <player>" + ChatColor.WHITE + " - Remove global ban");
            sender.sendMessage(ChatColor.YELLOW + "/voicechat admin global-kick <player> [reason]" + ChatColor.WHITE + " - Kick player from all groups");
            sender.sendMessage(ChatColor.YELLOW + "/voicechat admin start-broadcast <name>" + ChatColor.WHITE + " - Start voice broadcast to all players");
            sender.sendMessage(ChatColor.YELLOW + "/voicechat admin stop-broadcast" + ChatColor.WHITE + " - Stop current voice broadcast");
            sender.sendMessage(ChatColor.YELLOW + "/voicechat admin authorize-broadcaster <player>" + ChatColor.WHITE + " - Authorize player to broadcast");
            sender.sendMessage(ChatColor.YELLOW + "/voicechat admin unauthorize-broadcaster <player>" + ChatColor.WHITE + " - Remove broadcast authorization");
            sender.sendMessage(ChatColor.YELLOW + "/voicechat admin list-broadcasts" + ChatColor.WHITE + " - List active broadcasts and authorized broadcasters");
            sender.sendMessage(ChatColor.YELLOW + "/voicechat admin grant-spectator-voice <player>" + ChatColor.WHITE + " - Grant spectator voice interaction permission");
            sender.sendMessage(ChatColor.YELLOW + "/voicechat admin revoke-spectator-voice <player>" + ChatColor.WHITE + " - Revoke spectator voice interaction permission");
        }
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            List<String> subCommands = Arrays.asList("status", "reload", "secret", "help", "syncgroups", "syncperms", "invite", "kick", "ban", "mod", "moderator", "transfer", "opus");
            String partial = args[0].toLowerCase();
            
            for (String subCommand : subCommands) {
                if (subCommand.startsWith(partial)) {
                    completions.add(subCommand);
                }
            }
        } else if (args.length == 2 && args[0].equalsIgnoreCase("secret")) {
            String partial = args[1].toLowerCase();
            for (Player player : plugin.getServer().getOnlinePlayers()) {
                if (player.getName().toLowerCase().startsWith(partial)) {
                    completions.add(player.getName());
                }
            }
        }
        
        return completions;
    }

    /**
     * 处理邀请命令
     */
    private void handleInviteCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players");
            return;
        }

        Player player = (Player) sender;

        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat invite <player>");
            return;
        }

        String targetName = args[1];
        AdvancedGroupManager groupManager = plugin.getAdvancedGroupManager();

        if (groupManager == null) {
            sender.sendMessage(ChatColor.RED + "Group manager not available");
            return;
        }

        sender.sendMessage(ChatColor.YELLOW + "Player invitations are managed through the voice chat interface.");
        sender.sendMessage(ChatColor.YELLOW + "Group owners and moderators can invite players through the voice chat GUI.");
    }

    /**
     * 处理踢人命令
     */
    private void handleKickCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players");
            return;
        }

        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat kick <player>");
            return;
        }

        sender.sendMessage(ChatColor.YELLOW + "This command has been replaced by admin commands:");
        sender.sendMessage(ChatColor.YELLOW + "Use '/voicechat admin global-kick <player> [reason]' to kick from all groups");
        sender.sendMessage(ChatColor.YELLOW + "Use '/voicechat admin force-leave <player> <group>' to remove from specific group");
    }

    /**
     * 处理禁止命令
     */
    private void handleBanCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players");
            return;
        }

        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat ban <player>");
            return;
        }

        sender.sendMessage(ChatColor.YELLOW + "This command has been replaced by admin commands:");
        sender.sendMessage(ChatColor.YELLOW + "Use '/voicechat admin global-ban <player> [reason]' to ban from all groups");
    }

    /**
     * 处理管理员命令
     */
    private void handleModeratorCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players");
            return;
        }

        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat mod <player> <add|remove>");
            return;
        }

        sender.sendMessage(ChatColor.YELLOW + "Moderator functionality is managed through the voice server.");
        sender.sendMessage(ChatColor.YELLOW + "Group owners can set moderators through the voice chat interface.");
    }

    /**
     * 处理转移命令
     */
    private void handleTransferCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players");
            return;
        }

        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat transfer <player>");
            return;
        }

        sender.sendMessage(ChatColor.YELLOW + "Group ownership transfer is managed through the voice server.");
        sender.sendMessage(ChatColor.YELLOW + "Group owners can transfer ownership through the voice chat interface.");
    }

    /**
     * 处理权限同步命令
     */
    private void handleSyncPermissionsCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("voicechat.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command");
            return;
        }

        PermissionSyncManager permissionSyncManager = plugin.getPermissionSyncManager();
        if (permissionSyncManager == null) {
            sender.sendMessage(ChatColor.RED + "Permission sync manager not available");
            return;
        }

        if (args.length > 1) {
            // 同步特定玩家权限
            String playerName = args[1];
            Player target = plugin.getServer().getPlayer(playerName);
            if (target == null) {
                sender.sendMessage(ChatColor.RED + "Player not found: " + playerName);
                return;
            }

            permissionSyncManager.forceSyncPlayerPermissions(target);
            sender.sendMessage(ChatColor.GREEN + "Synced permissions for " + target.getName());
        } else {
            // 同步所有在线玩家权限
            permissionSyncManager.syncAllPlayerPermissions();
            sender.sendMessage(ChatColor.GREEN + "Synced permissions for all online players");
        }
    }

    /**
     * 处理Opus配置命令
     */
    private void handleOpusCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("voicechat.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command");
            return;
        }

        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat opus <info|set|reset|codecs>");
            return;
        }

        String subCommand = args[1].toLowerCase();
        VoiceServerClient client = plugin.getVoiceServerClient();

        try {
            switch (subCommand) {
                case "info":
                    handleOpusInfoCommand(sender, client);
                    break;
                case "set":
                    handleOpusSetCommand(sender, args, client);
                    break;
                case "reset":
                    handleOpusResetCommand(sender, client);
                    break;
                case "codecs":
                    handleOpusCodecsCommand(sender, client);
                    break;
                default:
                    sender.sendMessage(ChatColor.RED + "Unknown opus subcommand: " + subCommand);
                    sender.sendMessage(ChatColor.YELLOW + "Available: info, set, reset, codecs");
                    break;
            }
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Error executing opus command: " + e.getMessage());
            LOGGER.error("Error in opus command", e);
        }
    }

    private void handleOpusInfoCommand(CommandSender sender, VoiceServerClient client) throws Exception {
        Map<String, Object> config = client.getOpusConfig();

        sender.sendMessage(ChatColor.GOLD + "=== Opus Configuration ===");
        sender.sendMessage(ChatColor.YELLOW + "Current Codec: " + ChatColor.WHITE + config.get("currentCodec"));
        sender.sendMessage(ChatColor.YELLOW + "Description: " + ChatColor.WHITE + config.get("codecDescription"));
        sender.sendMessage(ChatColor.YELLOW + "Bitrate: " + ChatColor.WHITE + config.get("bitrate") + " bps");
        sender.sendMessage(ChatColor.YELLOW + "Complexity: " + ChatColor.WHITE + config.get("complexity"));
        sender.sendMessage(ChatColor.YELLOW + "DTX Enabled: " + ChatColor.WHITE + config.get("dtxEnabled"));
        sender.sendMessage(ChatColor.YELLOW + "FEC Enabled: " + ChatColor.WHITE + config.get("fecEnabled"));
        sender.sendMessage(ChatColor.YELLOW + "Max Packet Size: " + ChatColor.WHITE + config.get("maxPacketSize") + " bytes");
        sender.sendMessage(ChatColor.YELLOW + "Recommended MTU: " + ChatColor.WHITE + config.get("recommendedMtuSize") + " bytes");
    }

    private void handleOpusSetCommand(CommandSender sender, String[] args, VoiceServerClient client) throws Exception {
        if (args.length < 4) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat opus set <codec|bitrate|complexity|dtx|fec> <value>");
            return;
        }

        String setting = args[2].toLowerCase();
        String value = args[3];

        Map<String, Object> updateConfig = new HashMap<>();

        switch (setting) {
            case "codec":
                updateConfig.put("codec", value.toUpperCase());
                break;
            case "bitrate":
                try {
                    int bitrate = Integer.parseInt(value);
                    updateConfig.put("bitrate", bitrate);
                } catch (NumberFormatException e) {
                    sender.sendMessage(ChatColor.RED + "Invalid bitrate value: " + value);
                    return;
                }
                break;
            case "complexity":
                try {
                    int complexity = Integer.parseInt(value);
                    if (complexity < 0 || complexity > 10) {
                        sender.sendMessage(ChatColor.RED + "Complexity must be between 0 and 10");
                        return;
                    }
                    updateConfig.put("complexity", complexity);
                } catch (NumberFormatException e) {
                    sender.sendMessage(ChatColor.RED + "Invalid complexity value: " + value);
                    return;
                }
                break;
            case "dtx":
                updateConfig.put("dtxEnabled", Boolean.parseBoolean(value));
                break;
            case "fec":
                updateConfig.put("fecEnabled", Boolean.parseBoolean(value));
                break;
            default:
                sender.sendMessage(ChatColor.RED + "Unknown setting: " + setting);
                sender.sendMessage(ChatColor.YELLOW + "Available: codec, bitrate, complexity, dtx, fec");
                return;
        }

        client.updateOpusConfig(updateConfig);
        sender.sendMessage(ChatColor.GREEN + "Opus " + setting + " updated to: " + value);
    }

    private void handleOpusResetCommand(CommandSender sender, VoiceServerClient client) throws Exception {
        client.resetOpusConfig();
        sender.sendMessage(ChatColor.GREEN + "Opus configuration reset to defaults");
    }

    private void handleOpusCodecsCommand(CommandSender sender, VoiceServerClient client) throws Exception {
        Map<String, Object> response = client.getAvailableCodecs();
        List<Map<String, Object>> codecs = (List<Map<String, Object>>) response.get("codecs");

        sender.sendMessage(ChatColor.GOLD + "=== Available Opus Codecs ===");
        for (Map<String, Object> codec : codecs) {
            sender.sendMessage(ChatColor.YELLOW + String.valueOf(codec.get("name")) + ChatColor.WHITE + " - " + String.valueOf(codec.get("description")));
            sender.sendMessage(ChatColor.GRAY + "  Recommended Bitrate: " + String.valueOf(codec.get("recommendedBitrate")) + " bps");
            sender.sendMessage(ChatColor.GRAY + "  Complexity: " + String.valueOf(codec.get("complexity")));
            sender.sendMessage(ChatColor.GRAY + "  Frame Size: " + String.valueOf(codec.get("frameSize")) + " ms");
        }
    }

    /**
     * 处理管理员命令
     */
    private void handleAdminCommand(CommandSender sender, String[] args) {
        // 检查管理员权限
        if (!sender.hasPermission("voicechat.admin") && !sender.isOp()) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use admin commands.");
            return;
        }

        if (args.length == 0) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat admin <force-join|force-leave|force-delete|global-ban|global-unban|global-kick>");
            return;
        }

        String adminCommand = args[0].toLowerCase();

        switch (adminCommand) {
            case "force-join":
                handleForceJoinCommand(sender, args);
                break;
            case "force-leave":
                handleForceLeaveCommand(sender, args);
                break;
            case "force-delete":
                handleForceDeleteCommand(sender, args);
                break;
            case "global-ban":
                handleGlobalBanCommand(sender, args);
                break;
            case "global-unban":
                handleGlobalUnbanCommand(sender, args);
                break;
            case "global-kick":
                handleGlobalKickCommand(sender, args);
                break;
            case "start-broadcast":
                handleStartBroadcastCommand(sender, args);
                break;
            case "stop-broadcast":
                handleStopBroadcastCommand(sender, args);
                break;
            case "authorize-broadcaster":
                handleAuthorizeBroadcasterCommand(sender, args);
                break;
            case "unauthorize-broadcaster":
                handleUnauthorizeBroadcasterCommand(sender, args);
                break;
            case "list-broadcasts":
                handleListBroadcastsCommand(sender, args);
                break;
            case "grant-spectator-voice":
                handleGrantSpectatorVoiceCommand(sender, args);
                break;
            case "revoke-spectator-voice":
                handleRevokeSpectatorVoiceCommand(sender, args);
                break;
            default:
                sender.sendMessage(ChatColor.RED + "Unknown admin command: " + adminCommand);
                sender.sendMessage(ChatColor.YELLOW + "Available: force-join, force-leave, force-delete, global-ban, global-unban, global-kick, start-broadcast, stop-broadcast, authorize-broadcaster, unauthorize-broadcaster, list-broadcasts, grant-spectator-voice, revoke-spectator-voice");
                break;
        }
    }

    /**
     * 强制玩家加入群组
     */
    private void handleForceJoinCommand(CommandSender sender, String[] args) {
        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat admin force-join <player> <group>");
            return;
        }

        String playerName = args[1];
        String groupName = args[2];

        Player targetPlayer = plugin.getServer().getPlayer(playerName);
        if (targetPlayer == null) {
            sender.sendMessage(ChatColor.RED + "Player not found: " + playerName);
            return;
        }

        try {
            // 查找群组ID
            UUID groupId = findGroupByName(groupName);
            if (groupId == null) {
                sender.sendMessage(ChatColor.RED + "Group not found: " + groupName);
                return;
            }

            UUID adminUuid = sender instanceof Player ? ((Player) sender).getUniqueId() : UUID.randomUUID();
            boolean success = voiceServerClient.forceJoinGroup(groupId, targetPlayer.getUniqueId(), adminUuid);

            if (success) {
                sender.sendMessage(ChatColor.GREEN + "Successfully forced " + playerName + " to join group " + groupName);
                targetPlayer.sendMessage(ChatColor.YELLOW + "You have been added to group " + groupName + " by an administrator.");
            } else {
                sender.sendMessage(ChatColor.RED + "Failed to force join group. Check server logs for details.");
            }
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Error: " + e.getMessage());
            LOGGER.error("Failed to force join group", e);
        }
    }

    /**
     * 强制玩家离开群组
     */
    private void handleForceLeaveCommand(CommandSender sender, String[] args) {
        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat admin force-leave <player> <group>");
            return;
        }

        String playerName = args[1];
        String groupName = args[2];

        Player targetPlayer = plugin.getServer().getPlayer(playerName);
        if (targetPlayer == null) {
            sender.sendMessage(ChatColor.RED + "Player not found: " + playerName);
            return;
        }

        try {
            UUID groupId = findGroupByName(groupName);
            if (groupId == null) {
                sender.sendMessage(ChatColor.RED + "Group not found: " + groupName);
                return;
            }

            UUID adminUuid = sender instanceof Player ? ((Player) sender).getUniqueId() : UUID.randomUUID();
            boolean success = voiceServerClient.forceLeaveGroup(groupId, targetPlayer.getUniqueId(), adminUuid);

            if (success) {
                sender.sendMessage(ChatColor.GREEN + "Successfully forced " + playerName + " to leave group " + groupName);
                targetPlayer.sendMessage(ChatColor.YELLOW + "You have been removed from group " + groupName + " by an administrator.");
            } else {
                sender.sendMessage(ChatColor.RED + "Failed to force leave group. Check server logs for details.");
            }
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Error: " + e.getMessage());
            LOGGER.error("Failed to force leave group", e);
        }
    }

    /**
     * 强制删除群组
     */
    private void handleForceDeleteCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat admin force-delete <group>");
            return;
        }

        String groupName = args[1];

        try {
            UUID groupId = findGroupByName(groupName);
            if (groupId == null) {
                sender.sendMessage(ChatColor.RED + "Group not found: " + groupName);
                return;
            }

            UUID adminUuid = sender instanceof Player ? ((Player) sender).getUniqueId() : UUID.randomUUID();
            boolean success = voiceServerClient.forceDeleteGroup(groupId, adminUuid);

            if (success) {
                sender.sendMessage(ChatColor.GREEN + "Successfully force deleted group " + groupName);
            } else {
                sender.sendMessage(ChatColor.RED + "Failed to force delete group. Check server logs for details.");
            }
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Error: " + e.getMessage());
            LOGGER.error("Failed to force delete group", e);
        }
    }

    /**
     * 全局封禁玩家
     */
    private void handleGlobalBanCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat admin global-ban <player> [reason]");
            return;
        }

        String playerName = args[1];
        String reason = args.length > 2 ? String.join(" ", Arrays.copyOfRange(args, 2, args.length)) : null;

        Player targetPlayer = plugin.getServer().getPlayer(playerName);
        UUID targetUuid;

        if (targetPlayer != null) {
            targetUuid = targetPlayer.getUniqueId();
        } else {
            // 尝试从离线玩家获取UUID
            OfflinePlayer offlinePlayer = plugin.getServer().getOfflinePlayer(playerName);
            if (offlinePlayer.hasPlayedBefore()) {
                targetUuid = offlinePlayer.getUniqueId();
            } else {
                sender.sendMessage(ChatColor.RED + "Player not found: " + playerName);
                return;
            }
        }

        try {
            UUID adminUuid = sender instanceof Player ? ((Player) sender).getUniqueId() : UUID.randomUUID();
            boolean success = voiceServerClient.globalBanPlayer(targetUuid, adminUuid, reason);

            if (success) {
                sender.sendMessage(ChatColor.GREEN + "Successfully globally banned " + playerName + " from all groups.");
                if (targetPlayer != null) {
                    targetPlayer.sendMessage(ChatColor.RED + "You have been globally banned from all voice chat groups by an administrator.");
                    if (reason != null) {
                        targetPlayer.sendMessage(ChatColor.RED + "Reason: " + reason);
                    }
                }
            } else {
                sender.sendMessage(ChatColor.RED + "Failed to globally ban player. Check server logs for details.");
            }
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Error: " + e.getMessage());
            LOGGER.error("Failed to globally ban player", e);
        }
    }

    /**
     * 解除全局封禁
     */
    private void handleGlobalUnbanCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat admin global-unban <player>");
            return;
        }

        String playerName = args[1];

        Player targetPlayer = plugin.getServer().getPlayer(playerName);
        UUID targetUuid;

        if (targetPlayer != null) {
            targetUuid = targetPlayer.getUniqueId();
        } else {
            OfflinePlayer offlinePlayer = plugin.getServer().getOfflinePlayer(playerName);
            if (offlinePlayer.hasPlayedBefore()) {
                targetUuid = offlinePlayer.getUniqueId();
            } else {
                sender.sendMessage(ChatColor.RED + "Player not found: " + playerName);
                return;
            }
        }

        try {
            UUID adminUuid = sender instanceof Player ? ((Player) sender).getUniqueId() : UUID.randomUUID();
            boolean success = voiceServerClient.globalUnbanPlayer(targetUuid, adminUuid);

            if (success) {
                sender.sendMessage(ChatColor.GREEN + "Successfully removed global ban for " + playerName);
                if (targetPlayer != null) {
                    targetPlayer.sendMessage(ChatColor.GREEN + "Your global voice chat ban has been removed by an administrator.");
                }
            } else {
                sender.sendMessage(ChatColor.RED + "Failed to remove global ban. Check server logs for details.");
            }
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Error: " + e.getMessage());
            LOGGER.error("Failed to remove global ban", e);
        }
    }

    /**
     * 全局踢出玩家
     */
    private void handleGlobalKickCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat admin global-kick <player> [reason]");
            return;
        }

        String playerName = args[1];
        String reason = args.length > 2 ? String.join(" ", Arrays.copyOfRange(args, 2, args.length)) : null;

        Player targetPlayer = plugin.getServer().getPlayer(playerName);
        if (targetPlayer == null) {
            sender.sendMessage(ChatColor.RED + "Player not found or not online: " + playerName);
            return;
        }

        try {
            UUID adminUuid = sender instanceof Player ? ((Player) sender).getUniqueId() : UUID.randomUUID();
            boolean success = voiceServerClient.globalKickPlayer(targetPlayer.getUniqueId(), adminUuid, reason);

            if (success) {
                sender.sendMessage(ChatColor.GREEN + "Successfully kicked " + playerName + " from all groups.");
                targetPlayer.sendMessage(ChatColor.YELLOW + "You have been kicked from all voice chat groups by an administrator.");
                if (reason != null) {
                    targetPlayer.sendMessage(ChatColor.YELLOW + "Reason: " + reason);
                }
            } else {
                sender.sendMessage(ChatColor.RED + "Failed to globally kick player. Check server logs for details.");
            }
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Error: " + e.getMessage());
            LOGGER.error("Failed to globally kick player", e);
        }
    }

    /**
     * 根据群组名称查找群组ID
     */
    private UUID findGroupByName(String groupName) {
        try {
            List<Map<String, Object>> groups = voiceServerClient.getAllGroups();
            for (Map<String, Object> group : groups) {
                String name = (String) group.get("name");
                if (groupName.equalsIgnoreCase(name)) {
                    String idStr = (String) group.get("id");
                    return UUID.fromString(idStr);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Failed to find group by name: " + groupName, e);
        }
        return null;
    }

    /**
     * 开始语音广播
     */
    private void handleStartBroadcastCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players");
            return;
        }

        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat admin start-broadcast <name>");
            return;
        }

        Player player = (Player) sender;
        String broadcastName = String.join(" ", Arrays.copyOfRange(args, 1, args.length));

        VoiceBroadcastManager broadcastManager = plugin.getVoiceBroadcastManager();
        if (broadcastManager == null) {
            sender.sendMessage(ChatColor.RED + "Voice broadcast manager not available");
            return;
        }

        boolean success = broadcastManager.startBroadcast(player, broadcastName);
        if (success) {
            sender.sendMessage(ChatColor.GREEN + "Started voice broadcast: " + ChatColor.GOLD + broadcastName);
            sender.sendMessage(ChatColor.YELLOW + "All players on the server can now hear your voice!");
            sender.sendMessage(ChatColor.GRAY + "Use '/voicechat admin stop-broadcast' to stop broadcasting.");
        } else {
            sender.sendMessage(ChatColor.RED + "Failed to start broadcast. You may already be broadcasting or lack permission.");
        }
    }

    /**
     * 停止语音广播
     */
    private void handleStopBroadcastCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players");
            return;
        }

        Player player = (Player) sender;
        VoiceBroadcastManager broadcastManager = plugin.getVoiceBroadcastManager();
        if (broadcastManager == null) {
            sender.sendMessage(ChatColor.RED + "Voice broadcast manager not available");
            return;
        }

        boolean success = broadcastManager.stopBroadcast(player);
        if (success) {
            sender.sendMessage(ChatColor.GREEN + "Stopped voice broadcast.");
        } else {
            sender.sendMessage(ChatColor.RED + "You are not currently broadcasting.");
        }
    }

    /**
     * 授权玩家进行广播
     */
    private void handleAuthorizeBroadcasterCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat admin authorize-broadcaster <player>");
            return;
        }

        String playerName = args[1];
        Player targetPlayer = plugin.getServer().getPlayer(playerName);
        UUID targetUuid;

        if (targetPlayer != null) {
            targetUuid = targetPlayer.getUniqueId();
        } else {
            OfflinePlayer offlinePlayer = plugin.getServer().getOfflinePlayer(playerName);
            if (offlinePlayer.hasPlayedBefore()) {
                targetUuid = offlinePlayer.getUniqueId();
            } else {
                sender.sendMessage(ChatColor.RED + "Player not found: " + playerName);
                return;
            }
        }

        VoiceBroadcastManager broadcastManager = plugin.getVoiceBroadcastManager();
        if (broadcastManager == null) {
            sender.sendMessage(ChatColor.RED + "Voice broadcast manager not available");
            return;
        }

        broadcastManager.authorizeBroadcaster(targetUuid);
        sender.sendMessage(ChatColor.GREEN + "Authorized " + playerName + " for voice broadcasting.");

        if (targetPlayer != null) {
            targetPlayer.sendMessage(ChatColor.GREEN + "You have been authorized to use voice broadcasting!");
            targetPlayer.sendMessage(ChatColor.YELLOW + "Use '/voicechat admin start-broadcast <name>' to start broadcasting.");
        }
    }

    /**
     * 取消玩家的广播授权
     */
    private void handleUnauthorizeBroadcasterCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat admin unauthorize-broadcaster <player>");
            return;
        }

        String playerName = args[1];
        Player targetPlayer = plugin.getServer().getPlayer(playerName);
        UUID targetUuid;

        if (targetPlayer != null) {
            targetUuid = targetPlayer.getUniqueId();
        } else {
            OfflinePlayer offlinePlayer = plugin.getServer().getOfflinePlayer(playerName);
            if (offlinePlayer.hasPlayedBefore()) {
                targetUuid = offlinePlayer.getUniqueId();
            } else {
                sender.sendMessage(ChatColor.RED + "Player not found: " + playerName);
                return;
            }
        }

        VoiceBroadcastManager broadcastManager = plugin.getVoiceBroadcastManager();
        if (broadcastManager == null) {
            sender.sendMessage(ChatColor.RED + "Voice broadcast manager not available");
            return;
        }

        broadcastManager.unauthorizeBroadcaster(targetUuid);
        sender.sendMessage(ChatColor.GREEN + "Removed broadcast authorization for " + playerName);

        if (targetPlayer != null) {
            targetPlayer.sendMessage(ChatColor.YELLOW + "Your voice broadcasting authorization has been removed.");
        }
    }

    /**
     * 列出活跃的广播和授权的广播员
     */
    private void handleListBroadcastsCommand(CommandSender sender, String[] args) {
        VoiceBroadcastManager broadcastManager = plugin.getVoiceBroadcastManager();
        if (broadcastManager == null) {
            sender.sendMessage(ChatColor.RED + "Voice broadcast manager not available");
            return;
        }

        Map<UUID, VoiceBroadcastManager.BroadcastSession> activeBroadcasts = broadcastManager.getActiveBroadcasts();
        Set<UUID> authorizedBroadcasters = broadcastManager.getAuthorizedBroadcasters();

        sender.sendMessage(ChatColor.GOLD + "=== Voice Broadcast Status ===");

        // 显示活跃的广播
        if (activeBroadcasts.isEmpty()) {
            sender.sendMessage(ChatColor.YELLOW + "No active broadcasts");
        } else {
            sender.sendMessage(ChatColor.GREEN + "Active Broadcasts:");
            for (Map.Entry<UUID, VoiceBroadcastManager.BroadcastSession> entry : activeBroadcasts.entrySet()) {
                VoiceBroadcastManager.BroadcastSession session = entry.getValue();
                Player broadcaster = plugin.getServer().getPlayer(session.getBroadcasterId());
                String broadcasterName = broadcaster != null ? broadcaster.getName() : "Unknown";

                long durationSeconds = session.getDuration() / 1000;
                sender.sendMessage(ChatColor.YELLOW + "• " + ChatColor.GOLD + session.getBroadcastName());
                sender.sendMessage(ChatColor.GRAY + "  Broadcaster: " + broadcasterName);
                sender.sendMessage(ChatColor.GRAY + "  Duration: " + durationSeconds + " seconds");
            }
        }

        sender.sendMessage("");

        // 显示授权的广播员
        if (authorizedBroadcasters.isEmpty()) {
            sender.sendMessage(ChatColor.YELLOW + "No authorized broadcasters");
        } else {
            sender.sendMessage(ChatColor.GREEN + "Authorized Broadcasters:");
            for (UUID uuid : authorizedBroadcasters) {
                Player player = plugin.getServer().getPlayer(uuid);
                String playerName = player != null ? player.getName() : "Offline Player";
                boolean isOnline = player != null && player.isOnline();

                sender.sendMessage(ChatColor.YELLOW + "• " + playerName +
                                 (isOnline ? ChatColor.GREEN + " (Online)" : ChatColor.GRAY + " (Offline)"));
            }
        }
    }

    /**
     * 授予观察者语音权限
     */
    private void handleGrantSpectatorVoiceCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat admin grant-spectator-voice <player>");
            return;
        }

        String playerName = args[1];
        Player targetPlayer = plugin.getServer().getPlayer(playerName);
        UUID targetUuid;

        if (targetPlayer != null) {
            targetUuid = targetPlayer.getUniqueId();
        } else {
            // 尝试从离线玩家获取UUID
            try {
                targetUuid = plugin.getServer().getOfflinePlayer(playerName).getUniqueId();
            } catch (Exception e) {
                sender.sendMessage(ChatColor.RED + "Player not found: " + playerName);
                return;
            }
        }

        // 异步调用语音服务器API
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    voiceServerClient.grantSpectatorVoicePermission(targetUuid);

                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.GREEN + "Granted spectator voice permission to " + playerName);
                            if (targetPlayer != null) {
                                targetPlayer.sendMessage(ChatColor.GREEN + "You have been granted spectator voice interaction permission!");
                            }
                        }
                    }.runTask(plugin);

                } catch (Exception e) {
                    LOGGER.error("Failed to grant spectator voice permission", e);
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.RED + "Failed to grant spectator voice permission: " + e.getMessage());
                        }
                    }.runTask(plugin);
                }
            }
        }.runTaskAsynchronously(plugin);
    }

    /**
     * 撤销观察者语音权限
     */
    private void handleRevokeSpectatorVoiceCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /voicechat admin revoke-spectator-voice <player>");
            return;
        }

        String playerName = args[1];
        Player targetPlayer = plugin.getServer().getPlayer(playerName);
        UUID targetUuid;

        if (targetPlayer != null) {
            targetUuid = targetPlayer.getUniqueId();
        } else {
            // 尝试从离线玩家获取UUID
            try {
                targetUuid = plugin.getServer().getOfflinePlayer(playerName).getUniqueId();
            } catch (Exception e) {
                sender.sendMessage(ChatColor.RED + "Player not found: " + playerName);
                return;
            }
        }

        // 异步调用语音服务器API
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    voiceServerClient.revokeSpectatorVoicePermission(targetUuid);

                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.GREEN + "Revoked spectator voice permission from " + playerName);
                            if (targetPlayer != null) {
                                targetPlayer.sendMessage(ChatColor.YELLOW + "Your spectator voice interaction permission has been revoked.");
                            }
                        }
                    }.runTask(plugin);

                } catch (Exception e) {
                    LOGGER.error("Failed to revoke spectator voice permission", e);
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.sendMessage(ChatColor.RED + "Failed to revoke spectator voice permission: " + e.getMessage());
                        }
                    }.runTask(plugin);
                }
            }
        }.runTaskAsynchronously(plugin);
    }
}
