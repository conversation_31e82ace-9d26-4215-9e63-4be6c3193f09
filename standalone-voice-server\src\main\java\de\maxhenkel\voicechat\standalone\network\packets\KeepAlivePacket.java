package de.maxhenkel.voicechat.standalone.network.packets;

import de.maxhenkel.voicechat.standalone.network.Packet;
import io.netty.buffer.ByteBuf;

/**
 * 心跳保活数据包
 */
public class KeepAlivePacket extends Packet<KeepAlivePacket> {
    
    public static final byte PACKET_ID = (byte) 0x8;
    
    private long timestamp;
    
    public KeepAlivePacket() {}
    
    public KeepAlivePacket(long timestamp) {
        this.timestamp = timestamp;
    }
    
    @Override
    public void toBytes(ByteBuf buf) {
        // KeepAlivePacket不包含任何数据（与原项目一致）
    }

    @Override
    public void fromBytes(ByteBuf buf) {
        // KeepAlivePacket不包含任何数据（与原项目一致）
        // timestamp在服务器端生成，不从客户端读取
    }
    
    @Override
    public byte getPacketId() {
        return PACKET_ID;
    }
    
    @Override
    public Class<KeepAlivePacket> getPacketClass() {
        return KeepAlivePacket.class;
    }
    
    // Getters
    public long getTimestamp() { return timestamp; }
}
