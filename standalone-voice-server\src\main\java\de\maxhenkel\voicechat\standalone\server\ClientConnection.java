package de.maxhenkel.voicechat.standalone.server;

import java.net.SocketAddress;
import java.util.UUID;

/**
 * 客户端连接信息
 */
public class ClientConnection {
    
    private final UUID playerUuid;
    private final SocketAddress address;
    private long lastKeepAlive;
    private long connectionTime;
    
    public ClientConnection(UUID playerUuid, SocketAddress address) {
        this.playerUuid = playerUuid;
        this.address = address;
        this.lastKeepAlive = System.currentTimeMillis();
        this.connectionTime = System.currentTimeMillis();
    }
    
    /**
     * 更新心跳时间
     */
    public void updateKeepAlive() {
        this.lastKeepAlive = System.currentTimeMillis();
    }
    
    /**
     * 检查连接是否超时
     */
    public boolean isTimedOut(long timeoutMs) {
        return System.currentTimeMillis() - lastKeepAlive > timeoutMs;
    }
    
    /**
     * 获取连接持续时间
     */
    public long getConnectionDuration() {
        return System.currentTimeMillis() - connectionTime;
    }
    
    // Getters
    public UUID getPlayerUuid() { return playerUuid; }
    public SocketAddress getAddress() { return address; }
    public long getLastKeepAlive() { return lastKeepAlive; }
    public long getConnectionTime() { return connectionTime; }
    
    @Override
    public String toString() {
        return "ClientConnection{" +
                "playerUuid=" + playerUuid +
                ", address=" + address +
                ", lastKeepAlive=" + lastKeepAlive +
                '}';
    }
}
