# 独立语音服务器 Dockerfile

FROM openjdk:17-jre-slim

# 设置工作目录
WORKDIR /app

# 安装必要的工具
RUN apt-get update && \
    apt-get install -y curl && \
    rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN groupadd -r voiceserver && useradd -r -g voiceserver voiceserver

# 创建必要的目录
RUN mkdir -p /app/data /app/logs && \
    chown -R voiceserver:voiceserver /app

# 复制JAR文件
COPY build/libs/standalone-voice-server-1.0.0.jar /app/voice-server.jar

# 复制配置文件
COPY voice-server.yml /app/voice-server.yml
COPY src/main/resources/logback.xml /app/logback.xml

# 设置权限
RUN chown voiceserver:voiceserver /app/voice-server.jar /app/voice-server.yml /app/logback.xml

# 切换到应用用户
USER voiceserver

# 暴露端口
EXPOSE 24454/udp 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动命令
CMD ["java", "-jar", "/app/voice-server.jar", "/app/voice-server.yml"]
