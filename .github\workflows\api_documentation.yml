name: Build API Documentation
on:
  workflow_dispatch

jobs:
  build-api-documentation:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '21'
      - name: Build Javadocs
        uses: gradle/gradle-build-action@v2
        with:
          arguments: api:javadoc
      - name: Deploy to GitHub Pages
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          branch: api-documentation
          folder: api/build/docs/javadoc
          clean: true
          clean-exclude: '["CNAME"]'
          single-commit: true
