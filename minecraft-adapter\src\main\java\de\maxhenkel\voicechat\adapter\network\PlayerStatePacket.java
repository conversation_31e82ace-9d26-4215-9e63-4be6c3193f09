package de.maxhenkel.voicechat.adapter.network;

import de.maxhenkel.voicechat.adapter.model.PlayerState;
import de.maxhenkel.voicechat.adapter.util.FriendlyByteBuf;
import java.io.IOException;

/**
 * 单个玩家状态数据包
 * 用于向客户端发送单个玩家的状态更新
 */
public class PlayerStatePacket {
    
    public static final String CHANNEL = "voicechat:player_state";
    
    private PlayerState playerState;
    
    public PlayerStatePacket() {
    }
    
    public PlayerStatePacket(PlayerState playerState) {
        this.playerState = playerState;
    }
    
    public PlayerState getPlayerState() {
        return playerState;
    }
    
    /**
     * 从字节缓冲区读取数据包
     */
    public void fromBytes(FriendlyByteBuf buf) throws IOException {
        playerState = PlayerState.fromBytes(buf);
    }
    
    /**
     * 将数据包写入字节缓冲区
     */
    public void toBytes(FriendlyByteBuf buf) throws IOException {
        playerState.toBytes(buf);
    }
    
    /**
     * 转换为字节数组（用于插件消息）
     */
    public byte[] toByteArray() throws IOException {
        try (FriendlyByteBuf buf = new FriendlyByteBuf()) {
            toBytes(buf);
            return buf.toByteArray();
        }
    }
    
    /**
     * 从字节数组创建数据包（用于插件消息）
     */
    public static PlayerStatePacket fromByteArray(byte[] data) throws IOException {
        try (FriendlyByteBuf buf = new FriendlyByteBuf(data)) {
            PlayerStatePacket packet = new PlayerStatePacket();
            packet.fromBytes(buf);
            return packet;
        }
    }
}
