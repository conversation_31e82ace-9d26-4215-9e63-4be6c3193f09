package de.maxhenkel.voicechat.standalone.api;

import de.maxhenkel.voicechat.standalone.server.VoiceServer;
import de.maxhenkel.voicechat.standalone.server.WorldManager;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 世界管理API处理器
 */
public class WorldApiHandler {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(WorldApiHandler.class);
    
    private final VoiceServer voiceServer;
    private final WorldManager worldManager;
    
    public WorldApiHandler(VoiceServer voiceServer) {
        this.voiceServer = voiceServer;
        this.worldManager = voiceServer.getWorldManager();
    }
    
    /**
     * 获取世界配置
     * GET /api/worlds/{worldName}
     */
    public void handleGetWorldConfig(Context ctx) {
        try {
            String worldName = ctx.pathParam("worldName");
            
            WorldManager.WorldConfig config = worldManager.getWorldConfig(worldName);
            if (config == null) {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of("error", "World not found"));
                return;
            }
            
            ctx.json(worldConfigToMap(config));
            
        } catch (Exception e) {
            LOGGER.error("Error getting world config", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 更新世界配置
     * PUT /api/worlds/{worldName}
     */
    public void handleUpdateWorldConfig(Context ctx) {
        try {
            String worldName = ctx.pathParam("worldName");
            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            
            WorldManager.WorldConfig config = worldManager.getWorldConfig(worldName);
            if (config == null) {
                config = new WorldManager.WorldConfig(worldName);
            }
            
            // 更新配置
            if (requestData.containsKey("voiceEnabled")) {
                config.setVoiceEnabled((Boolean) requestData.get("voiceEnabled"));
            }
            if (requestData.containsKey("maxVoiceDistance")) {
                Number distance = (Number) requestData.get("maxVoiceDistance");
                config.setMaxVoiceDistance(distance.doubleValue());
            }
            if (requestData.containsKey("groupsEnabled")) {
                config.setGroupsEnabled((Boolean) requestData.get("groupsEnabled"));
            }
            if (requestData.containsKey("crossWorldAllowed")) {
                config.setCrossWorldAllowed((Boolean) requestData.get("crossWorldAllowed"));
            }
            if (requestData.containsKey("allowedWorlds")) {
                List<String> allowedWorlds = (List<String>) requestData.get("allowedWorlds");
                config.setAllowedWorlds(new HashSet<>(allowedWorlds));
            }
            
            worldManager.setWorldConfig(config);
            
            ctx.json(Map.of("success", true, "message", "World config updated successfully"));
            
        } catch (Exception e) {
            LOGGER.error("Error updating world config", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 获取所有世界配置
     * GET /api/worlds
     */
    public void handleGetAllWorldConfigs(Context ctx) {
        try {
            Map<String, Object> response = worldManager.getStatistics();
            ctx.json(response);
            
        } catch (Exception e) {
            LOGGER.error("Error getting world configs", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 获取服务器配置
     * GET /api/servers/{serverName}
     */
    public void handleGetServerConfig(Context ctx) {
        try {
            String serverName = ctx.pathParam("serverName");
            
            WorldManager.ServerConfig config = worldManager.getServerConfig(serverName);
            if (config == null) {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of("error", "Server not found"));
                return;
            }
            
            ctx.json(serverConfigToMap(config));
            
        } catch (Exception e) {
            LOGGER.error("Error getting server config", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 更新服务器配置
     * PUT /api/servers/{serverName}
     */
    public void handleUpdateServerConfig(Context ctx) {
        try {
            String serverName = ctx.pathParam("serverName");
            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            
            WorldManager.ServerConfig config = worldManager.getServerConfig(serverName);
            if (config == null) {
                config = new WorldManager.ServerConfig(serverName);
            }
            
            // 更新配置
            if (requestData.containsKey("voiceEnabled")) {
                config.setVoiceEnabled((Boolean) requestData.get("voiceEnabled"));
            }
            if (requestData.containsKey("crossServerAllowed")) {
                config.setCrossServerAllowed((Boolean) requestData.get("crossServerAllowed"));
            }
            if (requestData.containsKey("allowedServers")) {
                List<String> allowedServers = (List<String>) requestData.get("allowedServers");
                config.setAllowedServers(new HashSet<>(allowedServers));
            }
            
            worldManager.setServerConfig(config);
            
            ctx.json(Map.of("success", true, "message", "Server config updated successfully"));
            
        } catch (Exception e) {
            LOGGER.error("Error updating server config", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 添加跨世界规则
     * POST /api/worlds/cross-world-rules
     */
    public void handleAddCrossWorldRule(Context ctx) {
        try {
            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            String fromWorld = (String) requestData.get("fromWorld");
            String toWorld = (String) requestData.get("toWorld");
            
            if (fromWorld == null || toWorld == null) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "fromWorld and toWorld are required"));
                return;
            }
            
            worldManager.addCrossWorldRule(fromWorld, toWorld);
            
            ctx.json(Map.of("success", true, "message", "Cross-world rule added successfully"));
            
        } catch (Exception e) {
            LOGGER.error("Error adding cross-world rule", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 移除跨世界规则
     * DELETE /api/worlds/cross-world-rules
     */
    public void handleRemoveCrossWorldRule(Context ctx) {
        try {
            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            String fromWorld = (String) requestData.get("fromWorld");
            String toWorld = (String) requestData.get("toWorld");
            
            if (fromWorld == null || toWorld == null) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "fromWorld and toWorld are required"));
                return;
            }
            
            worldManager.removeCrossWorldRule(fromWorld, toWorld);
            
            ctx.json(Map.of("success", true, "message", "Cross-world rule removed successfully"));
            
        } catch (Exception e) {
            LOGGER.error("Error removing cross-world rule", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 添加跨服务器规则
     * POST /api/servers/cross-server-rules
     */
    public void handleAddCrossServerRule(Context ctx) {
        try {
            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            String fromServer = (String) requestData.get("fromServer");
            String toServer = (String) requestData.get("toServer");
            
            if (fromServer == null || toServer == null) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "fromServer and toServer are required"));
                return;
            }
            
            worldManager.addCrossServerRule(fromServer, toServer);
            
            ctx.json(Map.of("success", true, "message", "Cross-server rule added successfully"));
            
        } catch (Exception e) {
            LOGGER.error("Error adding cross-server rule", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 移除跨服务器规则
     * DELETE /api/servers/cross-server-rules
     */
    public void handleRemoveCrossServerRule(Context ctx) {
        try {
            Map<String, Object> requestData = ctx.bodyAsClass(Map.class);
            String fromServer = (String) requestData.get("fromServer");
            String toServer = (String) requestData.get("toServer");
            
            if (fromServer == null || toServer == null) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "fromServer and toServer are required"));
                return;
            }
            
            worldManager.removeCrossServerRule(fromServer, toServer);
            
            ctx.json(Map.of("success", true, "message", "Cross-server rule removed successfully"));
            
        } catch (Exception e) {
            LOGGER.error("Error removing cross-server rule", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 将世界配置转换为Map
     */
    private Map<String, Object> worldConfigToMap(WorldManager.WorldConfig config) {
        Map<String, Object> map = new HashMap<>();
        map.put("worldName", config.getWorldName());
        map.put("voiceEnabled", config.isVoiceEnabled());
        map.put("maxVoiceDistance", config.getMaxVoiceDistance());
        map.put("groupsEnabled", config.isGroupsEnabled());
        map.put("crossWorldAllowed", config.isCrossWorldAllowed());
        map.put("allowedWorlds", config.getAllowedWorlds());
        return map;
    }
    
    /**
     * 将服务器配置转换为Map
     */
    private Map<String, Object> serverConfigToMap(WorldManager.ServerConfig config) {
        Map<String, Object> map = new HashMap<>();
        map.put("serverName", config.getServerName());
        map.put("voiceEnabled", config.isVoiceEnabled());
        map.put("crossServerAllowed", config.isCrossServerAllowed());
        map.put("allowedServers", config.getAllowedServers());
        
        Map<String, Object> worldConfigs = new HashMap<>();
        for (Map.Entry<String, WorldManager.WorldConfig> entry : config.getWorldConfigs().entrySet()) {
            worldConfigs.put(entry.getKey(), worldConfigToMap(entry.getValue()));
        }
        map.put("worldConfigs", worldConfigs);
        
        return map;
    }
}
