# Standalone Voice Server Configuration for Velocity Proxy
# 
# Set to true to enable standalone voice server mode
# When enabled, all voice traffic will be directed to a single standalone voice server
# instead of proxying to individual backend servers
standalone.enabled=true

# Standalone voice server host
# This should be the IP address or hostname where your standalone voice server is running
standalone.voice_server_host=localhost

# Standalone voice server UDP port
# This should match the port configured in your standalone voice server (default: 24454)
standalone.voice_server_port=24454

# Note: When standalone mode is enabled, the voice chat proxy functionality is disabled
# and players will connect directly to the standalone voice server
#
# Configuration steps:
# 1. Copy this file to your Velocity plugins/voicechat/ directory as "standalone-voice.properties"
# 2. Set standalone.enabled=true
# 3. Configure the voice server host and port to match your standalone voice server
# 4. Restart your Velocity proxy
# 5. Make sure your backend Minecraft servers have the voice chat adapter plugin installed
