# PlayerState 网络通信测试指南

## 测试目标
验证独立服务端的 PlayerState 网络通信功能是否正常工作，确保：
1. 玩家之间的喇叭图标正常显示
2. 群组HUD中正确展示玩家信息
3. 玩家状态同步正常

## 测试环境准备

### 1. 启动独立语音服务器
```bash
cd standalone-voice-server
java -jar standalone-voice-server.jar
```

### 2. 启动Minecraft服务器（带adapter插件）
确保以下配置正确：
- `minecraft-adapter` 插件已安装
- 配置文件中指向独立语音服务器
- 客户端安装了Voice Chat模组

### 3. 客户端配置
确保客户端Voice Chat模组版本兼容

## 测试步骤

### 测试1：基本连接和状态同步
1. 玩家A加入服务器
2. 玩家B加入服务器
3. 检查两个玩家是否都能看到对方的状态

**预期结果：**
- 玩家加入时，其他玩家应该收到新玩家的状态信息
- 新玩家应该收到所有现有玩家的状态信息

### 测试2：语音状态显示
1. 玩家A开始说话
2. 检查玩家B是否能看到玩家A头上的喇叭图标

**预期结果：**
- 说话时显示喇叭图标
- 停止说话时图标消失

### 测试3：群组功能
1. 创建一个语音群组
2. 玩家A和玩家B加入同一群组
3. 检查群组HUD是否正确显示

**预期结果：**
- 群组HUD显示群组成员
- 群组成员的头像和状态正确显示
- 群组内语音通信正常

### 测试4：状态变化同步
1. 玩家A禁用语音聊天
2. 检查玩家B是否能看到玩家A的禁用状态

**预期结果：**
- 状态变化实时同步
- 禁用状态正确显示（灰色图标）

## 调试信息

### 检查日志
1. **独立服务器日志：** 查看语音数据处理
2. **Minecraft服务器日志：** 查看PlayerState同步
3. **客户端日志：** 查看接收到的状态信息

### 关键日志信息
```
[PlayerStateManager] Sent X player states to PlayerName
[PlayerStateNetworkManager] Sent PlayerState to PlayerName: PlayerState{...}
[PlayerStateManager] Broadcasted state update: PlayerState{...}
```

## 故障排除

### 问题1：喇叭图标不显示
- 检查PlayerState是否正确同步
- 确认客户端接收到状态更新
- 验证语音数据是否正确传输

### 问题2：群组HUD不显示
- 检查群组信息是否正确同步
- 确认PlayerState包含正确的群组ID
- 验证群组成员状态

### 问题3：状态同步延迟
- 检查网络延迟
- 确认插件消息通道正常工作
- 验证异步任务执行

## 验证成功标准
✅ 玩家加入时状态正确同步
✅ 语音状态实时显示（喇叭图标）
✅ 群组HUD正确显示成员信息
✅ 状态变化实时同步
✅ 无明显性能问题
