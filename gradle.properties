org.gradle.jvmargs=-Xmx16G
org.gradle.daemon=false

java_version=21
java_toolchain_version=21
voicechat_compatibility_version=18

minecraft_version=1.21.8
fabric_loader_version=0.16.13
fabric_loader_dependency=>=0.16.13

# Dependencies
configbuilder_version=2.0.2
cloth_config_version=19.0.147
concentus_version=1.0.1
opus4j_version=2.0.4
rnnoise4j_version=2.0.3
lame4j_version=2.0.4

# Mod information
mod_version=1.21.8-2.5.35
mod_id=voicechat
mod_display_name=Simple Voice Chat

# Script configuration
enable_configbuilder=true

# Project upload
curseforge_upload_minecraft_versions=1.21.8
curseforge_upload_id=416089
modrinth_upload_id=9eGKb6K1
hangar_upload_id=SimpleVoiceChat
curseforge_bukkit_upload_id=498303
modrinth_upload_include_prefix=false
upload_release_type=release
upload_recommended=true

# Gradle plugins
mod_gradle_script_version=1.0.43
fabric_loom_version=1.11-SNAPSHOT
quilt_loom_version=1.8.+
forgegradle_version=[6.0.16,6.2)
neogradle_version=7.0.+
mod_update_version=2.0.0
cursegradle_version=1.4.0
shadow_version=8.3.6
minotaur_version=2.+
mixingradle_version=0.7-SNAPSHOT
neogradle_mixin_version=7.0.+
hangar_publish_version=0.1.3
curse_gradle_uploader_version=1.5.1
maven_settings_version=0.5
paperweight_version=2.0.0-beta.17
run_task_version=2.3.1
