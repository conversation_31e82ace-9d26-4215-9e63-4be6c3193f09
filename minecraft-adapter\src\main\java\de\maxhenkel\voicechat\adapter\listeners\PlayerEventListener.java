package de.maxhenkel.voicechat.adapter.listeners;

import de.maxhenkel.voicechat.adapter.VoiceChatAdapterPlugin;
import de.maxhenkel.voicechat.adapter.managers.PlayerStateManager;
import de.maxhenkel.voicechat.adapter.model.PlayerInfo;
import de.maxhenkel.voicechat.adapter.model.Position;
import de.maxhenkel.voicechat.adapter.network.VoiceServerClient;
import de.maxhenkel.voicechat.adapter.network.SecretPacketSender;
import de.maxhenkel.voicechat.adapter.network.GroupMessageHandler;
import de.maxhenkel.voicechat.adapter.permission.PermissionSyncManager;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.*;
import org.bukkit.scheduler.BukkitRunnable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 玩家事件监听器
 */
public class PlayerEventListener implements Listener {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerEventListener.class);
    
    private final VoiceChatAdapterPlugin plugin;
    private final VoiceServerClient voiceServerClient;
    private final PlayerStateManager playerStateManager;
    private final ConcurrentHashMap<Player, Position> lastPositions = new ConcurrentHashMap<>();

    public PlayerEventListener(VoiceChatAdapterPlugin plugin, VoiceServerClient voiceServerClient) {
        this.plugin = plugin;
        this.voiceServerClient = voiceServerClient;
        this.playerStateManager = plugin.getPlayerStateManager();
        
        // 启动定时同步任务
        startSyncTasks();
    }
    
    /**
     * 玩家加入事件
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // 异步处理玩家登录
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    PlayerInfo playerInfo = createPlayerInfo(player);
                    voiceServerClient.playerLogin(playerInfo);

                    // 在PlayerStateManager中注册玩家
                    playerStateManager.onPlayerJoin(player);

                    // 同步玩家权限
                    PermissionSyncManager permissionSyncManager = plugin.getPermissionSyncManager();
                    if (permissionSyncManager != null) {
                        permissionSyncManager.syncPlayerPermissions(player);
                    }

                    // 记录初始位置
                    lastPositions.put(player, playerInfo.getPosition());

                    // 延迟同步群组，确保语音聊天连接已建立
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            GroupMessageHandler groupHandler = plugin.getGroupMessageHandler();
                            if (groupHandler != null) {
                                groupHandler.syncGroupsForPlayer(player);
                            }

                            // 发送所有玩家状态给新加入的玩家
                            playerStateManager.sendAllStatesToPlayer(player);
                        }
                    }.runTaskLaterAsynchronously(plugin, 60L); // 3秒后同步群组

                    LOGGER.debug("Player {} joined and synced to voice server", player.getName());

                } catch (Exception e) {
                    LOGGER.error("Failed to sync player join for {}", player.getName(), e);
                }
            }
        }.runTaskAsynchronously(plugin);
    }
    
    /**
     * 玩家离开事件
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        
        // 异步处理玩家登出
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    voiceServerClient.playerLogout(player.getUniqueId());

                    // 在PlayerStateManager中注销玩家
                    playerStateManager.onPlayerQuit(player);

                    // 清理权限缓存
                    PermissionSyncManager permissionSyncManager = plugin.getPermissionSyncManager();
                    if (permissionSyncManager != null) {
                        permissionSyncManager.onPlayerDisconnect(player.getUniqueId());
                    }

                    lastPositions.remove(player);
                    
                    LOGGER.debug("Player {} quit and synced to voice server", player.getName());
                    
                } catch (Exception e) {
                    LOGGER.error("Failed to sync player quit for {}", player.getName(), e);
                }
            }
        }.runTaskAsynchronously(plugin);
    }
    
    /**
     * 玩家移动事件
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerMove(PlayerMoveEvent event) {
        if (!plugin.getAdapterConfig().getSync().isSyncOnMove()) {
            return;
        }
        
        Player player = event.getPlayer();
        Position currentPosition = createPosition(player);
        Position lastPosition = lastPositions.get(player);
        
        // 检查是否移动了足够的距离
        if (lastPosition != null) {
            double distance = currentPosition.getDistanceTo(lastPosition);
            if (distance < plugin.getAdapterConfig().getSync().getMinMoveDistance()) {
                return;
            }
        }
        
        // 异步更新位置
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    voiceServerClient.updatePlayerPosition(player.getUniqueId(), currentPosition);
                    lastPositions.put(player, currentPosition);
                    
                } catch (Exception e) {
                    LOGGER.warn("Failed to sync position for {}", player.getName(), e);
                }
            }
        }.runTaskAsynchronously(plugin);
    }
    
    /**
     * 玩家游戏模式变更事件
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerGameModeChange(PlayerGameModeChangeEvent event) {
        Player player = event.getPlayer();
        int newGameMode = convertGameMode(event.getNewGameMode());

        // 异步更新游戏模式
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    voiceServerClient.updatePlayerGameMode(player.getUniqueId(), newGameMode);
                    LOGGER.debug("Updated game mode for {}: {} -> {}",
                               player.getName(), event.getPlayer().getGameMode(), event.getNewGameMode());
                } catch (Exception e) {
                    LOGGER.warn("Failed to sync game mode for {}", player.getName(), e);
                }
            }
        }.runTaskAsynchronously(plugin);
    }

    /**
     * 玩家传送事件
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerTeleport(PlayerTeleportEvent event) {
        Player player = event.getPlayer();
        
        // 异步更新位置
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    Position newPosition = new Position(
                            createWorldId(event.getTo().getWorld().getName()),
                            event.getTo().getX(),
                            event.getTo().getY(),
                            event.getTo().getZ()
                    );
                    
                    voiceServerClient.updatePlayerPosition(player.getUniqueId(), newPosition);
                    lastPositions.put(player, newPosition);
                    
                } catch (Exception e) {
                    LOGGER.warn("Failed to sync teleport for {}", player.getName(), e);
                }
            }
        }.runTaskAsynchronously(plugin);
    }
    
    /**
     * 启动定时同步任务
     */
    private void startSyncTasks() {
        // 位置同步任务
        int positionInterval = plugin.getAdapterConfig().getSync().getPositionSyncInterval();
        if (positionInterval > 0) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    syncAllPlayerPositions();
                }
            }.runTaskTimerAsynchronously(plugin, 20L, positionInterval / 50L); // 转换为tick
        }
        
        // 权限同步任务
        int permissionInterval = plugin.getAdapterConfig().getSync().getPermissionSyncInterval();
        if (permissionInterval > 0) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    syncAllPlayerPermissions();
                }
            }.runTaskTimerAsynchronously(plugin, 100L, permissionInterval / 50L); // 转换为tick
        }

        // 服务器重启检查任务（每30秒检查一次）
        new BukkitRunnable() {
            @Override
            public void run() {
                checkServerRestart();
            }
        }.runTaskTimerAsynchronously(plugin, 600L, 600L); // 30秒后开始，每30秒执行一次

        // 群组成员状态同步任务（每10秒同步一次）
        new BukkitRunnable() {
            @Override
            public void run() {
                syncGroupMemberStates();
            }
        }.runTaskTimerAsynchronously(plugin, 200L, 200L); // 10秒后开始，每10秒执行一次
    }
    
    /**
     * 同步所有玩家位置
     */
    private void syncAllPlayerPositions() {
        for (Player player : plugin.getServer().getOnlinePlayers()) {
            try {
                Position currentPosition = createPosition(player);
                Position lastPosition = lastPositions.get(player);
                
                // 检查位置是否有变化
                if (lastPosition == null || !currentPosition.equals(lastPosition)) {
                    voiceServerClient.updatePlayerPosition(player.getUniqueId(), currentPosition);
                    lastPositions.put(player, currentPosition);
                }
                
            } catch (Exception e) {
                LOGGER.warn("Failed to sync position for {}", player.getName(), e);
            }
        }
    }
    
    /**
     * 同步所有玩家权限
     */
    private void syncAllPlayerPermissions() {
        for (Player player : plugin.getServer().getOnlinePlayers()) {
            try {
                List<String> permissionList = getPlayerPermissions(player);
                Map<String, Boolean> permissions = new HashMap<>();
                for (String permission : permissionList) {
                    permissions.put(permission, true);
                }
                voiceServerClient.updatePlayerPermissions(player.getUniqueId(), permissions);

            } catch (Exception e) {
                LOGGER.warn("Failed to sync permissions for {}", player.getName(), e);
            }
        }
    }

    /**
     * 检查服务器重启并重新同步玩家
     */
    private void checkServerRestart() {
        try {
            if (voiceServerClient.checkServerRestart()) {
                LOGGER.info("Voice server restart detected, resyncing all online players");

                // 重新同步所有在线玩家
                voiceServerClient.resyncAllPlayers(plugin.getServer().getOnlinePlayers());

                // 清空本地位置缓存，强制重新同步位置
                lastPositions.clear();

                LOGGER.info("Completed resyncing after voice server restart");
            }
        } catch (Exception e) {
            LOGGER.error("Error checking server restart: {}", e.getMessage());
        }
    }
    
    /**
     * 创建玩家信息
     */
    private PlayerInfo createPlayerInfo(Player player) {
        PlayerInfo playerInfo = new PlayerInfo(player.getUniqueId(), player.getName());
        playerInfo.setPosition(createPosition(player));
        playerInfo.setPermissions(getPlayerPermissions(player));
        playerInfo.setGameMode(convertGameMode(player.getGameMode()));
        return playerInfo;
    }
    
    /**
     * 创建位置信息
     */
    private Position createPosition(Player player) {
        return new Position(
                createWorldId(player.getWorld().getName()),
                player.getLocation().getX(),
                player.getLocation().getY(),
                player.getLocation().getZ()
        );
    }

    /**
     * 创建带服务器前缀的世界ID，避免跨服务器世界名冲突
     */
    private String createWorldId(String worldName) {
        String serverName = plugin.getAdapterConfig().getServerName();
        return serverName + ":" + worldName;
    }
    
    /**
     * 获取玩家权限
     */
    private List<String> getPlayerPermissions(Player player) {
        List<String> permissions = new ArrayList<>();
        
        if (player.hasPermission("voicechat.speak")) {
            permissions.add("voicechat.speak");
        }
        if (player.hasPermission("voicechat.groups")) {
            permissions.add("voicechat.groups");
        }
        if (player.hasPermission("voicechat.admin")) {
            permissions.add("voicechat.admin");
        }
        if (player.hasPermission("voicechat.record")) {
            permissions.add("voicechat.record");
        }
        
        return permissions;
    }

    /**
     * 同步群组成员状态
     */
    private void syncGroupMemberStates() {
        try {
            // 获取所有群组及其成员信息
            java.util.List<Map<String, Object>> allGroups = voiceServerClient.getAllGroups();

            if (allGroups != null && !allGroups.isEmpty()) {
                for (Map<String, Object> groupInfo : allGroups) {
                    try {
                        UUID groupUuid = UUID.fromString((String) groupInfo.get("id"));
                        String groupName = (String) groupInfo.get("name");

                        // 获取群组成员列表
                        java.util.List<Map<String, Object>> members =
                            (java.util.List<Map<String, Object>>) groupInfo.get("members");

                        if (members != null && !members.isEmpty()) {
                            // 向群组中的每个在线成员发送群组成员更新
                            for (Map<String, Object> memberInfo : members) {
                                String memberUuidStr = (String) memberInfo.get("uuid");
                                Boolean isOnline = (Boolean) memberInfo.get("online");

                                if (isOnline != null && isOnline) {
                                    UUID memberUuid = UUID.fromString(memberUuidStr);
                                    Player member = plugin.getServer().getPlayer(memberUuid);

                                    if (member != null && member.isOnline()) {
                                        // 发送群组成员状态更新
                                        sendGroupMemberUpdate(member, groupUuid, members);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        LOGGER.warn("Failed to sync group member states: {}", e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error syncing group member states: {}", e.getMessage());
        }
    }

    /**
     * 转换Bukkit游戏模式为数字
     */
    private int convertGameMode(org.bukkit.GameMode gameMode) {
        switch (gameMode) {
            case SURVIVAL:
                return 0;
            case CREATIVE:
                return 1;
            case ADVENTURE:
                return 2;
            case SPECTATOR:
                return 3;
            default:
                return 0; // 默认生存模式
        }
    }

    /**
     * 发送群组成员更新给玩家
     */
    private void sendGroupMemberUpdate(Player player, UUID groupUuid, java.util.List<Map<String, Object>> members) {
        try {
            // 这里可以发送一个自定义的群组成员更新包
            // 暂时记录日志，表示群组成员状态已更新
            LOGGER.debug("Updated group member states for player {} in group {}: {} members",
                        player.getName(), groupUuid, members.size());
        } catch (Exception e) {
            LOGGER.warn("Failed to send group member update to {}: {}", player.getName(), e.getMessage());
        }
    }
}
