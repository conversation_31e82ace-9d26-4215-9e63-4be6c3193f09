package de.maxhenkel.voicechat.standalone.server;

import de.maxhenkel.voicechat.standalone.model.PlayerData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.SocketAddress;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 连接管理器
 */
public class ConnectionManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ConnectionManager.class);
    
    private final VoiceServer voiceServer;
    private final ConcurrentHashMap<UUID, UUID> playerSecrets = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<SocketAddress, Long> connectionAttempts = new ConcurrentHashMap<>();
    
    // 连接限制配置
    private static final int MAX_ATTEMPTS_PER_IP = 10;
    private static final long ATTEMPT_WINDOW_MS = 60000; // 1分钟
    private static final long SECRET_EXPIRY_MS = 300000; // 5分钟
    
    public ConnectionManager(VoiceServer voiceServer) {
        this.voiceServer = voiceServer;
    }
    
    /**
     * 生成玩家认证密钥
     */
    public UUID generateSecret(UUID playerUuid) {
        UUID secret = UUID.randomUUID();
        playerSecrets.put(playerUuid, secret);

        // 同时更新PlayerManager中的密钥
        if (voiceServer.getPlayerManager() != null) {
            voiceServer.getPlayerManager().setPlayerSecret(playerUuid, secret);
        }

        LOGGER.debug("Generated secret for player {}: {}", playerUuid, secret);
        return secret;
    }
    
    /**
     * 验证玩家密钥
     */
    public boolean validateSecret(UUID playerUuid, UUID secret) {
        UUID storedSecret = playerSecrets.get(playerUuid);
        if (storedSecret == null) {
            LOGGER.warn("No secret found for player {}", playerUuid);
            return false;
        }

        boolean valid = storedSecret.equals(secret);
        if (valid) {
            LOGGER.debug("Secret validated for player {}", playerUuid);
            // 不删除密钥，允许重连使用同一个密钥
        } else {
            LOGGER.warn("Invalid secret for player {}", playerUuid);
        }

        return valid;
    }
    
    /**
     * 检查连接尝试是否被限制
     */
    public boolean isConnectionAllowed(SocketAddress address) {
        long currentTime = System.currentTimeMillis();
        
        // 清理过期的连接尝试记录
        connectionAttempts.entrySet().removeIf(entry -> 
            currentTime - entry.getValue() > ATTEMPT_WINDOW_MS);
        
        // 计算该IP的连接尝试次数
        long attempts = connectionAttempts.entrySet().stream()
                .filter(entry -> entry.getKey().equals(address))
                .count();
        
        if (attempts >= MAX_ATTEMPTS_PER_IP) {
            LOGGER.warn("Connection rate limit exceeded for address: {}", address);
            return false;
        }
        
        // 记录本次连接尝试
        connectionAttempts.put(address, currentTime);
        return true;
    }
    
    /**
     * 清理过期的密钥
     */
    public void cleanupExpiredSecrets() {
        // 注意：这里简化处理，实际应该记录密钥生成时间
        // 为了演示，我们假设所有密钥都可能过期
        long currentTime = System.currentTimeMillis();
        
        // 在实际实现中，应该维护一个密钥生成时间的映射
        // 这里只是示例代码
        LOGGER.debug("Cleaning up expired secrets (placeholder implementation)");
    }
    
    /**
     * 获取连接统计信息
     */
    public Map<String, Object> getConnectionStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("activeSecrets", playerSecrets.size());
        stats.put("recentConnectionAttempts", connectionAttempts.size());
        stats.put("maxAttemptsPerIp", MAX_ATTEMPTS_PER_IP);
        stats.put("attemptWindowMs", ATTEMPT_WINDOW_MS);
        return stats;
    }
    
    /**
     * 重置IP的连接尝试计数
     */
    public void resetConnectionAttempts(SocketAddress address) {
        connectionAttempts.entrySet().removeIf(entry -> entry.getKey().equals(address));
        LOGGER.debug("Reset connection attempts for address: {}", address);
    }
    
    /**
     * 获取所有活跃的密钥
     */
    public Set<UUID> getActiveSecrets() {
        return new HashSet<>(playerSecrets.keySet());
    }
    
    /**
     * 强制移除玩家密钥
     */
    public void removeSecret(UUID playerUuid) {
        UUID removed = playerSecrets.remove(playerUuid);
        if (removed != null) {
            LOGGER.debug("Removed secret for player {}", playerUuid);
        }
    }
    
    /**
     * 检查玩家是否有有效密钥
     */
    public boolean hasValidSecret(UUID playerUuid) {
        return playerSecrets.containsKey(playerUuid);
    }

    /**
     * 检查玩家是否连接到语音聊天
     */
    public boolean isPlayerConnected(UUID playerUuid) {
        // 通过VoiceServer的connections映射检查连接状态
        return voiceServer.getConnections().containsKey(playerUuid);
    }

    /**
     * 清理离线玩家的密钥
     */
    public void cleanupOfflinePlayerSecrets() {
        playerSecrets.entrySet().removeIf(entry -> {
            UUID playerUuid = entry.getKey();
            PlayerData player = voiceServer.getPlayerManager().getPlayer(playerUuid);

            // 如果玩家不存在或已离线，清理其密钥
            if (player == null || !player.isOnline()) {
                LOGGER.debug("Cleaning up secret for offline player: {}", playerUuid);
                return true;
            }
            return false;
        });
    }
}
