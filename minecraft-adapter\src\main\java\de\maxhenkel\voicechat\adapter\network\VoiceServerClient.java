package de.maxhenkel.voicechat.adapter.network;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.maxhenkel.voicechat.adapter.config.AdapterConfig;
import de.maxhenkel.voicechat.adapter.model.PlayerInfo;
import de.maxhenkel.voicechat.adapter.model.Position;
import okhttp3.*;
import org.bukkit.entity.Player;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 语音服务器HTTP客户端
 */
public class VoiceServerClient {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(VoiceServerClient.class);
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    
    private final AdapterConfig config;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final String baseUrl;
    private final String authHeader;
    private volatile boolean connected = false;
    private volatile long serverStartTime = 0; // 服务器启动时间，用于检测重启
    
    public VoiceServerClient(AdapterConfig config) {
        this.config = config;
        this.baseUrl = config.getVoiceServer().getApiEndpoint();
        this.authHeader = "Bearer " + config.getVoiceServer().getAuthToken();
        this.objectMapper = new ObjectMapper();
        
        // 配置HTTP客户端
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }
    
    /**
     * 初始化客户端
     */
    public void initialize() throws Exception {
        // 测试连接
        if (!testConnection()) {
            throw new Exception("Failed to connect to voice server");
        }
        connected = true;
        LOGGER.info("Voice server client initialized successfully");
    }
    
    /**
     * 检查是否已连接
     */
    public boolean isConnected() {
        return connected;
    }

    /**
     * 检查服务器是否重启并返回是否需要重新同步
     */
    public boolean checkServerRestart() {
        try {
            long oldStartTime = serverStartTime;
            testConnectionAndCheckRestart();
            return oldStartTime > 0 && serverStartTime > oldStartTime;
        } catch (Exception e) {
            LOGGER.warn("Failed to check server restart: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取所有群组列表
     */
    public java.util.List<Map<String, Object>> getAllGroups() {
        try {
            String url = config.getVoiceServer().getApiEndpoint() + "/api/groups";
            LOGGER.debug("Requesting all groups from URL: {}", url);

            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                    .header("Content-Type", "application/json")
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                LOGGER.debug("Response code: {}, successful: {}", response.code(), response.isSuccessful());

                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    LOGGER.debug("Response body: {}", responseBody);

                    if (responseBody.trim().isEmpty()) {
                        LOGGER.warn("Empty response body from voice server");
                        return new java.util.ArrayList<>();
                    }

                    java.util.List<Map<String, Object>> groups = objectMapper.readValue(responseBody, java.util.List.class);
                    LOGGER.debug("Parsed {} groups from response", groups.size());
                    return groups;
                } else {
                    String responseBody = response.body() != null ? response.body().string() : "No response body";
                    LOGGER.warn("Failed to get all groups: HTTP {} - {}", response.code(), responseBody);
                    return new java.util.ArrayList<>(); // 返回空列表而不是null
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error getting all groups: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取群组详细信息
     */
    public Map<String, Object> getGroupInfo(UUID groupUuid) {
        try {
            String url = config.getVoiceServer().getApiEndpoint() + "/api/groups/" + groupUuid.toString();

            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                    .header("Content-Type", "application/json")
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    return objectMapper.readValue(responseBody, Map.class);
                } else {
                    LOGGER.warn("Failed to get group info {}: HTTP {}", groupUuid, response.code());
                    return null;
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error getting group info {}: {}", groupUuid, e.getMessage());
            return null;
        }
    }

    /**
     * 重新同步所有在线玩家（用于服务器重启后）
     */
    public void resyncAllPlayers(java.util.Collection<? extends org.bukkit.entity.Player> onlinePlayers) {
        LOGGER.info("Resyncing {} online players to voice server", onlinePlayers.size());

        for (org.bukkit.entity.Player player : onlinePlayers) {
            try {
                // 重新发送玩家登录信息
                de.maxhenkel.voicechat.adapter.model.PlayerInfo playerInfo = createPlayerInfo(player);
                playerLogin(playerInfo);

                LOGGER.debug("Resynced player {} to voice server", player.getName());

                // 添加小延迟避免过快发送
                Thread.sleep(50);

            } catch (Exception e) {
                LOGGER.error("Failed to resync player {}: {}", player.getName(), e.getMessage());
            }
        }

        LOGGER.info("Completed resyncing all players to voice server");
    }

    /**
     * 创建玩家信息（从Player对象）
     */
    private de.maxhenkel.voicechat.adapter.model.PlayerInfo createPlayerInfo(org.bukkit.entity.Player player) {
        de.maxhenkel.voicechat.adapter.model.PlayerInfo playerInfo =
            new de.maxhenkel.voicechat.adapter.model.PlayerInfo(player.getUniqueId(), player.getName());

        // 设置位置
        de.maxhenkel.voicechat.adapter.model.Position position =
            new de.maxhenkel.voicechat.adapter.model.Position(
                createWorldId(player.getWorld().getName()),
                player.getLocation().getX(),
                player.getLocation().getY(),
                player.getLocation().getZ()
            );
        playerInfo.setPosition(position);

        // 设置权限
        java.util.List<String> permissions = new java.util.ArrayList<>();
        if (player.hasPermission("voicechat.speak")) {
            permissions.add("voicechat.speak");
        }
        if (player.hasPermission("voicechat.groups")) {
            permissions.add("voicechat.groups");
        }
        if (player.hasPermission("voicechat.admin")) {
            permissions.add("voicechat.admin");
        }
        if (player.hasPermission("voicechat.record")) {
            permissions.add("voicechat.record");
        }
        playerInfo.setPermissions(permissions);

        return playerInfo;
    }

    /**
     * 获取玩家连接状态
     */
    public Map<UUID, Boolean> getPlayerConnectionStates() throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/players/connections")
                .header("Authorization", authHeader)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to get player connections: " + response.code());
            }

            String responseBody = response.body().string();
            @SuppressWarnings("unchecked")
            Map<String, Boolean> rawMap = objectMapper.readValue(responseBody, Map.class);

            Map<UUID, Boolean> connectionStates = new HashMap<>();
            for (Map.Entry<String, Boolean> entry : rawMap.entrySet()) {
                try {
                    UUID playerUuid = UUID.fromString(entry.getKey());
                    connectionStates.put(playerUuid, entry.getValue());
                } catch (IllegalArgumentException e) {
                    LOGGER.warn("Invalid UUID in connection states: {}", entry.getKey());
                }
            }

            return connectionStates;
        }
    }

    /**
     * 测试与独立服务端的连接
     */
    public boolean testConnection() {
        try {
            Request request = new Request.Builder()
                    .url(baseUrl + "/health")
                    .header("Authorization", authHeader)
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                boolean isHealthy = response.isSuccessful();
                if (isHealthy != connected) {
                    connected = isHealthy;
                    if (isHealthy) {
                        LOGGER.info("Connection to voice server restored");
                    } else {
                        LOGGER.warn("Connection to voice server lost");
                    }
                }
                return isHealthy;
            }
        } catch (Exception e) {
            if (connected) {
                LOGGER.warn("Connection test failed: {}", e.getMessage());
                connected = false;
            }
            return false;
        }
    }

    /**
     * 安全执行HTTP请求，带有异常处理和重试
     */
    private <T> T executeWithRetry(String operation, HttpRequestExecutor<T> executor) throws Exception {
        Exception lastException = null;

        for (int attempt = 1; attempt <= 3; attempt++) {
            try {
                return executor.execute();
            } catch (Exception e) {
                lastException = e;
                LOGGER.warn("{} failed (attempt {}/3): {}", operation, attempt, e.getMessage());

                if (attempt < 3) {
                    try {
                        Thread.sleep(1000 * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new Exception("Operation interrupted", ie);
                    }
                }
            }
        }

        throw new Exception(operation + " failed after 3 attempts", lastException);
    }

    @FunctionalInterface
    private interface HttpRequestExecutor<T> {
        T execute() throws Exception;
    }

    /**
     * 关闭客户端
     */
    public void shutdown() {
        connected = false;
        httpClient.dispatcher().executorService().shutdown();
        httpClient.connectionPool().evictAll();
    }
    
    /**
     * 测试连接并检查服务器重启
     */
    private void testConnectionAndCheckRestart() throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/health")
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Health check failed: " + response.code());
            }

            // 解析响应获取服务器启动时间
            String responseBody = response.body().string();
            Map<String, Object> healthData = objectMapper.readValue(responseBody, Map.class);

            Object startTimeObj = healthData.get("startTime");
            long newStartTime = 0;
            if (startTimeObj instanceof Number) {
                newStartTime = ((Number) startTimeObj).longValue();
            }

            // 检查服务器是否重启了
            if (serverStartTime > 0 && newStartTime > serverStartTime) {
                LOGGER.warn("Voice server restart detected! Previous start time: {}, new start time: {}",
                           serverStartTime, newStartTime);
                // 标记需要重新同步
                onServerRestart();
            }

            serverStartTime = newStartTime;
            LOGGER.info("Voice server health check passed, start time: {}", serverStartTime);
        }
    }

    /**
     * 处理服务器重启
     */
    private void onServerRestart() {
        LOGGER.info("Handling voice server restart - will resync all players");
        // 这个方法将在PlayerEventListener中被调用来重新同步所有玩家
    }
    
    /**
     * 玩家登录
     */
    public void playerLogin(PlayerInfo playerInfo) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("uuid", playerInfo.getUuid().toString());
        requestBody.put("name", playerInfo.getName());
        requestBody.put("serverName", config.getServerName());
        requestBody.put("permissions", playerInfo.getPermissions());
        
        if (playerInfo.getPosition() != null) {
            Position pos = playerInfo.getPosition();
            Map<String, Object> positionData = new HashMap<>();
            positionData.put("worldId", pos.getWorldId());
            positionData.put("x", pos.getX());
            positionData.put("y", pos.getY());
            positionData.put("z", pos.getZ());
            requestBody.put("position", positionData);
        }
        
        String json = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(json, JSON);
        
        Request request = new Request.Builder()
                .url(baseUrl + "/api/players/login")
                .header("Authorization", authHeader)
                .post(body)
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Player login failed: " + response.code() + " " + response.body().string());
            }
            LOGGER.debug("Player {} logged in successfully", playerInfo.getName());
        }
    }
    
    /**
     * 玩家登出
     */
    public void playerLogout(UUID playerUuid) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("uuid", playerUuid.toString());
        
        String json = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(json, JSON);
        
        Request request = new Request.Builder()
                .url(baseUrl + "/api/players/logout")
                .header("Authorization", authHeader)
                .post(body)
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Player logout failed: " + response.code() + " " + response.body().string());
            }
            LOGGER.debug("Player {} logged out successfully", playerUuid);
        }
    }
    
    /**
     * 更新玩家位置
     */
    public void updatePlayerPosition(UUID playerUuid, Position position) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("worldId", position.getWorldId());
        requestBody.put("x", position.getX());
        requestBody.put("y", position.getY());
        requestBody.put("z", position.getZ());
        
        String json = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(json, JSON);
        
        Request request = new Request.Builder()
                .url(baseUrl + "/api/players/" + playerUuid + "/position")
                .header("Authorization", authHeader)
                .put(body)
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Position update failed: " + response.code() + " " + response.body().string());
            }
            LOGGER.debug("Position updated for player {}", playerUuid);
        }
    }
    
    /**
     * 更新玩家权限
     */
    public void updatePlayerPermissions(UUID playerUuid, Map<String, Boolean> permissions) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("permissions", permissions);

        String json = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(json, JSON);

        Request request = new Request.Builder()
                .url(baseUrl + "/api/permissions/players/" + playerUuid)
                .header("Authorization", authHeader)
                .put(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Permissions update failed: " + response.code() + " " + response.body().string());
            }
            LOGGER.debug("Permissions updated for player {}", playerUuid);
        }
    }

    /**
     * 获取玩家权限
     */
    public Map<String, Object> getPlayerPermissions(UUID playerUuid) throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/permissions/players/" + playerUuid)
                .header("Authorization", authHeader)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Get permissions failed: " + response.code());
            }
            String responseBody = response.body().string();
            return objectMapper.readValue(responseBody, Map.class);
        }
    }

    /**
     * 检查玩家权限
     */
    public boolean checkPlayerPermission(UUID playerUuid, String permission) throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/permissions/players/" + playerUuid + "/check/" + permission)
                .header("Authorization", authHeader)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                return false; // 默认无权限
            }
            String responseBody = response.body().string();
            Map<String, Object> result = objectMapper.readValue(responseBody, Map.class);
            return (Boolean) result.getOrDefault("granted", false);
        }
    }

    /**
     * 将玩家添加到权限组
     */
    public void addPlayerToPermissionGroup(UUID playerUuid, String groupName) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("groupName", groupName);

        String json = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(json, JSON);

        Request request = new Request.Builder()
                .url(baseUrl + "/api/permissions/players/" + playerUuid + "/groups")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Add to permission group failed: " + response.code());
            }
            LOGGER.debug("Added player {} to permission group {}", playerUuid, groupName);
        }
    }

    /**
     * 从权限组移除玩家
     */
    public void removePlayerFromPermissionGroup(UUID playerUuid, String groupName) throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/permissions/players/" + playerUuid + "/groups/" + groupName)
                .header("Authorization", authHeader)
                .delete()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Remove from permission group failed: " + response.code());
            }
            LOGGER.debug("Removed player {} from permission group {}", playerUuid, groupName);
        }
    }
    
    /**
     * 确保玩家已在语音服务器中注册
     */
    public void ensurePlayerRegistered(Player player) throws Exception {
        // 构建玩家登录请求
        Map<String, Object> loginData = Map.of(
            "uuid", player.getUniqueId().toString(),
            "name", player.getName(),
            "serverName", config.getServerName()
        );

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(loginData), JSON);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/players/login")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                LOGGER.warn("Player registration failed for {}: {} {}", player.getName(), response.code(), responseBody);
                // 不抛出异常，允许继续尝试生成密钥
            } else {
                LOGGER.debug("Player {} registered successfully", player.getName());
            }
        }
    }

    /**
     * 生成玩家认证密钥
     */
    public String generatePlayerSecret(UUID playerUuid) throws Exception {
        return executeWithRetry("Generate secret for " + playerUuid, () -> {
            Request request = new Request.Builder()
                    .url(baseUrl + "/api/players/" + playerUuid + "/secret")
                    .header("Authorization", authHeader)
                    .post(RequestBody.create("", JSON))
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Secret generation failed: " + response.code() + " " + response.body().string());
                }

                String responseBody = response.body().string();
                Map<String, Object> result = objectMapper.readValue(responseBody, Map.class);
                String secret = (String) result.get("secret");

                LOGGER.debug("Generated secret for player {}", playerUuid);
                return secret;
            }
        });
    }
    
    /**
     * 创建群组
     * @return 创建成功时返回群组UUID，失败时返回null
     */
    public UUID createGroup(UUID playerUuid, String groupName, String password, int groupType) throws Exception {
        Map<String, Object> groupData = new HashMap<>();
        groupData.put("name", groupName);
        groupData.put("ownerUuid", playerUuid.toString()); // 修复字段名：creatorUuid -> ownerUuid
        groupData.put("type", groupType);
        if (password != null) {
            groupData.put("password", password);
        }

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(groupData), JSON);
        Request request = new Request.Builder()
                .url(config.getVoiceServer().getApiEndpoint() + "/api/groups")
                .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();

                // 解析响应获取群组UUID
                Map<String, Object> responseData = objectMapper.readValue(responseBody, Map.class);

                // 从嵌套的group对象中获取id
                Map<String, Object> groupInfo = (Map<String, Object>) responseData.get("group");
                String groupUuidStr = groupInfo != null ? (String) groupInfo.get("id") : null;

                if (groupUuidStr != null) {
                    UUID groupUuid = UUID.fromString(groupUuidStr);
                    LOGGER.debug("Successfully created group {} with UUID {} for player {}", groupName, groupUuid, playerUuid);
                    return groupUuid;
                } else {
                    LOGGER.warn("Group created but no UUID returned in response");
                    return null;
                }
            } else {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                LOGGER.warn("Failed to create group {}: {} {}", groupName, response.code(), responseBody);
                return null;
            }
        }
    }

    /**
     * 加入群组
     */
    public boolean joinGroup(UUID playerUuid, UUID groupUuid, String password) throws Exception {
        Map<String, Object> joinData = new HashMap<>();
        joinData.put("playerUuid", playerUuid.toString());
        if (password != null) {
            joinData.put("password", password);
        }

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(joinData), JSON);
        Request request = new Request.Builder()
                .url(config.getVoiceServer().getApiEndpoint() + "/api/groups/" + groupUuid + "/members")
                .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.debug("Successfully joined group {} for player {}", groupUuid, playerUuid);
                return true;
            } else {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                LOGGER.warn("Failed to join group {}: {} {}", groupUuid, response.code(), responseBody);
                return false;
            }
        }
    }

    /**
     * 离开群组
     */
    public boolean leaveGroup(UUID playerUuid) throws Exception {
        String url = config.getVoiceServer().getApiEndpoint() + "/api/players/" + playerUuid + "/group";

        Request request = new Request.Builder()
                .url(url)
                .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                .delete()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Successfully left group for player {}", playerUuid);
                return true;
            } else {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                LOGGER.warn("Failed to leave group for player {}: {} - {}", playerUuid, response.code(), responseBody);
                return false;
            }
        }
    }

    /**
     * 邀请玩家加入群组
     */
    public boolean invitePlayerToGroup(UUID groupId, UUID inviterUuid, UUID inviteeUuid) throws Exception {
        Map<String, Object> inviteData = new HashMap<>();
        inviteData.put("inviterUuid", inviterUuid.toString());
        inviteData.put("inviteeUuid", inviteeUuid.toString());

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(inviteData), JSON);
        Request request = new Request.Builder()
                .url(config.getVoiceServer().getApiEndpoint() + "/api/groups/" + groupId + "/invite")
                .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Successfully invited player {} to group {}", inviteeUuid, groupId);
                return true;
            } else {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                LOGGER.warn("Failed to invite player {} to group {}: {} - {}", inviteeUuid, groupId, response.code(), responseBody);
                return false;
            }
        }
    }

    /**
     * 踢出玩家
     */
    public boolean kickPlayerFromGroup(UUID groupId, UUID kickerUuid, UUID kickeeUuid) throws Exception {
        Map<String, Object> kickData = new HashMap<>();
        kickData.put("kickerUuid", kickerUuid.toString());
        kickData.put("kickeeUuid", kickeeUuid.toString());

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(kickData), JSON);
        Request request = new Request.Builder()
                .url(config.getVoiceServer().getApiEndpoint() + "/api/groups/" + groupId + "/kick")
                .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Successfully kicked player {} from group {}", kickeeUuid, groupId);
                return true;
            } else {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                LOGGER.warn("Failed to kick player {} from group {}: {} - {}", kickeeUuid, groupId, response.code(), responseBody);
                return false;
            }
        }
    }

    /**
     * 禁止玩家
     */
    public boolean banPlayerFromGroup(UUID groupId, UUID bannerUuid, UUID banneeUuid) throws Exception {
        Map<String, Object> banData = new HashMap<>();
        banData.put("bannerUuid", bannerUuid.toString());
        banData.put("banneeUuid", banneeUuid.toString());

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(banData), JSON);
        Request request = new Request.Builder()
                .url(config.getVoiceServer().getApiEndpoint() + "/api/groups/" + groupId + "/ban")
                .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Successfully banned player {} from group {}", banneeUuid, groupId);
                return true;
            } else {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                LOGGER.warn("Failed to ban player {} from group {}: {} - {}", banneeUuid, groupId, response.code(), responseBody);
                return false;
            }
        }
    }

    /**
     * 设置群组管理员
     */
    public boolean setGroupModerator(UUID groupId, UUID setterUuid, UUID targetUuid, boolean isModerator) throws Exception {
        Map<String, Object> modData = new HashMap<>();
        modData.put("setterUuid", setterUuid.toString());
        modData.put("targetUuid", targetUuid.toString());
        modData.put("isModerator", isModerator);

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(modData), JSON);
        Request request = new Request.Builder()
                .url(config.getVoiceServer().getApiEndpoint() + "/api/groups/" + groupId + "/moderator")
                .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Successfully set moderator status for player {} in group {}: {}", targetUuid, groupId, isModerator);
                return true;
            } else {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                LOGGER.warn("Failed to set moderator status for player {} in group {}: {} - {}", targetUuid, groupId, response.code(), responseBody);
                return false;
            }
        }
    }

    /**
     * 转移群组所有权
     */
    public boolean transferGroupOwnership(UUID groupId, UUID currentOwnerUuid, UUID newOwnerUuid) throws Exception {
        Map<String, Object> transferData = new HashMap<>();
        transferData.put("currentOwnerUuid", currentOwnerUuid.toString());
        transferData.put("newOwnerUuid", newOwnerUuid.toString());

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(transferData), JSON);
        Request request = new Request.Builder()
                .url(config.getVoiceServer().getApiEndpoint() + "/api/groups/" + groupId + "/transfer")
                .header("Authorization", "Bearer " + config.getVoiceServer().getAuthToken())
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Successfully transferred ownership of group {} from {} to {}", groupId, currentOwnerUuid, newOwnerUuid);
                return true;
            } else {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                LOGGER.warn("Failed to transfer ownership of group {} from {} to {}: {} - {}", groupId, currentOwnerUuid, newOwnerUuid, response.code(), responseBody);
                return false;
            }
        }
    }

    /**
     * 获取世界配置
     */
    public Map<String, Object> getWorldConfig(String worldName) throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/worlds/" + worldName)
                .header("Authorization", authHeader)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Get world config failed: " + response.code());
            }
            String responseBody = response.body().string();
            return objectMapper.readValue(responseBody, Map.class);
        }
    }

    /**
     * 更新世界配置
     */
    public void updateWorldConfig(String worldName, Map<String, Object> config) throws Exception {
        String json = objectMapper.writeValueAsString(config);
        RequestBody body = RequestBody.create(json, JSON);

        Request request = new Request.Builder()
                .url(baseUrl + "/api/worlds/" + worldName)
                .header("Authorization", authHeader)
                .put(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Update world config failed: " + response.code());
            }
            LOGGER.debug("World config updated for {}", worldName);
        }
    }

    /**
     * 添加跨世界规则
     */
    public void addCrossWorldRule(String fromWorld, String toWorld) throws Exception {
        Map<String, Object> ruleData = new HashMap<>();
        ruleData.put("fromWorld", fromWorld);
        ruleData.put("toWorld", toWorld);

        String json = objectMapper.writeValueAsString(ruleData);
        RequestBody body = RequestBody.create(json, JSON);

        Request request = new Request.Builder()
                .url(baseUrl + "/api/worlds/cross-world-rules")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Add cross-world rule failed: " + response.code());
            }
            LOGGER.debug("Added cross-world rule: {} -> {}", fromWorld, toWorld);
        }
    }

    /**
     * 移除跨世界规则
     */
    public void removeCrossWorldRule(String fromWorld, String toWorld) throws Exception {
        Map<String, Object> ruleData = new HashMap<>();
        ruleData.put("fromWorld", fromWorld);
        ruleData.put("toWorld", toWorld);

        String json = objectMapper.writeValueAsString(ruleData);
        RequestBody body = RequestBody.create(json, JSON);

        Request request = new Request.Builder()
                .url(baseUrl + "/api/worlds/cross-world-rules")
                .header("Authorization", authHeader)
                .delete(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Remove cross-world rule failed: " + response.code());
            }
            LOGGER.debug("Removed cross-world rule: {} -> {}", fromWorld, toWorld);
        }
    }

    /**
     * 获取Opus配置
     */
    public Map<String, Object> getOpusConfig() throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/opus/config")
                .header("Authorization", authHeader)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Get Opus config failed: " + response.code());
            }
            String responseBody = response.body().string();
            return objectMapper.readValue(responseBody, Map.class);
        }
    }

    /**
     * 更新Opus配置
     */
    public void updateOpusConfig(Map<String, Object> config) throws Exception {
        String json = objectMapper.writeValueAsString(config);
        RequestBody body = RequestBody.create(json, JSON);

        Request request = new Request.Builder()
                .url(baseUrl + "/api/opus/config")
                .header("Authorization", authHeader)
                .put(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Update Opus config failed: " + response.code());
            }
            LOGGER.debug("Opus config updated successfully");
        }
    }

    /**
     * 获取可用的编解码器
     */
    public Map<String, Object> getAvailableCodecs() throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/opus/codecs")
                .header("Authorization", authHeader)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Get available codecs failed: " + response.code());
            }
            String responseBody = response.body().string();
            return objectMapper.readValue(responseBody, Map.class);
        }
    }

    /**
     * 获取Opus统计信息
     */
    public Map<String, Object> getOpusStatistics() throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/opus/statistics")
                .header("Authorization", authHeader)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Get Opus statistics failed: " + response.code());
            }
            String responseBody = response.body().string();
            return objectMapper.readValue(responseBody, Map.class);
        }
    }

    /**
     * 重置Opus配置
     */
    public void resetOpusConfig() throws Exception {
        RequestBody body = RequestBody.create("", JSON);

        Request request = new Request.Builder()
                .url(baseUrl + "/api/opus/reset")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Reset Opus config failed: " + response.code());
            }
            LOGGER.debug("Opus config reset successfully");
        }
    }



    /**
     * 获取服务器状态
     */
    public Map<String, Object> getServerStatus() throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/status")
                .header("Authorization", authHeader)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Status request failed: " + response.code() + " " + response.body().string());
            }

            String responseBody = response.body().string();
            return objectMapper.readValue(responseBody, Map.class);
        }
    }

    /**
     * 管理员强制玩家加入群组
     */
    public boolean forceJoinGroup(UUID groupId, UUID playerUuid, UUID adminUuid) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("groupId", groupId.toString());
        params.put("playerUuid", playerUuid.toString());
        params.put("adminUuid", adminUuid.toString());

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(params), JSON);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/admin/groups/force-join")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Admin {} forced player {} to join group {}", adminUuid, playerUuid, groupId);
                return true;
            } else {
                LOGGER.warn("Failed to force join group: {} {}", response.code(), response.body().string());
                return false;
            }
        }
    }

    /**
     * 管理员强制玩家离开群组
     */
    public boolean forceLeaveGroup(UUID groupId, UUID playerUuid, UUID adminUuid) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("groupId", groupId.toString());
        params.put("playerUuid", playerUuid.toString());
        params.put("adminUuid", adminUuid.toString());

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(params), JSON);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/admin/groups/force-leave")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Admin {} forced player {} to leave group {}", adminUuid, playerUuid, groupId);
                return true;
            } else {
                LOGGER.warn("Failed to force leave group: {} {}", response.code(), response.body().string());
                return false;
            }
        }
    }

    /**
     * 管理员强制删除群组
     */
    public boolean forceDeleteGroup(UUID groupId, UUID adminUuid) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("groupId", groupId.toString());
        params.put("adminUuid", adminUuid.toString());

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(params), JSON);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/admin/groups/force-delete")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Admin {} force deleted group {}", adminUuid, groupId);
                return true;
            } else {
                LOGGER.warn("Failed to force delete group: {} {}", response.code(), response.body().string());
                return false;
            }
        }
    }

    /**
     * 全局封禁玩家
     */
    public boolean globalBanPlayer(UUID playerUuid, UUID adminUuid, String reason) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("playerUuid", playerUuid.toString());
        params.put("adminUuid", adminUuid.toString());
        if (reason != null) {
            params.put("reason", reason);
        }

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(params), JSON);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/admin/players/global-ban")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Admin {} globally banned player {}. Reason: {}", adminUuid, playerUuid, reason);
                return true;
            } else {
                LOGGER.warn("Failed to global ban player: {} {}", response.code(), response.body().string());
                return false;
            }
        }
    }

    /**
     * 解除全局封禁
     */
    public boolean globalUnbanPlayer(UUID playerUuid, UUID adminUuid) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("playerUuid", playerUuid.toString());
        params.put("adminUuid", adminUuid.toString());

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(params), JSON);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/admin/players/global-unban")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Admin {} removed global ban for player {}", adminUuid, playerUuid);
                return true;
            } else {
                LOGGER.warn("Failed to global unban player: {} {}", response.code(), response.body().string());
                return false;
            }
        }
    }

    /**
     * 全局踢出玩家
     */
    public boolean globalKickPlayer(UUID playerUuid, UUID adminUuid, String reason) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("playerUuid", playerUuid.toString());
        params.put("adminUuid", adminUuid.toString());
        if (reason != null) {
            params.put("reason", reason);
        }

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(params), JSON);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/admin/players/global-kick")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Admin {} globally kicked player {}. Reason: {}", adminUuid, playerUuid, reason);
                return true;
            } else {
                LOGGER.warn("Failed to global kick player: {} {}", response.code(), response.body().string());
                return false;
            }
        }
    }

    /**
     * 开始广播
     */
    public boolean startBroadcast(UUID playerUuid) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("playerUuid", playerUuid.toString());

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(params), JSON);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/broadcast/start")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Started broadcast for player {}", playerUuid);
                return true;
            } else {
                LOGGER.warn("Failed to start broadcast: {} {}", response.code(), response.body().string());
                return false;
            }
        }
    }

    /**
     * 停止广播
     */
    public boolean stopBroadcast(UUID playerUuid) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("playerUuid", playerUuid.toString());

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(params), JSON);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/broadcast/stop")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Stopped broadcast for player {}", playerUuid);
                return true;
            } else {
                LOGGER.warn("Failed to stop broadcast: {} {}", response.code(), response.body().string());
                return false;
            }
        }
    }

    /**
     * 同步世界配置到独立语音服务器
     */
    public boolean syncWorldConfigs(Map<String, Map<String, Object>> worldConfigs) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("serverName", config.getServerName());
        params.put("worldConfigs", worldConfigs);

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(params), JSON);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/worlds/sync-configs")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Successfully synced {} world configs to voice server", worldConfigs.size());
                return true;
            } else {
                LOGGER.warn("Failed to sync world configs: {} {}", response.code(), response.body().string());
                return false;
            }
        }
    }

    /**
     * 同步单个世界配置
     */
    public boolean syncWorldConfig(String worldName, Map<String, Object> worldConfig) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("serverName", config.getServerName());
        params.put("worldName", worldName);
        params.put("worldConfig", worldConfig);

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(params), JSON);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/worlds/sync-config")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.debug("Successfully synced world config for {} to voice server", worldName);
                return true;
            } else {
                LOGGER.warn("Failed to sync world config for {}: {} {}", worldName, response.code(), response.body().string());
                return false;
            }
        }
    }

    /**
     * 更新玩家游戏模式
     */
    public void updatePlayerGameMode(UUID playerUuid, int gameMode) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("gameMode", gameMode);

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(params), JSON);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/players/" + playerUuid + "/gamemode")
                .header("Authorization", authHeader)
                .put(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Game mode update failed: " + response.code() + " " + response.body().string());
            }
            LOGGER.debug("Game mode updated for player {}: {}", playerUuid, gameMode);
        }
    }

    /**
     * 授予观察者语音权限
     */
    public void grantSpectatorVoicePermission(UUID playerUuid) throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/players/" + playerUuid + "/permissions/spectator-voice/grant")
                .header("Authorization", authHeader)
                .post(RequestBody.create("", JSON))
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Grant spectator voice permission failed: " + response.code() + " " + response.body().string());
            }
            LOGGER.debug("Granted spectator voice permission for player {}", playerUuid);
        }
    }

    /**
     * 撤销观察者语音权限
     */
    public void revokeSpectatorVoicePermission(UUID playerUuid) throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/players/" + playerUuid + "/permissions/spectator-voice/revoke")
                .header("Authorization", authHeader)
                .post(RequestBody.create("", JSON))
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Revoke spectator voice permission failed: " + response.code() + " " + response.body().string());
            }
            LOGGER.debug("Revoked spectator voice permission for player {}", playerUuid);
        }
    }

    /**
     * 创建带服务器前缀的世界ID，避免跨服务器世界名冲突
     */
    private String createWorldId(String worldName) {
        String serverName = config.getServerName();
        return serverName + ":" + worldName;
    }

    /**
     * 注册服务器到独立语音服务器
     * @param serverName 服务器名称
     * @param host 服务器主机地址
     * @param port 服务器端口
     * @return 如果注册成功返回true，如果服务器名称已存在返回false
     * @throws Exception 如果发生网络错误或其他异常
     */
    public boolean registerServer(String serverName, String host, int port) throws Exception {
        Map<String, Object> registrationData = Map.of(
            "serverName", serverName,
            "host", host,
            "port", port
        );

        RequestBody body = RequestBody.create(objectMapper.writeValueAsString(registrationData), JSON);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/servers/register")
                .header("Authorization", authHeader)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                LOGGER.info("Server '{}' registered successfully", serverName);
                return true;
            } else if (response.code() == 409) {
                // 服务器名称冲突
                LOGGER.warn("Server name '{}' is already registered", serverName);
                return false;
            } else {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                throw new Exception("Failed to register server: " + response.code() + " " + responseBody);
            }
        }
    }

    /**
     * 发送keepalive到独立语音服务器
     * @param serverName 服务器名称
     * @return 如果发送成功返回true，否则返回false
     */
    public boolean sendKeepalive(String serverName) {
        try {
            Request request = new Request.Builder()
                    .url(baseUrl + "/api/servers/" + serverName + "/keepalive")
                    .header("Authorization", authHeader)
                    .post(RequestBody.create("", JSON))
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    LOGGER.debug("Keepalive sent successfully for server: {}", serverName);
                    return true;
                } else {
                    LOGGER.warn("Failed to send keepalive for server '{}': {} {}",
                            serverName, response.code(), response.message());
                    return false;
                }
            }
        } catch (Exception e) {
            LOGGER.warn("Error sending keepalive for server '{}': {}", serverName, e.getMessage());
            return false;
        }
    }

    /**
     * 检查服务器名称是否可用
     * @param serverName 服务器名称
     * @return 如果名称可用返回true，否则返回false
     * @throws Exception 如果发生网络错误
     */
    public boolean isServerNameAvailable(String serverName) throws Exception {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/servers/" + serverName + "/available")
                .header("Authorization", authHeader)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                Map<String, Object> result = objectMapper.readValue(responseBody, Map.class);
                return (Boolean) result.get("available");
            } else {
                throw new Exception("Failed to check server name availability: " + response.code());
            }
        }
    }

    /**
     * 取消注册服务器
     * @param serverName 服务器名称
     * @return 如果取消注册成功返回true，否则返回false
     */
    public boolean unregisterServer(String serverName) {
        try {
            Request request = new Request.Builder()
                    .url(baseUrl + "/api/servers/register/" + serverName)
                    .header("Authorization", authHeader)
                    .delete()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    LOGGER.info("Server '{}' unregistered successfully", serverName);
                    return true;
                } else {
                    LOGGER.warn("Failed to unregister server '{}': {} {}",
                            serverName, response.code(), response.message());
                    return false;
                }
            }
        } catch (Exception e) {
            LOGGER.warn("Error unregistering server '{}': {}", serverName, e.getMessage());
            return false;
        }
    }
}
