@echo off
REM 独立语音服务器启动脚本 (Windows)

REM 设置Java选项
set JAVA_OPTS=-Xmx1G -Xms512M
set JAVA_OPTS=%JAVA_OPTS% -XX:+UseG1GC
set JAVA_OPTS=%JAVA_OPTS% -XX:+UnlockExperimentalVMOptions
set JAVA_OPTS=%JAVA_OPTS% -XX:MaxGCPauseMillis=100
set JAVA_OPTS=%JAVA_OPTS% -XX:+DisableExplicitGC
set JAVA_OPTS=%JAVA_OPTS% -XX:TargetSurvivorRatio=90
set JAVA_OPTS=%JAVA_OPTS% -XX:G1NewSizePercent=50
set JAVA_OPTS=%JAVA_OPTS% -XX:G1MaxNewSizePercent=80
set JAVA_OPTS=%JAVA_OPTS% -XX:G1MixedGCLiveThresholdPercent=35
set JAVA_OPTS=%JAVA_OPTS% -XX:+AlwaysPreTouch

REM 设置日志配置
set JAVA_OPTS=%JAVA_OPTS% -Dlogback.configurationFile=logback.xml

REM 设置工作目录
cd /d "%~dp0"

REM 检查Java版本
for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
    set JAVA_VERSION=%%g
)
set JAVA_VERSION=%JAVA_VERSION:"=%
for /f "delims=. tokens=1" %%v in ("%JAVA_VERSION%") do (
    set JAVA_MAJOR=%%v
)

if %JAVA_MAJOR% LSS 17 (
    echo 错误：需要Java 17或更高版本
    pause
    exit /b 1
)

REM 检查JAR文件是否存在
set JAR_FILE=standalone-voice-server-1.0.0.jar
if not exist "%JAR_FILE%" (
    echo 错误：找不到JAR文件 %JAR_FILE%
    echo 请先运行 'gradlew shadowJar' 构建项目
    pause
    exit /b 1
)

REM 检查配置文件
set CONFIG_FILE=voice-server.yml
if not exist "%CONFIG_FILE%" (
    echo 警告：找不到配置文件 %CONFIG_FILE%，将使用默认配置
)

echo 启动独立语音服务器...
echo Java选项: %JAVA_OPTS%
echo JAR文件: %JAR_FILE%
echo 配置文件: %CONFIG_FILE%
echo.

REM 启动服务器
java %JAVA_OPTS% -jar "%JAR_FILE%" "%CONFIG_FILE%"

pause
