package de.maxhenkel.voicechat.adapter;

import de.maxhenkel.voicechat.adapter.config.AdapterConfig;
import de.maxhenkel.voicechat.adapter.network.VoiceServerClient;
import de.maxhenkel.voicechat.adapter.network.SecretPacketSender;
import de.maxhenkel.voicechat.adapter.network.GroupMessageHandler;
import de.maxhenkel.voicechat.adapter.network.ConnectionManager;
import de.maxhenkel.voicechat.adapter.listeners.PlayerEventListener;
import de.maxhenkel.voicechat.adapter.managers.PlayerStateManager;
import de.maxhenkel.voicechat.adapter.commands.VoiceChatCommand;
import de.maxhenkel.voicechat.adapter.permission.PermissionSyncManager;
import de.maxhenkel.voicechat.adapter.group.AdvancedGroupManager;
import de.maxhenkel.voicechat.adapter.world.WorldConfigManager;
import de.maxhenkel.voicechat.adapter.broadcast.VoiceBroadcastManager;
import org.bukkit.plugin.java.JavaPlugin;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;

/**
 * Minecraft服务器语音聊天适配器插件
 */
public class VoiceChatAdapterPlugin extends JavaPlugin {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(VoiceChatAdapterPlugin.class);
    
    private static VoiceChatAdapterPlugin instance;
    private AdapterConfig config;
    private VoiceServerClient voiceServerClient;
    private ConnectionManager connectionManager;
    private SecretPacketSender secretPacketSender;
    private GroupMessageHandler groupMessageHandler;
    private PlayerStateManager playerStateManager;
    private PlayerEventListener playerEventListener;
    private PermissionSyncManager permissionSyncManager;
    private AdvancedGroupManager advancedGroupManager;
    private WorldConfigManager worldConfigManager;
    private VoiceBroadcastManager voiceBroadcastManager;
    
    @Override
    public void onEnable() {
        instance = this;
        
        LOGGER.info("Enabling Voice Chat Adapter Plugin...");
        
        try {
            // 加载配置
            loadConfiguration();
            
            // 初始化语音服务器客户端
            initializeVoiceServerClient();

            // 初始化SecretPacket发送器
            initializeSecretPacketSender();

            // 初始化玩家状态管理器
            initializePlayerStateManager();

            // 初始化群组消息处理器
            initializeGroupMessageHandler();

            // 初始化连接管理器
            initializeConnectionManager();

            // 注册事件监听器
            registerEventListeners();

            // 初始化权限同步管理器
            initializePermissionSyncManager();

            // 初始化高级群组管理器
            initializeAdvancedGroupManager();

            // 初始化世界配置管理器
            initializeWorldConfigManager();

            // 初始化语音广播管理器
            initializeVoiceBroadcastManager();

            // 注册命令
            registerCommands();
            
            LOGGER.info("Voice Chat Adapter Plugin enabled successfully!");
            
        } catch (Exception e) {
            LOGGER.error("Failed to enable Voice Chat Adapter Plugin", e);
            getServer().getPluginManager().disablePlugin(this);
        }
    }
    
    @Override
    public void onDisable() {
        LOGGER.info("Disabling Voice Chat Adapter Plugin...");
        
        try {
            // 关闭连接管理器
            if (connectionManager != null) {
                connectionManager.stop();
            }

            // 关闭玩家状态管理器
            if (playerStateManager != null) {
                playerStateManager.shutdown();
            }

            // 关闭语音广播管理器
            if (voiceBroadcastManager != null) {
                voiceBroadcastManager.shutdown();
            }

            // 关闭SecretPacket发送器
            if (secretPacketSender != null) {
                secretPacketSender.unregister();
            }

            // 关闭权限同步管理器
            if (permissionSyncManager != null) {
                permissionSyncManager.shutdown();
            }

            // 取消注册服务器并关闭语音服务器客户端
            if (voiceServerClient != null) {
                try {
                    voiceServerClient.unregisterServer(config.getServerName());
                } catch (Exception e) {
                    LOGGER.warn("Failed to unregister server during shutdown: {}", e.getMessage());
                }
                voiceServerClient.shutdown();
            }

            LOGGER.info("Voice Chat Adapter Plugin disabled successfully!");

        } catch (Exception e) {
            LOGGER.error("Error during plugin disable", e);
        }
        
        instance = null;
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfiguration() throws Exception {
        // 创建配置目录
        if (!getDataFolder().exists()) {
            getDataFolder().mkdirs();
        }
        
        // 保存默认配置文件
        saveDefaultConfig();
        
        // 加载配置
        File configFile = new File(getDataFolder(), "config.yml");
        config = AdapterConfig.load(configFile);
        
        LOGGER.info("Configuration loaded from: {}", configFile.getAbsolutePath());
    }
    
    /**
     * 初始化语音服务器客户端
     */
    private void initializeVoiceServerClient() throws Exception {
        voiceServerClient = new VoiceServerClient(config);
        voiceServerClient.initialize();

        // 检查服务器名称是否可用并尝试注册
        String serverName = config.getServerName();
        try {
            // 检查服务器名称是否可用
            if (!voiceServerClient.isServerNameAvailable(serverName)) {
                LOGGER.error("=".repeat(80));
                LOGGER.error("SERVER NAME CONFLICT DETECTED!");
                LOGGER.error("The server name '{}' is already registered with the voice server.", serverName);
                LOGGER.error("Please change the 'server-name' in your config.yml file to a unique name.");
                LOGGER.error("Current server name: {}", serverName);
                LOGGER.error("=".repeat(80));
                throw new Exception("Server name '" + serverName + "' is already in use. Please choose a different name.");
            }

            // 尝试注册服务器
            String host = getServer().getIp().isEmpty() ? "localhost" : getServer().getIp();
            int port = getServer().getPort();

            if (!voiceServerClient.registerServer(serverName, host, port)) {
                LOGGER.warn("Failed to register server '{}' - name may have been taken by another server", serverName);
                // 不抛出异常，允许继续运行，但会在keepalive时重试注册
            } else {
                LOGGER.info("Server '{}' registered successfully with voice server", serverName);
            }

        } catch (Exception e) {
            if (e.getMessage().contains("already in use")) {
                throw e; // 重新抛出名称冲突异常
            } else {
                LOGGER.warn("Failed to register server during initialization: {}", e.getMessage());
                // 不抛出异常，允许继续运行
            }
        }

        LOGGER.info("Voice server client initialized: {}", config.getVoiceServer().getApiEndpoint());
    }

    /**
     * 初始化SecretPacket发送器
     */
    private void initializeSecretPacketSender() {
        secretPacketSender = new SecretPacketSender(this, voiceServerClient);
        secretPacketSender.register();

        LOGGER.info("Secret packet sender initialized");
    }

    /**
     * 初始化玩家状态管理器
     */
    private void initializePlayerStateManager() {
        playerStateManager = new PlayerStateManager(this);
        playerStateManager.initialize(); // 初始化网络支持
        LOGGER.info("Player state manager initialized");
    }

    /**
     * 初始化群组消息处理器
     */
    private void initializeGroupMessageHandler() {
        groupMessageHandler = new GroupMessageHandler(this, voiceServerClient, playerStateManager);

        // 注册群组相关的插件消息通道
        getServer().getMessenger().registerIncomingPluginChannel(this, "voicechat:create_group", groupMessageHandler);
        getServer().getMessenger().registerIncomingPluginChannel(this, "voicechat:set_group", groupMessageHandler);
        getServer().getMessenger().registerIncomingPluginChannel(this, "voicechat:leave_group", groupMessageHandler);

        // 注册输出通道（用于发送响应）
        getServer().getMessenger().registerOutgoingPluginChannel(this, "voicechat:add_group");
        getServer().getMessenger().registerOutgoingPluginChannel(this, "voicechat:remove_group");
        getServer().getMessenger().registerOutgoingPluginChannel(this, "voicechat:joined_group");

        // 注册玩家状态通道
        getServer().getMessenger().registerOutgoingPluginChannel(this, "voicechat:player_state");
        getServer().getMessenger().registerOutgoingPluginChannel(this, "voicechat:player_states");

        LOGGER.info("Group message handler initialized");
    }
    
    /**
     * 注册事件监听器
     */
    private void registerEventListeners() {
        playerEventListener = new PlayerEventListener(this, voiceServerClient);
        getServer().getPluginManager().registerEvents(playerEventListener, this);
        
        LOGGER.info("Event listeners registered");
    }
    
    /**
     * 初始化连接管理器
     */
    private void initializeConnectionManager() {
        connectionManager = new ConnectionManager(this, voiceServerClient);
        connectionManager.start();

        LOGGER.info("Connection manager initialized");
    }

    /**
     * 注册命令
     */
    private void registerCommands() {
        VoiceChatCommand voiceChatCommand = new VoiceChatCommand(this, voiceServerClient);
        getCommand("voicechat").setExecutor(voiceChatCommand);
        getCommand("voicechat").setTabCompleter(voiceChatCommand);

        LOGGER.info("Commands registered");
    }
    
    /**
     * 重载配置
     */
    public void reloadConfiguration() throws Exception {
        reloadConfig();
        File configFile = new File(getDataFolder(), "config.yml");
        config = AdapterConfig.load(configFile);
        
        // 重新初始化组件
        if (secretPacketSender != null) {
            secretPacketSender.unregister();
        }
        if (voiceServerClient != null) {
            voiceServerClient.shutdown();
        }

        initializeVoiceServerClient();
        initializeSecretPacketSender();
        
        LOGGER.info("Configuration reloaded");
    }

    /**
     * 初始化权限同步管理器
     */
    private void initializePermissionSyncManager() {
        permissionSyncManager = new PermissionSyncManager(voiceServerClient, config);
        LOGGER.info("Permission sync manager initialized");
    }

    /**
     * 初始化高级群组管理器
     */
    private void initializeAdvancedGroupManager() {
        advancedGroupManager = new AdvancedGroupManager(voiceServerClient);
        LOGGER.info("Advanced group manager initialized");
    }

    /**
     * 初始化世界配置管理器
     */
    private void initializeWorldConfigManager() {
        worldConfigManager = new WorldConfigManager(voiceServerClient, config);
        // 注册世界切换事件监听器
        getServer().getPluginManager().registerEvents(worldConfigManager, this);
        LOGGER.info("World config manager initialized");
    }

    // Getters
    public static VoiceChatAdapterPlugin getInstance() {
        return instance;
    }

    public AdapterConfig getAdapterConfig() {
        return config;
    }

    public VoiceServerClient getVoiceServerClient() {
        return voiceServerClient;
    }

    public ConnectionManager getConnectionManager() {
        return connectionManager;
    }

    public GroupMessageHandler getGroupMessageHandler() {
        return groupMessageHandler;
    }

    public SecretPacketSender getSecretPacketSender() {
        return secretPacketSender;
    }

    public PlayerStateManager getPlayerStateManager() {
        return playerStateManager;
    }

    public PermissionSyncManager getPermissionSyncManager() {
        return permissionSyncManager;
    }

    public AdvancedGroupManager getAdvancedGroupManager() {
        return advancedGroupManager;
    }

    public WorldConfigManager getWorldConfigManager() {
        return worldConfigManager;
    }

    public VoiceBroadcastManager getVoiceBroadcastManager() {
        return voiceBroadcastManager;
    }

    /**
     * 初始化语音广播管理器
     */
    private void initializeVoiceBroadcastManager() {
        voiceBroadcastManager = new VoiceBroadcastManager(this);
        LOGGER.info("Voice broadcast manager initialized");
    }
}
