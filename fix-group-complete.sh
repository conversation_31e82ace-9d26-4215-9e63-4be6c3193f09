#!/bin/bash

# 群组功能完整修复部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示修复总结
show_complete_fix() {
    log_step "群组功能完整修复总结"
    
    echo "=================================="
    echo "🎉 群组功能完整修复完成！"
    echo "=================================="
    echo ""
    echo "📋 修复的所有问题："
    echo ""
    echo "1. ❌ 之前: Secret generation failed: 404 Not Found"
    echo "   ✅ 现在: 自动注册玩家并生成密钥"
    echo ""
    echo "2. ❌ 之前: Unknown packet ID: -76 错误"
    echo "   ✅ 现在: 正确处理所有数据包类型"
    echo ""
    echo "3. ❌ 之前: 群组功能完全不可用"
    echo "   ✅ 现在: 完整的群组管理功能"
    echo ""
    echo "🔧 完整的修复内容："
    echo ""
    echo "📦 独立语音服务器修复："
    echo "- ✅ 添加了所有缺少的数据包类型支持"
    echo "- ✅ 改进了数据包格式验证"
    echo "- ✅ 优雅处理未知数据包"
    echo "- ✅ 完整的群组API端点"
    echo ""
    echo "🔌 适配器插件修复："
    echo "- ✅ 自动玩家注册功能"
    echo "- ✅ 群组消息处理器"
    echo "- ✅ 完整的群组管理API调用"
    echo "- ✅ 正确的插件消息通道注册"
    echo ""
    echo "🎯 现在支持的完整功能："
    echo "- ✅ 玩家认证和密钥生成"
    echo "- ✅ 实时语音聊天"
    echo "- ✅ 创建语音群组"
    echo "- ✅ 加入/离开群组"
    echo "- ✅ 群组密码保护"
    echo "- ✅ 不同群组类型（普通/开放/隔离）"
    echo "- ✅ 连接检查和心跳"
    echo "- ✅ 位置音频"
    echo ""
    echo "🚀 部署步骤："
    echo "1. 停止所有服务"
    echo "2. 部署新版本文件"
    echo "3. 重启服务"
    echo "4. 测试功能"
    echo "=================================="
}

# 部署指南
show_deployment_guide() {
    log_step "详细部署指南"
    
    echo ""
    echo "🚀 完整部署步骤："
    echo ""
    echo "1️⃣ 停止现有服务："
    echo "   # 停止Paper服务器"
    echo "   systemctl stop paper-server"
    echo ""
    echo "   # 停止独立语音服务器"
    echo "   kill \$(cat voice-server.pid)"
    echo ""
    echo "2️⃣ 备份现有文件："
    echo "   cp /path/to/paper/plugins/minecraft-adapter-1.0.0.jar /path/to/backup/"
    echo "   cp /path/to/voice-server/standalone-voice-server-1.0.0.jar /path/to/backup/"
    echo ""
    echo "3️⃣ 部署新版本："
    echo "   # 部署新适配器"
    echo "   cp minecraft-adapter/build/libs/minecraft-adapter-1.0.0.jar /path/to/paper/plugins/"
    echo ""
    echo "   # 部署新独立语音服务器"
    echo "   cp standalone-voice-server/build/libs/standalone-voice-server-1.0.0.jar /path/to/voice-server/"
    echo ""
    echo "4️⃣ 配置检查："
    echo "   # 适配器配置 (plugins/VoiceChatAdapter/config.yml)"
    echo "   voice-server:"
    echo "     host: \"localhost\""
    echo "     port: 24454"
    echo "     api-endpoint: \"http://localhost:8080\""
    echo "     auth-token: \"change-this-secret-token\""
    echo ""
    echo "   sync:"
    echo "     position-interval: 1000"
    echo "     permission-interval: 5000"
    echo "     sync-on-move: true"
    echo "     min-move-distance: 1.0"
    echo ""
    echo "   server-name: \"survival\""
    echo ""
    echo "   # 独立语音服务器配置 (voice-server.yml)"
    echo "   server:"
    echo "     host: \"0.0.0.0\""
    echo "     port: 24454"
    echo "     api-port: 8080"
    echo "     auth-token: \"change-this-secret-token\""
    echo ""
    echo "5️⃣ 启动服务："
    echo "   # 先启动独立语音服务器"
    echo "   cd /path/to/voice-server"
    echo "   java -jar standalone-voice-server-1.0.0.jar voice-server.yml &"
    echo "   echo \$! > voice-server.pid"
    echo ""
    echo "   # 等待几秒钟"
    echo "   sleep 5"
    echo ""
    echo "   # 启动Paper服务器"
    echo "   systemctl start paper-server"
    echo ""
    echo "6️⃣ 验证部署："
    echo "   # 检查独立语音服务器"
    echo "   curl http://localhost:8080/health"
    echo ""
    echo "   # 检查Paper服务器日志"
    echo "   tail -f /path/to/paper/logs/latest.log"
    echo ""
    echo "   # 应该看到："
    echo "   # [INFO] [VoiceChatAdapter] Voice server client initialized"
    echo "   # [INFO] [VoiceChatAdapter] Secret packet sender initialized"
    echo "   # [INFO] [VoiceChatAdapter] Group message handler initialized"
}

# 测试指南
show_testing_guide() {
    log_step "功能测试指南"
    
    echo ""
    echo "🧪 完整功能测试："
    echo ""
    echo "1️⃣ 基础连接测试："
    echo "   - 玩家加入服务器"
    echo "   - 检查是否收到语音聊天密钥"
    echo "   - 验证语音聊天界面可以打开"
    echo ""
    echo "2️⃣ 语音功能测试："
    echo "   - 测试麦克风输入"
    echo "   - 测试语音输出"
    echo "   - 测试位置音频（距离衰减）"
    echo ""
    echo "3️⃣ 群组功能测试："
    echo "   - 创建新群组（普通/开放/隔离类型）"
    echo "   - 设置群组密码"
    echo "   - 邀请其他玩家加入"
    echo "   - 测试群组内语音聊天"
    echo "   - 离开群组"
    echo ""
    echo "4️⃣ 预期结果："
    echo "   ✅ 不再有 'Unknown packet ID' 错误"
    echo "   ✅ 不再有 '404 Not Found' 错误"
    echo "   ✅ 群组创建和管理功能正常"
    echo "   ✅ 所有语音聊天功能可用"
    echo ""
    echo "5️⃣ 故障排除："
    echo "   如果仍有问题，检查："
    echo "   - 独立语音服务器日志"
    echo "   - Paper服务器日志"
    echo "   - 网络连接和防火墙"
    echo "   - 配置文件中的认证令牌"
}

# 主函数
main() {
    echo "群组功能完整修复部署"
    echo "===================="
    
    show_complete_fix
    show_deployment_guide
    show_testing_guide
    
    echo ""
    log_info "🎉 群组功能修复完成！现在可以部署并享受完整的语音聊天功能了！"
    echo ""
    echo "📞 技术支持："
    echo "- 如果遇到问题，请检查服务器日志"
    echo "- 确保所有配置文件正确"
    echo "- 验证网络连接和端口开放"
    echo ""
    echo "🎮 享受语音聊天吧！"
}

# 执行主函数
main
