package de.maxhenkel.voicechat.standalone.network.packets;

import de.maxhenkel.voicechat.standalone.network.Packet;
import io.netty.buffer.ByteBuf;

import java.util.UUID;

/**
 * 群组音频数据包（与原项目GroupSoundPacket保持一致）
 */
public class GroupSoundPacket extends Packet<GroupSoundPacket> {
    
    public static final byte PACKET_ID = (byte) 0x3;
    
    private UUID channelId;
    private UUID sender;
    private byte[] data;
    private long sequenceNumber;
    private String category;
    
    public GroupSoundPacket() {}
    
    public GroupSoundPacket(UUID channelId, UUID sender, byte[] data, long sequenceNumber, String category) {
        this.channelId = channelId;
        this.sender = sender;
        this.data = data;
        this.sequenceNumber = sequenceNumber;
        this.category = category;
    }
    
    @Override
    public void toBytes(ByteBuf buf) {
        // 写入UUID
        buf.writeLong(channelId.getMostSignificantBits());
        buf.writeLong(channelId.getLeastSignificantBits());
        buf.writeLong(sender.getMostSignificantBits());
        buf.writeLong(sender.getLeastSignificantBits());
        
        // 写入音频数据
        writeByteArray(buf, data);
        
        // 写入序列号
        buf.writeLong(sequenceNumber);
        
        // 写入标志位和类别
        byte flags = 0;
        if (category != null) {
            flags |= 0x2; // HAS_CATEGORY_MASK
        }
        buf.writeByte(flags);
        
        if (category != null) {
            writeString(buf, category);
        }
    }
    
    @Override
    public void fromBytes(ByteBuf buf) {
        // 按照原项目的顺序读取数据
        // 1. 读取channelId
        long channelMost = buf.readLong();
        long channelLeast = buf.readLong();
        channelId = new UUID(channelMost, channelLeast);
        
        // 2. 读取sender
        long senderMost = buf.readLong();
        long senderLeast = buf.readLong();
        sender = new UUID(senderMost, senderLeast);
        
        // 3. 读取音频数据
        data = readByteArray(buf);
        
        // 4. 读取序列号
        sequenceNumber = buf.readLong();
        
        // 5. 读取标志位
        byte flags = buf.readByte();
        
        // 6. 读取类别（如果存在）
        if ((flags & 0x2) != 0) {
            category = readString(buf);
        }
    }
    
    @Override
    public byte getPacketId() {
        return PACKET_ID;
    }
    
    @Override
    public Class<GroupSoundPacket> getPacketClass() {
        return GroupSoundPacket.class;
    }
    
    // 工具方法
    private static void writeByteArray(ByteBuf buf, byte[] data) {
        writeVarInt(buf, data.length);
        buf.writeBytes(data);
    }
    
    private static byte[] readByteArray(ByteBuf buf) {
        int length = readVarInt(buf);
        byte[] data = new byte[length];
        buf.readBytes(data);
        return data;
    }
    
    private static void writeString(ByteBuf buf, String str) {
        byte[] bytes = str.getBytes(java.nio.charset.StandardCharsets.UTF_8);
        writeVarInt(buf, bytes.length);
        buf.writeBytes(bytes);
    }
    
    private static String readString(ByteBuf buf) {
        int length = readVarInt(buf);
        byte[] bytes = new byte[length];
        buf.readBytes(bytes);
        return new String(bytes, java.nio.charset.StandardCharsets.UTF_8);
    }
    
    private static void writeVarInt(ByteBuf buf, int value) {
        while ((value & -128) != 0) {
            buf.writeByte(value & 127 | 128);
            value >>>= 7;
        }
        buf.writeByte(value);
    }
    
    private static int readVarInt(ByteBuf buf) {
        int i = 0;
        int j = 0;

        byte b;
        do {
            b = buf.readByte();
            i |= (b & 127) << j++ * 7;
            if (j > 5) {
                throw new RuntimeException("VarInt too big");
            }
        } while ((b & 128) == 128);

        return i;
    }
    
    // Getters
    public UUID getChannelId() { return channelId; }
    public UUID getSender() { return sender; }
    public byte[] getData() { return data; }
    public long getSequenceNumber() { return sequenceNumber; }
    public String getCategory() { return category; }
}
