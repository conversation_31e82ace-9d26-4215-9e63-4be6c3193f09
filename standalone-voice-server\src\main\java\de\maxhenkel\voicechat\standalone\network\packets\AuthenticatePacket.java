package de.maxhenkel.voicechat.standalone.network.packets;

import de.maxhenkel.voicechat.standalone.network.Packet;
import io.netty.buffer.ByteBuf;

import java.util.UUID;

/**
 * 客户端认证数据包
 */
public class AuthenticatePacket extends Packet<AuthenticatePacket> {
    
    public static final byte PACKET_ID = 5;
    
    private UUID playerUuid;
    private UUID secret;
    
    public AuthenticatePacket() {}
    
    public AuthenticatePacket(UUID playerUuid, UUID secret) {
        this.playerUuid = playerUuid;
        this.secret = secret;
    }
    
    @Override
    public void toBytes(ByteBuf buf) {
        buf.writeLong(playerUuid.getMostSignificantBits());
        buf.writeLong(playerUuid.getLeastSignificantBits());
        buf.writeLong(secret.getMostSignificantBits());
        buf.writeLong(secret.getLeastSignificantBits());
    }
    
    @Override
    public void fromBytes(ByteBuf buf) {
        long playerMostSig = buf.readLong();
        long playerLeastSig = buf.readLong();
        playerUuid = new UUID(playerMostSig, playerLeastSig);
        
        long secretMostSig = buf.readLong();
        long secretLeastSig = buf.readLong();
        secret = new UUID(secretMostSig, secretLeastSig);
    }
    
    @Override
    public byte getPacketId() {
        return PACKET_ID;
    }
    
    @Override
    public Class<AuthenticatePacket> getPacketClass() {
        return AuthenticatePacket.class;
    }
    
    // Getters
    public UUID getPlayerUuid() { return playerUuid; }
    public UUID getSecret() { return secret; }
}
